﻿#pragma once
#include <string>
#include <vector>
#include <map>
#include <chrono>
#include <windows.h>
#include <nlohmann/json.hpp>

// EXIF基础数据结构
struct ExifBasicInfo {
    std::string file_name;              // 文件名
    std::string file_path;              // 文件路径
    std::string file_size;              // 文件大小
    std::string make;                   // 制造商
    std::string model;                  // 型号
    std::string software;               // 软件
    std::string date_time;              // 修改时间
    std::string date_time_original;     // 拍摄时间
    std::string date_time_digitized;    // 数字化时间
    std::string artist;                 // 艺术家/作者
    std::string copyright;              // 版权信息
    std::string image_description;      // 图像描述
    std::string user_comment;           // 用户注释

    ExifBasicInfo() {}
};

// EXIF拍摄参数
struct ExifCameraSettings {
    std::string exposure_time;          // 曝光时间
    std::string f_number;               // 光圈值
    std::string exposure_program;       // 曝光程序
    std::string iso_speed;              // ISO感光度
    std::string shutter_speed;          // 快门速度
    std::string aperture;               // 光圈
    std::string brightness;             // 亮度
    std::string exposure_bias;          // 曝光补偿
    std::string max_aperture;           // 最大光圈
    std::string metering_mode;          // 测光模式
    std::string light_source;           // 光源
    std::string flash;                  // 闪光灯
    std::string focal_length;           // 焦距
    std::string focal_length_35mm;      // 35mm等效焦距
    std::string scene_capture_type;     // 场景拍摄类型
    std::string white_balance;          // 白平衡
    std::string digital_zoom_ratio;     // 数字变焦比
    std::string contrast;               // 对比度
    std::string saturation;             // 饱和度
    std::string sharpness;              // 锐度

    ExifCameraSettings() {}
};

// EXIF图像信息
struct ExifImageInfo {
    int image_width;                    // 图像宽度
    int image_height;                   // 图像高度
    int orientation;                    // 方向
    int x_resolution;                   // X分辨率
    int y_resolution;                   // Y分辨率
    std::string resolution_unit;        // 分辨率单位
    std::string color_space;            // 色彩空间
    int bits_per_sample;                // 每样本位数
    int samples_per_pixel;              // 每像素样本数
    std::string compression;            // 压缩方式
    std::string photometric_interpretation; // 光度解释

    ExifImageInfo() {
        image_width = 0;
        image_height = 0;
        orientation = 0;
        x_resolution = 0;
        y_resolution = 0;
        bits_per_sample = 0;
        samples_per_pixel = 0;
    }
};

// EXIF GPS信息
struct ExifGpsInfo {
    std::string gps_latitude;           // GPS纬度
    std::string gps_latitude_ref;       // GPS纬度参考
    std::string gps_longitude;          // GPS经度
    std::string gps_longitude_ref;      // GPS经度参考
    std::string gps_altitude;           // GPS高度
    std::string gps_altitude_ref;       // GPS高度参考
    std::string gps_timestamp;          // GPS时间戳
    std::string gps_datestamp;          // GPS日期戳
    std::string gps_processing_method;  // GPS处理方法
    std::string gps_area_information;   // GPS区域信息
    std::string gps_map_datum;          // GPS地图基准
    std::string gps_speed;              // GPS速度
    std::string gps_speed_ref;          // GPS速度参考
    std::string gps_track;              // GPS轨迹
    std::string gps_track_ref;          // GPS轨迹参考
    std::string gps_img_direction;      // GPS图像方向
    std::string gps_img_direction_ref;  // GPS图像方向参考

    ExifGpsInfo() {}
};

// EXIF镜头信息
struct ExifLensInfo {
    std::string lens_make;              // 镜头制造商
    std::string lens_model;             // 镜头型号
    std::string lens_serial_number;     // 镜头序列号
    std::string lens_specification;     // 镜头规格
    std::string focal_length_in_35mm;   // 35mm等效焦距
    std::string max_aperture_value;     // 最大光圈值
    std::string min_focal_length;       // 最小焦距
    std::string max_focal_length;       // 最大焦距

    ExifLensInfo() {}
};

// 设备类型识别结果
struct DeviceTypeInfo {
    std::string device_type;            // 设备类型 (手机/相机/电脑/未知)
    std::string device_category;        // 设备分类 (智能手机/数码相机/单反相机/无反相机等)
    std::string confidence_level;       // 可信度 (高/中/低)
    std::string identification_reason;  // 识别依据
    std::string brand_info;             // 品牌信息
    std::string device_description;     // 设备描述

    DeviceTypeInfo() {}
};

// 完整的EXIF数据结构
struct ExifMetadata {
    std::string file_path;              // 文件路径
    std::string file_name;              // 文件名
    std::string file_size;              // 文件大小
    std::string file_format;            // 文件格式
    std::string scan_time;              // 扫描时间
    bool has_exif_data;                 // 是否包含EXIF数据
    std::string error_message;          // 错误信息

    ExifBasicInfo basic_info;           // 基础信息
    ExifCameraSettings camera_settings; // 拍摄参数
    ExifImageInfo image_info;           // 图像信息
    ExifGpsInfo gps_info;               // GPS信息
    ExifLensInfo lens_info;             // 镜头信息
    DeviceTypeInfo device_type;         // 设备类型识别

    std::map<std::string, std::string> raw_exif_data; // 原始EXIF数据

    ExifMetadata() {
        has_exif_data = false;
    }
};

// EXIF扫描统计信息
struct ExifScanStatistics {
    int total_files_scanned;            // 总扫描文件数
    int files_with_exif;                // 包含EXIF的文件数
    int files_without_exif;             // 不包含EXIF的文件数
    int phone_photos;                   // 手机照片数
    int camera_photos;                  // 相机照片数
    int processed_images;               // 处理过的图片数
    int unknown_device_photos;          // 未知设备照片数
    std::string scan_duration;          // 扫描耗时
    std::string scan_time;              // 扫描时间
    std::chrono::system_clock::time_point scan_start_time;  // 扫描开始时间
    std::chrono::system_clock::time_point scan_end_time;    // 扫描结束时间

    ExifScanStatistics() {
        total_files_scanned = 0;
        files_with_exif = 0;
        files_without_exif = 0;
        phone_photos = 0;
        camera_photos = 0;
        processed_images = 0;
        unknown_device_photos = 0;
    }
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExifBasicInfo,
    make, model, software, date_time, date_time_original, date_time_digitized,
    artist, copyright, image_description, user_comment)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExifCameraSettings,
    exposure_time, f_number, exposure_program, iso_speed, shutter_speed,
    aperture, brightness, exposure_bias, max_aperture, metering_mode,
    light_source, flash, focal_length, focal_length_35mm, scene_capture_type,
    white_balance, digital_zoom_ratio, contrast, saturation, sharpness)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExifImageInfo,
    image_width, image_height, orientation, x_resolution, y_resolution,
    resolution_unit, color_space, bits_per_sample, samples_per_pixel,
    compression, photometric_interpretation)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExifGpsInfo,
    gps_latitude, gps_latitude_ref, gps_longitude, gps_longitude_ref,
    gps_altitude, gps_altitude_ref, gps_timestamp, gps_datestamp,
    gps_processing_method, gps_area_information, gps_map_datum,
    gps_speed, gps_speed_ref, gps_track, gps_track_ref,
    gps_img_direction, gps_img_direction_ref)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExifLensInfo,
    lens_make, lens_model, lens_serial_number, lens_specification,
    focal_length_in_35mm, max_aperture_value, min_focal_length, max_focal_length)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(DeviceTypeInfo,
    device_type, device_category, confidence_level, identification_reason,
    brand_info, device_description)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExifMetadata,
    file_path, file_name, file_size, file_format, scan_time, has_exif_data, error_message,
    basic_info, camera_settings, image_info, gps_info, lens_info, device_type, raw_exif_data)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExifScanStatistics,
    total_files_scanned, files_with_exif, files_without_exif,
    phone_photos, camera_photos, processed_images, unknown_device_photos,
    scan_duration, scan_time)

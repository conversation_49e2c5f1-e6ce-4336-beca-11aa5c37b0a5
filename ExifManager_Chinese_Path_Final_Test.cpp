#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <windows.h>

// 测试中文路径的直接访问
void testDirectChinesePathAccess() {
    std::cout << "=== Testing Direct Chinese Path Access ===" << std::endl;
    
    // 测试Screenshots目录
    std::string screenshotsPath = "C:\\Users\\<USER>\\Pictures\\Screenshots";
    
    // 转换为宽字符
    int widePathLength = MultiByteToWideChar(CP_ACP, 0, screenshotsPath.c_str(), -1, NULL, 0);
    if (widePathLength > 0) {
        std::vector<wchar_t> widePath(widePathLength);
        MultiByteToWideChar(CP_ACP, 0, screenshotsPath.c_str(), -1, &widePath[0], widePathLength);
        
        WIN32_FIND_DATAW findData;
        std::wstring searchPattern = std::wstring(&widePath[0]) + L"\\*.png";
        HANDLE hFind = FindFirstFileW(searchPattern.c_str(), &findData);
        
        if (hFind != INVALID_HANDLE_VALUE) {
            int fileCount = 0;
            do {
                std::wstring wFileName = findData.cFileName;
                std::wstring wideFullPath = std::wstring(&widePath[0]) + L"\\" + wFileName;
                
                // 转换为ANSI路径（与扫描时一致）
                int ansiPathLength = WideCharToMultiByte(CP_ACP, 0, wideFullPath.c_str(), -1, NULL, 0, NULL, NULL);
                if (ansiPathLength > 0) {
                    std::vector<char> ansiPathBuffer(ansiPathLength);
                    WideCharToMultiByte(CP_ACP, 0, wideFullPath.c_str(), -1, &ansiPathBuffer[0], ansiPathLength, NULL, NULL);
                    std::string ansiPath(&ansiPathBuffer[0]);
                    
                    fileCount++;
                    std::cout << "\nFile " << fileCount << ":" << std::endl;
                    std::cout << "  ANSI path: " << ansiPath << std::endl;
                    
                    // 测试文件访问
                    DWORD attributes = GetFileAttributesW(wideFullPath.c_str());
                    if (attributes != INVALID_FILE_ATTRIBUTES) {
                        std::cout << "  ✓ File accessible via wide API" << std::endl;
                        
                        // 测试ANSI路径转回宽字符是否一致
                        int testWideLength = MultiByteToWideChar(CP_ACP, 0, ansiPath.c_str(), -1, NULL, 0);
                        if (testWideLength > 0) {
                            std::vector<wchar_t> testWidePath(testWideLength);
                            MultiByteToWideChar(CP_ACP, 0, ansiPath.c_str(), -1, &testWidePath[0], testWideLength);
                            
                            DWORD testAttributes = GetFileAttributesW(&testWidePath[0]);
                            if (testAttributes != INVALID_FILE_ATTRIBUTES) {
                                std::cout << "  ✓ Round-trip conversion successful" << std::endl;
                            } else {
                                std::cout << "  ✗ Round-trip conversion failed" << std::endl;
                            }
                        }
                    } else {
                        std::cout << "  ✗ File not accessible" << std::endl;
                    }
                    
                    if (fileCount >= 3) break; // 只测试前3个文件
                }
                
            } while (FindNextFileW(hFind, &findData));
            
            FindClose(hFind);
            std::cout << "\nTotal PNG files found: " << fileCount << std::endl;
        } else {
            std::cout << "No PNG files found or directory not accessible" << std::endl;
        }
    }
}

// 简单的进度回调函数
void chinesePathFinalProgressCallback(const std::string& message, int progress) {
    std::cout << "[Chinese Path Final Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool chinesePathFinalQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

int main() {
    std::cout << "=== EXIF Manager Chinese Path Final Test ===" << std::endl;
    
    // 首先测试直接路径访问
    testDirectChinesePathAccess();
    
    std::string taskId = "chinese_path_final_test_001";
    
    std::cout << "\n=== Starting EXIF Analysis with Fixed Chinese Path Support ===" << std::endl;
    std::cout << "This test will verify that Chinese filenames are handled correctly..." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            chinesePathFinalProgressCallback,
            taskId,
            chinesePathFinalQueryTaskControl
        );
        
        std::cout << "\n=== Chinese Path Final Test Results ===" << std::endl;
        
        // 解析结果查找路径问题
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            if (jsonResult.find("statistics") != jsonResult.end()) {
                auto stats = jsonResult["statistics"];
                if (stats.find("successful_extractions") != stats.end()) {
                    std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                }
            }
            
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                int totalFiles = imageAnalysis.size();
                int failedFiles = 0;
                int pathIssues = 0;
                int chinesePathFiles = 0;
                
                std::cout << "\n=== Path Analysis ===" << std::endl;
                for (const auto& image : imageAnalysis) {
                    if (image.find("file_path") != image.end()) {
                        std::string filePath = image["file_path"];
                        
                        // 检查是否包含中文路径标识
                        if (filePath.find("Screenshots") != std::string::npos) {
                            chinesePathFiles++;
                            
                            bool success = false;
                            if (image.find("success") != image.end()) {
                                success = image["success"].get<bool>();
                            }
                            
                            if (!success) {
                                failedFiles++;
                                if (filePath.find("????????") != std::string::npos) {
                                    pathIssues++;
                                    std::cout << "Path issue: " << filePath << std::endl;
                                } else {
                                    std::cout << "Other failure: " << filePath << std::endl;
                                    if (image.find("error") != image.end()) {
                                        std::cout << "  Error: " << image["error"] << std::endl;
                                    }
                                }
                            } else {
                                std::cout << "✓ Success: " << filePath.substr(filePath.find_last_of("\\") + 1) << std::endl;
                            }
                        }
                    }
                }
                
                std::cout << "\n=== Summary ===" << std::endl;
                std::cout << "Total files analyzed: " << totalFiles << std::endl;
                std::cout << "Chinese path files: " << chinesePathFiles << std::endl;
                std::cout << "Failed files: " << failedFiles << std::endl;
                std::cout << "Path encoding issues: " << pathIssues << std::endl;
                
                if (pathIssues == 0) {
                    std::cout << "✓ No path encoding issues found!" << std::endl;
                } else {
                    std::cout << "✗ Found " << pathIssues << " files with path encoding issues" << std::endl;
                }
            }
            
            // 检查文件保存
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "\n✓ Results saved to: " << outputFile << std::endl;
                } else {
                    std::cout << "\n⚠ Failed to save results to: " << outputFile << std::endl;
                }
            }
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

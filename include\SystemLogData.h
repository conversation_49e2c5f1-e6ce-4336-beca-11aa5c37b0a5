﻿#pragma once

#include <string>
#include <vector>
#include <map>
#include <windows.h>
#include <nlohmann/json.hpp>
#include <chrono>

// 日志级别枚举
enum class LogLevel {
    Information = 1,
    Warning = 2,
    Error = 3,
    SuccessAudit = 4,
    FailureAudit = 5,
    Critical = 6
};

// 日志类型枚举
enum class LogType {
    System,         // 系统日志
    Security,       // 安全日志
    Application,    // 应用程序日志
    Setup,          // 安装日志
    ForwardedEvents // 转发事件日志
};

// 单条日志记录结构
struct LogEntry {
    std::string log_name;           // 日志名称 (System/Application/Security)
    std::string source;             // 来源 (事件源)
    std::string event_id;           // 事件ID
    std::string level;              // 级别 (日志级别)
    std::string user_name;          // 用户
    std::string time_generated;     // 记录时间
    std::string category;           // 任务类别
    std::string computer_name;      // 计算机

    // 保留的附加字段
    std::string time_written;       // 写入时间
    std::string description;        // 事件描述
    std::string data;              // 附加数据
    std::map<std::string, std::string> details; // 详细信息

    LogEntry() {}

    // 转换为JSON - 只包含可以获取真实数据的8个字段
    nlohmann::json toJson() const {
        nlohmann::json j;
        j["log_name"] = log_name;         // 日志名称
        j["source"] = source;             // 来源
        j["event_id"] = event_id;         // 事件ID
        j["level"] = level;               // 级别
        j["user"] = user_name;            // 用户
        j["record_time"] = time_generated; // 记录时间
        j["task_category"] = category;     // 任务类别
        j["computer"] = computer_name;     // 计算机
        return j;
    }
};

// 登录日志特定结构
struct LoginLogEntry {
    std::string event_id;           // 事件ID (4624=成功登录, 4625=登录失败, 4634=注销)
    std::string login_type;         // 登录类型
    std::string time_stamp;         // 时间戳
    std::string user_name;          // 用户名
    std::string domain;             // 域名
    std::string computer_name;      // 计算机名
    std::string source_ip;          // 源IP地址
    std::string logon_process;      // 登录进程
    std::string authentication_package; // 认证包
    std::string status;             // 状态 (成功/失败)
    std::string failure_reason;     // 失败原因
    std::string session_id;         // 会话ID

    LoginLogEntry() {}
    
    nlohmann::json toJson() const {
        nlohmann::json j;
        // 统一字段格式：只包含可获取真实数据的8个字段
        j["log_name"] = "Security";                    // 日志名称
        j["source"] = "Microsoft-Windows-Security";   // 来源
        j["event_id"] = event_id;                      // 事件ID
        j["level"] = (status == "成功" || status == "Success") ? "Information" : "Warning"; // 级别
        j["user"] = user_name;                         // 用户
        j["record_time"] = time_stamp;                 // 记录时间
        j["task_category"] = "Logon/Logoff";          // 任务类别
        j["computer"] = computer_name;                 // 计算机
        return j;
    }
};

// 开关机日志结构
struct PowerLogEntry {
    std::string event_id;           // 事件ID (6005=启动, 6006=关机, 6008=意外关机)
    std::string action;             // 动作类型 (启动/关机/重启/休眠/唤醒)
    std::string time_stamp;         // 时间戳
    std::string computer_name;      // 计算机名
    std::string reason_code;        // 原因代码
    std::string reason_text;        // 原因描述
    std::string user_name;          // 操作用户
    std::string process_name;       // 相关进程
    std::string shutdown_type;      // 关机类型
    std::string duration;           // 持续时间

    PowerLogEntry() {}
    
    nlohmann::json toJson() const {
        nlohmann::json j;
        // 统一字段格式：只包含可获取真实数据的8个字段
        j["log_name"] = "System";                      // 日志名称
        j["source"] = "EventLog";                      // 来源
        j["event_id"] = event_id;                      // 事件ID
        j["level"] = "Information";                    // 级别
        j["user"] = user_name.empty() ? "N/A" : user_name; // 用户
        j["record_time"] = time_stamp;                 // 记录时间
        j["task_category"] = "System Event";          // 任务类别
        j["computer"] = computer_name;                 // 计算机
        return j;
    }
};

// 日志统计信息
struct LogStatistics {
    int total_entries;              // 总条目数
    int information_count;          // 信息级别数量
    int warning_count;              // 警告级别数量
    int error_count;                // 错误级别数量
    int critical_count;             // 严重级别数量
    int success_audit_count;        // 成功审核数量
    int failure_audit_count;        // 失败审核数量
    std::string oldest_entry;       // 最早条目时间
    std::string newest_entry;       // 最新条目时间
    std::string scan_duration;      // 扫描耗时
    std::string scan_time;          // 扫描时间

    LogStatistics() : total_entries(0), information_count(0), warning_count(0), 
                     error_count(0), critical_count(0), success_audit_count(0), 
                     failure_audit_count(0) {}
    
    nlohmann::json toJson() const {
        nlohmann::json j;
        j["total_entries"] = total_entries;
        j["information_count"] = information_count;
        j["warning_count"] = warning_count;
        j["error_count"] = error_count;
        j["critical_count"] = critical_count;
        j["success_audit_count"] = success_audit_count;
        j["failure_audit_count"] = failure_audit_count;
        j["oldest_entry"] = oldest_entry;
        j["newest_entry"] = newest_entry;
        j["scan_duration"] = scan_duration;
        j["scan_time"] = scan_time;
        return j;
    }
};

// 日志查询参数
struct LogQueryParams {
    LogType log_type;               // 日志类型
    int max_entries;                // 最大条目数 (0表示不限制)
    std::string start_time;         // 开始时间 (格式: YYYY-MM-DD HH:MM:SS)
    std::string end_time;           // 结束时间
    std::vector<std::string> event_ids; // 特定事件ID列表
    std::vector<std::string> sources;   // 事件源列表
    LogLevel min_level;             // 最小日志级别
    std::string computer_name;      // 计算机名过滤
    std::string user_name;          // 用户名过滤
    bool include_details;           // 是否包含详细信息

    LogQueryParams() : log_type(LogType::System), max_entries(1000), 
                      min_level(LogLevel::Information), include_details(true) {}
};

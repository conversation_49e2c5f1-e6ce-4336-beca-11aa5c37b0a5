#include "ExifExtractor.h"
#include <comdef.h>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <chrono>

ExifExtractor::ExifExtractor() : m_initialized(false)
{
}

ExifExtractor::~ExifExtractor()
{
    Cleanup();
}

bool ExifExtractor::Initialize()
{
    if (m_initialized)
        return true;
        
    // 初始化COM组件
    HRESULT hr = CoInitialize(NULL);
    if (FAILED(hr))
    {
        SetError("初始化COM组件失败");
        return false;
    }
    
    m_initialized = true;
    return true;
}

void ExifExtractor::Cleanup()
{
    if (m_initialized)
    {
        CoUninitialize();
        m_initialized = false;
    }
}

bool ExifExtractor::ExtractExifInfo(const std::string& filePath, ExifInfo& exifInfo)
{
    if (!m_initialized)
    {
        SetError("ExifExtractor未初始化");
        return false;
    }
    
    exifInfo.Clear();
    
    // 将文件路径转换为宽字符串
    int wideLen = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, NULL, 0);
    if (wideLen == 0)
    {
        SetError("文件路径转换失败");
        return false;
    }
    
    std::vector<wchar_t> widePath(wideLen);
    MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, &widePath[0], wideLen);
    
    // 创建属性存储对象
    IPropertyStore* pPropertyStore = NULL;
    HRESULT hr = SHGetPropertyStoreFromParsingName(&widePath[0], NULL, GPS_DEFAULT, 
                                                   IID_PPV_ARGS(&pPropertyStore));
    if (FAILED(hr))
    {
        SetError("无法打开文件属性存储");
        return false;
    }
    
    PROPVARIANT pv;
    PropVariantInit(&pv);
    
    // 提取照相机制造商
    hr = pPropertyStore->GetValue(PKEY_Photo_CameraManufacturer, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.manufacturer = GetStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);
    
    // 提取照相机型号
    hr = pPropertyStore->GetValue(PKEY_Photo_CameraModel, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.model = GetStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);
    
    // 提取拍摄日期时间
    hr = pPropertyStore->GetValue(PKEY_Photo_DateTaken, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.dateTime = GetStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取图像方向
    hr = pPropertyStore->GetValue(PKEY_Photo_Orientation, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.orientation = GetNumberStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取曝光时间
    hr = pPropertyStore->GetValue(PKEY_Photo_ExposureTime, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.exposureTime = GetRationalStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取光圈值
    hr = pPropertyStore->GetValue(PKEY_Photo_FNumber, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.fNumber = GetRationalStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取ISO感光度
    hr = pPropertyStore->GetValue(PKEY_Photo_ISOSpeed, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.isoSpeed = GetNumberStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取焦距
    hr = pPropertyStore->GetValue(PKEY_Photo_FocalLength, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.focalLength = GetRationalStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取闪光灯信息
    hr = pPropertyStore->GetValue(PKEY_Photo_Flash, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.flash = GetNumberStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取图像宽度
    hr = pPropertyStore->GetValue(PKEY_Image_HorizontalSize, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.width = GetNumberStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取图像高度
    hr = pPropertyStore->GetValue(PKEY_Image_VerticalSize, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.height = GetNumberStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS纬度
    hr = pPropertyStore->GetValue(PKEY_GPS_Latitude, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsLatitude = ConvertGpsCoordinate(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS纬度参考(N/S)
    hr = pPropertyStore->GetValue(PKEY_GPS_LatitudeRef, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsLatitudeRef = GetStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS经度
    hr = pPropertyStore->GetValue(PKEY_GPS_Longitude, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsLongitude = ConvertGpsCoordinate(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS经度参考(E/W)
    hr = pPropertyStore->GetValue(PKEY_GPS_LongitudeRef, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsLongitudeRef = GetStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS海拔高度
    hr = pPropertyStore->GetValue(PKEY_GPS_Altitude, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsAltitude = ConvertGpsAltitude(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS海拔参考
    hr = pPropertyStore->GetValue(PKEY_GPS_AltitudeRef, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsAltitudeRef = GetNumberStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS时间戳
    hr = pPropertyStore->GetValue(PKEY_GPS_Date, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsDateTime = GetStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    // 提取GPS地图基准
    hr = pPropertyStore->GetValue(PKEY_GPS_MapDatum, &pv);
    if (SUCCEEDED(hr))
    {
        exifInfo.gpsMapDatum = GetStringFromPropVariant(pv);
    }
    PropVariantClear(&pv);

    pPropertyStore->Release();
    return true;
}

std::string ExifExtractor::GetStringFromPropVariant(const PROPVARIANT& pv)
{
    if (pv.vt == VT_LPWSTR && pv.pwszVal != NULL)
    {
        return WideStringToString(pv.pwszVal);
    }
    else if (pv.vt == VT_BSTR && pv.bstrVal != NULL)
    {
        return WideStringToString(pv.bstrVal);
    }
    else if (pv.vt == VT_FILETIME)
    {
        SYSTEMTIME st;
        FileTimeToSystemTime(&pv.filetime, &st);

        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(4) << st.wYear << "-"
            << std::setw(2) << st.wMonth << "-"
            << std::setw(2) << st.wDay << " "
            << std::setw(2) << st.wHour << ":"
            << std::setw(2) << st.wMinute << ":"
            << std::setw(2) << st.wSecond;
        return oss.str();
    }
    return "";
}

std::string ExifExtractor::GetNumberStringFromPropVariant(const PROPVARIANT& pv)
{
    std::ostringstream oss;

    switch (pv.vt)
    {
    case VT_UI1:
        oss << static_cast<int>(pv.bVal);
        break;
    case VT_UI2:
        oss << pv.uiVal;
        break;
    case VT_UI4:
        oss << pv.ulVal;
        break;
    case VT_I2:
        oss << pv.iVal;
        break;
    case VT_I4:
        oss << pv.lVal;
        break;
    case VT_R4:
        oss << pv.fltVal;
        break;
    case VT_R8:
        oss << pv.dblVal;
        break;
    default:
        return "";
    }

    return oss.str();
}

std::string ExifExtractor::GetRationalStringFromPropVariant(const PROPVARIANT& pv)
{
    if (pv.vt == VT_UI8)
    {
        // 分数格式：高32位为分子，低32位为分母
        ULARGE_INTEGER ulValue;
        ulValue.QuadPart = pv.uhVal.QuadPart;
        DWORD numerator = static_cast<DWORD>(ulValue.HighPart);
        DWORD denominator = static_cast<DWORD>(ulValue.LowPart);

        if (denominator == 0)
            return "";

        std::ostringstream oss;
        if (denominator == 1)
        {
            oss << numerator;
        }
        else
        {
            double value = static_cast<double>(numerator) / denominator;
            oss << std::fixed << std::setprecision(2) << value;
        }
        return oss.str();
    }
    else
    {
        return GetNumberStringFromPropVariant(pv);
    }
}

void ExifExtractor::SetError(const std::string& error)
{
    m_lastError = error;
}

std::string ExifExtractor::WideStringToString(const std::wstring& wstr)
{
    if (wstr.empty())
        return "";

    int len = WideCharToMultiByte(CP_ACP, 0, wstr.c_str(), -1, NULL, 0, NULL, NULL);
    if (len == 0)
        return "";

    std::vector<char> buffer(len);
    WideCharToMultiByte(CP_ACP, 0, wstr.c_str(), -1, &buffer[0], len, NULL, NULL);

    return std::string(&buffer[0]);
}

// GPS坐标转换函数
std::string ExifExtractor::ConvertGpsCoordinate(const PROPVARIANT& pv)
{
    if (pv.vt != (VT_VECTOR | VT_UI8) || pv.cauh.cElems != 3)
        return "";

    // GPS坐标以度分秒格式存储，每个值都是分数
    double degrees = 0.0, minutes = 0.0, seconds = 0.0;

    // 提取度
    if (pv.cauh.pElems[0].QuadPart != 0)
    {
        DWORD numerator = static_cast<DWORD>(pv.cauh.pElems[0].HighPart);
        DWORD denominator = static_cast<DWORD>(pv.cauh.pElems[0].LowPart);
        if (denominator != 0)
            degrees = static_cast<double>(numerator) / denominator;
    }

    // 提取分
    if (pv.cauh.pElems[1].QuadPart != 0)
    {
        DWORD numerator = static_cast<DWORD>(pv.cauh.pElems[1].HighPart);
        DWORD denominator = static_cast<DWORD>(pv.cauh.pElems[1].LowPart);
        if (denominator != 0)
            minutes = static_cast<double>(numerator) / denominator;
    }

    // 提取秒
    if (pv.cauh.pElems[2].QuadPart != 0)
    {
        DWORD numerator = static_cast<DWORD>(pv.cauh.pElems[2].HighPart);
        DWORD denominator = static_cast<DWORD>(pv.cauh.pElems[2].LowPart);
        if (denominator != 0)
            seconds = static_cast<double>(numerator) / denominator;
    }

    return FormatGpsCoordinate(degrees, minutes, seconds);
}

// GPS海拔高度转换函数
std::string ExifExtractor::ConvertGpsAltitude(const PROPVARIANT& pv)
{
    if (pv.vt == VT_UI8)
    {
        ULARGE_INTEGER ulValue;
        ulValue.QuadPart = pv.uhVal.QuadPart;
        DWORD numerator = static_cast<DWORD>(ulValue.HighPart);
        DWORD denominator = static_cast<DWORD>(ulValue.LowPart);

        if (denominator == 0)
            return "";

        double altitude = static_cast<double>(numerator) / denominator;

        std::ostringstream oss;
        oss << std::fixed << std::setprecision(2) << altitude;
        return oss.str();
    }
    else
    {
        return GetNumberStringFromPropVariant(pv);
    }
}

// 格式化GPS坐标显示
std::string ExifExtractor::FormatGpsCoordinate(double degrees, double minutes, double seconds)
{
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(0) << degrees << "°"
        << std::setprecision(0) << minutes << "'"
        << std::setprecision(2) << seconds << "\"";
    return oss.str();
}

// 将度分秒转换为十进制度数
double ExifExtractor::ConvertDmsToDecimal(double degrees, double minutes, double seconds)
{
    return degrees + (minutes / 60.0) + (seconds / 3600.0);
}

// 获取GPS十进制坐标字符串
std::string ExifExtractor::GetGpsDecimalCoordinate(const std::string& dmsCoord, const std::string& ref)
{
    // 这个函数用于将度分秒格式转换为十进制格式
    // 由于我们已经在ConvertGpsCoordinate中处理了转换，这里主要用于格式化显示
    if (dmsCoord.empty())
        return "";

    // 简单返回原始坐标，实际应用中可以进一步解析和转换
    std::string result = dmsCoord;
    if (!ref.empty())
        result += " " + ref;
    return result;
}

// 检查是否包含GPS信息
bool ExifExtractor::HasGpsInfo(const ExifInfo& exifInfo)
{
    return !exifInfo.gpsLatitude.empty() && !exifInfo.gpsLongitude.empty();
}

// 静态接口方法实现
std::string ExifExtractor::Init_ExifExtractorMsg(
    const std::string& params,
    std::function<void(const std::string&, int)> progressCallback,
    const std::string& taskId,
    std::function<bool(const std::string&)> queryTaskControl
) {
    ExifExtractor extractor;

    // 初始化COM组件
    if (!extractor.Initialize()) {
        nlohmann::json result;
        result["status"] = "error";
        result["message"] = u8"初始化EXIF提取器失败";
        result["error"] = extractor.GetLastError();
        return result.dump(4);
    }

    progressCallback(u8"开始EXIF数据提取...", 0);

    // 检查任务是否被取消
    if (queryTaskControl && queryTaskControl(taskId)) {
        nlohmann::json result;
        result["status"] = "cancelled";
        result["message"] = u8"任务已取消";
        return result.dump(4);
    }

    // 解析输入参数（文件路径）
    std::string filePath = params;
    if (filePath.empty()) {
        nlohmann::json result;
        result["status"] = "error";
        result["message"] = u8"未指定文件路径";
        return result.dump(4);
    }

    progressCallback(u8"正在提取EXIF信息...", 30);

    // 提取EXIF信息
    ExifInfo exifInfo;
    bool success = extractor.ExtractExifInfo(filePath, exifInfo);

    progressCallback(u8"正在生成结果...", 80);

    // 构建JSON结果
    nlohmann::json result;

    if (success) {
        result["status"] = "success";
        result["message"] = u8"EXIF信息提取成功";

        // 基本信息
        nlohmann::json exifData;
        exifData["file_path"] = filePath;
        exifData["manufacturer"] = exifInfo.manufacturer;
        exifData["model"] = exifInfo.model;
        exifData["date_time"] = exifInfo.dateTime;
        exifData["orientation"] = exifInfo.orientation;

        // 拍摄参数
        nlohmann::json cameraSettings;
        cameraSettings["exposure_time"] = exifInfo.exposureTime;
        cameraSettings["f_number"] = exifInfo.fNumber;
        cameraSettings["iso_speed"] = exifInfo.isoSpeed;
        cameraSettings["focal_length"] = exifInfo.focalLength;
        cameraSettings["flash"] = exifInfo.flash;
        exifData["camera_settings"] = cameraSettings;

        // 图像信息
        nlohmann::json imageInfo;
        imageInfo["width"] = exifInfo.width;
        imageInfo["height"] = exifInfo.height;
        exifData["image_info"] = imageInfo;

        // GPS信息
        nlohmann::json gpsInfo;
        gpsInfo["latitude"] = exifInfo.gpsLatitude;
        gpsInfo["longitude"] = exifInfo.gpsLongitude;
        gpsInfo["altitude"] = exifInfo.gpsAltitude;
        gpsInfo["latitude_ref"] = exifInfo.gpsLatitudeRef;
        gpsInfo["longitude_ref"] = exifInfo.gpsLongitudeRef;
        gpsInfo["altitude_ref"] = exifInfo.gpsAltitudeRef;
        gpsInfo["date_time"] = exifInfo.gpsDateTime;
        gpsInfo["map_datum"] = exifInfo.gpsMapDatum;
        gpsInfo["has_gps_info"] = HasGpsInfo(exifInfo);
        exifData["gps_info"] = gpsInfo;

        result["exif_data"] = exifData;

        // 提取时间戳
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::tm tm_info;
        localtime_s(&tm_info, &time_t);

        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(4) << (tm_info.tm_year + 1900) << "-"
            << std::setw(2) << (tm_info.tm_mon + 1) << "-"
            << std::setw(2) << tm_info.tm_mday << " "
            << std::setw(2) << tm_info.tm_hour << ":"
            << std::setw(2) << tm_info.tm_min << ":"
            << std::setw(2) << tm_info.tm_sec;
        result["extraction_time"] = oss.str();

    } else {
        result["status"] = "error";
        result["message"] = u8"EXIF信息提取失败";
        result["error"] = extractor.GetLastError();
        result["file_path"] = filePath;
    }

    progressCallback(u8"EXIF信息提取完成", 100);

    return result.dump(4);
}

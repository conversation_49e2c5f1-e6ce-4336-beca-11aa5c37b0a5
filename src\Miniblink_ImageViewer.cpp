#include "../include/Miniblink_ImageViewer.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <filesystem>
#include <algorithm>
#include <iomanip>

// 构造函数
MinibinkImageViewer::MinibinkImageViewer() 
    : m_webView(nullptr)
    , m_hwnd(nullptr)
    , m_initialized(false)
    , m_currentIndex(0) {
}

// 析构函数
MinibinkImageViewer::~MinibinkImageViewer() {
    Cleanup();
}

// 初始化
bool MinibinkImageViewer::Initialize() {
    if (m_initialized) {
        return true;
    }
    
    try {
        // 初始化miniblink
        wkeInitialize();
        
        // 创建WebView
        m_webView = wkeCreateWebView();
        if (!m_webView) {
            std::cerr << "Failed to create WebView" << std::endl;
            return false;
        }
        
        // 设置回调
        wkeOnWindowClosing(m_webView, OnWindowClosingCallback, this);
        wkeOnDocumentReady(m_webView, OnDocumentReadyCallback, this);
        
        m_initialized = true;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Initialize failed: " << e.what() << std::endl;
        return false;
    }
}

// 清理
void MinibinkImageViewer::Cleanup() {
    if (m_webView) {
        wkeDestroyWebView(m_webView);
        m_webView = nullptr;
    }
    
    if (m_initialized) {
        wkeFinalize();
        m_initialized = false;
    }
}

// 创建窗口
bool MinibinkImageViewer::CreateWindow(int width, int height) {
    if (!m_webView) {
        return false;
    }
    
    try {
        // 设置窗口大小
        wkeResize(m_webView, width, height);
        
        // 获取窗口句柄
        m_hwnd = wkeGetWindowHandle(m_webView);
        
        // 设置默认标题
        SetWindowTitle(u8"本地图片查看器 - Miniblink");
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "CreateWindow failed: " << e.what() << std::endl;
        return false;
    }
}

// 显示窗口
void MinibinkImageViewer::ShowWindow(bool show) {
    if (m_webView) {
        wkeShowWindow(m_webView, show);
    }
}

// 设置窗口标题
void MinibinkImageViewer::SetWindowTitle(const std::string& title) {
    if (m_webView) {
        wkeSetWindowTitle(m_webView, title.c_str());
    }
}

// 获取窗口句柄
HWND MinibinkImageViewer::GetWindowHandle() const {
    return m_hwnd;
}

// 加载图片查看器HTML
bool MinibinkImageViewer::LoadImageViewer() {
    if (!LoadHtmlTemplate()) {
        return false;
    }
    
    try {
        // 加载HTML内容
        wkeLoadHTML(m_webView, m_htmlTemplate.c_str());
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "LoadImageViewer failed: " << e.what() << std::endl;
        return false;
    }
}

// 显示图片
bool MinibinkImageViewer::DisplayImage(const std::string& imagePath) {
    if (!IsValidImageFile(imagePath)) {
        return false;
    }
    
    ImageInfo info = GetImageInfo(imagePath);
    return DisplayImageWithInfo(imagePath, info);
}

// 显示图片（带信息）
bool MinibinkImageViewer::DisplayImageWithInfo(const std::string& imagePath, const ImageInfo& info) {
    try {
        // 转换为文件URL
        std::string fileUrl = ConvertToFileUrl(imagePath);
        
        // 构建JavaScript调用
        nlohmann::json imageInfo;
        imageInfo["fileName"] = info.fileName;
        imageInfo["filePath"] = fileUrl;
        imageInfo["fileSize"] = info.fileSize;
        imageInfo["dimensions"] = info.dimensions;
        imageInfo["createTime"] = info.createTime;
        imageInfo["format"] = info.format;
        
        std::string script = "displayLocalImage('" + EscapeJsonString(fileUrl) + "', " + imageInfo.dump() + ");";
        
        return ExecuteScript(script);
        
    } catch (const std::exception& e) {
        std::cerr << "DisplayImageWithInfo failed: " << e.what() << std::endl;
        return false;
    }
}

// 设置图片列表
bool MinibinkImageViewer::SetImageList(const std::vector<ImageInfo>& images) {
    try {
        m_imageList = images;
        m_currentIndex = 0;
        
        // 构建JSON数组
        nlohmann::json imageArray = nlohmann::json::array();
        
        for (const auto& info : images) {
            nlohmann::json imageInfo;
            imageInfo["fileName"] = info.fileName;
            imageInfo["filePath"] = ConvertToFileUrl(info.filePath);
            imageInfo["fileSize"] = info.fileSize;
            imageInfo["dimensions"] = info.dimensions;
            imageInfo["createTime"] = info.createTime;
            imageInfo["format"] = info.format;
            imageInfo["hasExifData"] = info.hasExifData;
            
            imageArray.push_back(imageInfo);
        }
        
        std::string script = "setLocalImageList(" + imageArray.dump() + ");";
        return ExecuteScript(script);
        
    } catch (const std::exception& e) {
        std::cerr << "SetImageList failed: " << e.what() << std::endl;
        return false;
    }
}

// 转换为文件URL
std::string MinibinkImageViewer::ConvertToFileUrl(const std::string& localPath) {
    std::string path = localPath;
    
    // 替换反斜杠为正斜杠
    std::replace(path.begin(), path.end(), '\\', '/');
    
    // 如果不是以file://开头，则添加
    if (path.find("file://") != 0) {
        if (path[0] != '/') {
            path = "/" + path;
        }
        path = "file://" + path;
    }
    
    return path;
}

// 检查是否为有效图片文件
bool MinibinkImageViewer::IsValidImageFile(const std::string& filePath) {
    if (!std::filesystem::exists(filePath)) {
        return false;
    }
    
    std::string extension = std::filesystem::path(filePath).extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    
    std::vector<std::string> supportedFormats = GetSupportedImageExtensions();
    
    return std::find(supportedFormats.begin(), supportedFormats.end(), extension) != supportedFormats.end();
}

// 获取图片信息
ImageInfo MinibinkImageViewer::GetImageInfo(const std::string& imagePath) {
    ImageInfo info;
    
    try {
        std::filesystem::path path(imagePath);
        
        info.fileName = path.filename().string();
        info.filePath = imagePath;
        info.fileSize = GetFileSize(imagePath);
        info.createTime = GetFileCreateTime(imagePath);
        info.format = path.extension().string();
        info.dimensions = GetImageDimensions(imagePath);
        info.hasExifData = false; // 这里可以集成EXIF检测
        
    } catch (const std::exception& e) {
        std::cerr << "GetImageInfo failed: " << e.what() << std::endl;
    }
    
    return info;
}

// 扫描图片文件夹
std::vector<ImageInfo> MinibinkImageViewer::ScanImageFolder(const std::string& folderPath, bool recursive) {
    std::vector<ImageInfo> images;
    
    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(folderPath)) {
                if (entry.is_regular_file() && IsValidImageFile(entry.path().string())) {
                    images.push_back(GetImageInfo(entry.path().string()));
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(folderPath)) {
                if (entry.is_regular_file() && IsValidImageFile(entry.path().string())) {
                    images.push_back(GetImageInfo(entry.path().string()));
                }
            }
        }
        
        // 按文件名排序
        std::sort(images.begin(), images.end(), 
                 [](const ImageInfo& a, const ImageInfo& b) {
                     return a.fileName < b.fileName;
                 });
        
    } catch (const std::exception& e) {
        std::cerr << "ScanImageFolder failed: " << e.what() << std::endl;
    }
    
    return images;
}

// 执行JavaScript脚本
bool MinibinkImageViewer::ExecuteScript(const std::string& script) {
    if (!m_webView) {
        return false;
    }
    
    try {
        wkeRunJS(m_webView, script.c_str());
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "ExecuteScript failed: " << e.what() << std::endl;
        return false;
    }
}

// 调用JavaScript函数
bool MinibinkImageViewer::CallJavaScriptFunction(const std::string& functionName, const std::vector<std::string>& params) {
    std::ostringstream script;
    script << functionName << "(";
    
    for (size_t i = 0; i < params.size(); ++i) {
        if (i > 0) script << ", ";
        script << "'" << EscapeJsonString(params[i]) << "'";
    }
    
    script << ");";
    
    return ExecuteScript(script.str());
}

// 触发前端文件选择
bool MinibinkImageViewer::TriggerFileSelection() {
    return ExecuteScript("selectSingleFile();");
}

// 触发前端文件夹选择
bool MinibinkImageViewer::TriggerFolderSelection() {
    return ExecuteScript("selectFolder();");
}

// 清空图片
bool MinibinkImageViewer::ClearImages() {
    return ExecuteScript("clearImages();");
}

// 检查是否支持文件选择
bool MinibinkImageViewer::IsFileSelectionSupported() const {
    return m_webView != nullptr;
}

// 获取选择的文件信息
std::string MinibinkImageViewer::GetSelectedFilesInfo() {
    // 这里可以通过JavaScript获取当前选择的文件信息
    // 实际实现可能需要更复杂的JavaScript交互
    return "前端文件选择模式";
}

// 设置窗口关闭回调
void MinibinkImageViewer::SetOnWindowClosing(std::function<void()> callback) {
    m_onWindowClosing = callback;
}

// 设置文档就绪回调
void MinibinkImageViewer::SetOnDocumentReady(std::function<void()> callback) {
    m_onDocumentReady = callback;
}

// 运行消息循环
void MinibinkImageViewer::RunMessageLoop() {
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
}

// 处理消息
void MinibinkImageViewer::ProcessMessages() {
    MSG msg;
    while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
}

// 静态回调函数
void MinibinkImageViewer::OnWindowClosingCallback(wkeWebView webView, void* param) {
    MinibinkImageViewer* viewer = static_cast<MinibinkImageViewer*>(param);
    if (viewer && viewer->m_onWindowClosing) {
        viewer->m_onWindowClosing();
    }
}

void MinibinkImageViewer::OnDocumentReadyCallback(wkeWebView webView, void* param) {
    MinibinkImageViewer* viewer = static_cast<MinibinkImageViewer*>(param);
    if (viewer && viewer->m_onDocumentReady) {
        viewer->m_onDocumentReady();
    }
}

// 加载HTML模板
bool MinibinkImageViewer::LoadHtmlTemplate() {
    try {
        std::ifstream file("image_viewer.html");
        if (!file.is_open()) {
            std::cerr << "Cannot open image_viewer.html" << std::endl;
            return false;
        }

        std::ostringstream buffer;
        buffer << file.rdbuf();
        m_htmlTemplate = buffer.str();

        return true;

    } catch (const std::exception& e) {
        std::cerr << "LoadHtmlTemplate failed: " << e.what() << std::endl;
        return false;
    }
}

// 转义JSON字符串
std::string MinibinkImageViewer::EscapeJsonString(const std::string& input) {
    std::string output;
    output.reserve(input.length());

    for (char c : input) {
        switch (c) {
            case '"': output += "\\\""; break;
            case '\\': output += "\\\\"; break;
            case '\b': output += "\\b"; break;
            case '\f': output += "\\f"; break;
            case '\n': output += "\\n"; break;
            case '\r': output += "\\r"; break;
            case '\t': output += "\\t"; break;
            default: output += c; break;
        }
    }

    return output;
}

// 获取文件大小
std::string MinibinkImageViewer::GetFileSize(const std::string& filePath) {
    try {
        auto fileSize = std::filesystem::file_size(filePath);

        if (fileSize < 1024) {
            return std::to_string(fileSize) + " B";
        } else if (fileSize < 1024 * 1024) {
            return std::to_string(fileSize / 1024) + " KB";
        } else {
            return std::to_string(fileSize / (1024 * 1024)) + " MB";
        }

    } catch (const std::exception&) {
        return "未知";
    }
}

// 获取文件创建时间
std::string MinibinkImageViewer::GetFileCreateTime(const std::string& filePath) {
    try {
        auto ftime = std::filesystem::last_write_time(filePath);
        auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
            ftime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
        auto time_t = std::chrono::system_clock::to_time_t(sctp);

        std::ostringstream oss;
        oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return oss.str();

    } catch (const std::exception&) {
        return "未知";
    }
}

// 获取图片尺寸（简单实现，实际可能需要图像库）
std::string MinibinkImageViewer::GetImageDimensions(const std::string& imagePath) {
    // 这里是简化实现，实际应该使用图像库来获取真实尺寸
    // 可以集成如FreeImage、SOIL等库来获取图片尺寸
    return "未知";
}

// 获取支持的图片扩展名
std::vector<std::string> MinibinkImageViewer::GetSupportedImageExtensions() {
    return {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif",
        ".webp", ".ico", ".svg"
    };
}

// 全局函数实现
namespace MinibinkImageViewerUtils {

    bool InitializeMiniblink() {
        try {
            wkeInitialize();
            return true;
        } catch (const std::exception& e) {
            std::cerr << "InitializeMiniblink failed: " << e.what() << std::endl;
            return false;
        }
    }

    void FinalizeMiniblink() {
        try {
            wkeFinalize();
        } catch (const std::exception& e) {
            std::cerr << "FinalizeMiniblink failed: " << e.what() << std::endl;
        }
    }

    MinibinkImageViewer* CreateImageViewer(const std::string& imagePath) {
        MinibinkImageViewer* viewer = new MinibinkImageViewer();

        if (!viewer->Initialize()) {
            delete viewer;
            return nullptr;
        }

        if (!viewer->CreateWindow()) {
            delete viewer;
            return nullptr;
        }

        if (!viewer->LoadImageViewer()) {
            delete viewer;
            return nullptr;
        }

        viewer->ShowWindow(true);

        // 如果提供了图片路径，则显示该图片
        if (!imagePath.empty()) {
            viewer->DisplayImage(imagePath);
        }

        return viewer;
    }

    bool ShowImageFolder(const std::string& folderPath) {
        MinibinkImageViewer* viewer = CreateImageViewer();
        if (!viewer) {
            return false;
        }

        // 设置文档就绪回调
        viewer->SetOnDocumentReady([viewer, folderPath]() {
            auto images = viewer->ScanImageFolder(folderPath, false);
            if (!images.empty()) {
                viewer->SetImageList(images);
                viewer->SetWindowTitle(u8"图片查看器 - " + folderPath);
            }
        });

        viewer->RunMessageLoop();
        delete viewer;

        return true;
    }

    bool ShowSingleImage(const std::string& imagePath) {
        MinibinkImageViewer* viewer = CreateImageViewer();
        if (!viewer) {
            return false;
        }

        // 设置文档就绪回调
        viewer->SetOnDocumentReady([viewer, imagePath]() {
            if (viewer->DisplayImage(imagePath)) {
                std::filesystem::path path(imagePath);
                viewer->SetWindowTitle(u8"图片查看器 - " + path.filename().string());
            }
        });

        viewer->RunMessageLoop();
        delete viewer;

        return true;
    }

    MinibinkImageViewer* CreateInteractiveImageViewer() {
        MinibinkImageViewer* viewer = new MinibinkImageViewer();

        if (!viewer->Initialize()) {
            delete viewer;
            return nullptr;
        }

        if (!viewer->CreateWindow(1200, 900)) {
            delete viewer;
            return nullptr;
        }

        if (!viewer->LoadImageViewer()) {
            delete viewer;
            return nullptr;
        }

        // 设置窗口标题
        viewer->SetWindowTitle(u8"交互式图片查看器 - 请选择图片文件");

        viewer->ShowWindow(true);

        return viewer;
    }

    bool ShowInteractiveImageViewer() {
        MinibinkImageViewer* viewer = CreateInteractiveImageViewer();
        if (!viewer) {
            return false;
        }

        // 设置文档就绪回调
        viewer->SetOnDocumentReady([viewer]() {
            std::cout << u8"交互式图片查看器已就绪，用户可以选择文件" << std::endl;

            // 可以在这里添加一些初始化逻辑
            viewer->ExecuteScript("console.log('交互式图片查看器已就绪');");
        });

        // 设置窗口关闭回调
        viewer->SetOnWindowClosing([]() {
            std::cout << u8"用户关闭了查看器窗口" << std::endl;
            PostQuitMessage(0);
        });

        viewer->RunMessageLoop();
        delete viewer;

        return true;
    }
}

#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <iomanip>
#include "nlohmann/json.hpp"

// 简单的进度回调函数
void testProgressCallback(const std::string& message, int progress) {
    std::cout << "[Progress " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool testQueryTaskControl(const std::string& taskId) {
    // 总是返回false，表示不取消任务
    return false;
}

int main() {
    std::cout << "=== EXIF Manager Test ===" << std::endl;

    std::string taskId = "device_scan_001";

    std::cout << "Starting device-wide image scan and EXIF analysis..." << std::endl;
    std::cout << "This will scan all drives for image files and extract EXIF data" << std::endl;
    std::cout << "Using GDI+ for Windows XP compatibility" << std::endl;
    std::cout << "Please wait, this may take several minutes..." << std::endl;

    // 调用设备扫描和EXIF分析接口
    std::string result = ExifManager::Init_ExifInfoMsg(
        "", // 空参数表示扫描整个设备
        testProgressCallback,
        taskId,
        testQueryTaskControl
    );

    std::cout << "\n=== Device EXIF Analysis Results ===" << std::endl;

    // 解析并显示统计信息
    try {
        nlohmann::json jsonResult = nlohmann::json::parse(result);

        if (jsonResult.find("statistics") != jsonResult.end()) {
            auto stats = jsonResult["statistics"];
            std::cout << "Total images found: " << stats["total_images"] << std::endl;
            std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
            std::cout << "Success rate: " << std::fixed << std::setprecision(1)
                      << stats["extraction_success_rate"].get<double>() << "%" << std::endl;

            if (stats.find("top_manufacturers") != stats.end()) {
                std::cout << "\nTop camera manufacturers:" << std::endl;
                for (auto it = stats["top_manufacturers"].begin(); it != stats["top_manufacturers"].end(); ++it) {
                    std::cout << "  " << it.key() << ": " << it.value() << " images" << std::endl;
                }
            }

            if (stats.find("top_models") != stats.end()) {
                std::cout << "\nTop camera models:" << std::endl;
                for (auto it = stats["top_models"].begin(); it != stats["top_models"].end(); ++it) {
                    std::cout << "  " << it.key() << ": " << it.value() << " images" << std::endl;
                }
            }
        }

        std::cout << "\nFull JSON result available (truncated for readability)" << std::endl;

    } catch (const std::exception& e) {
        std::cout << "Error parsing JSON result: " << e.what() << std::endl;
        std::cout << "Raw result (first 500 chars): " << result.substr(0, 500) << "..." << std::endl;
    }

    return 0;
}

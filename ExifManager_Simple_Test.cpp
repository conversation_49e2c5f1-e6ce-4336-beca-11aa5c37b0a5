// 简单的编译测试
#include <iostream>
#include <string>
#include <functional>

// 模拟QueryTaskControlCallback类型
typedef std::function<bool(const std::string&)> QueryTaskControlCallback;

// 模拟ExifManager类
class ExifManager {
public:
    static std::string Init_ExifInfoMsg(
        const std::string& params,
        void(*progressCallback)(const std::string&, int),
        const std::string& taskId,
        QueryTaskControlCallback queryTaskControlCb
    ) {
        return "{\"status\":\"success\",\"message\":\"Test result\"}";
    }
};

// 测试回调函数
void testProgress(const std::string& msg, int progress) {
    std::cout << "Progress: " << progress << "% - " << msg << std::endl;
}

bool testControl(const std::string& taskId) {
    return false;
}

int main() {
    std::cout << "=== Simple ExifManager Test ===" << std::endl;
    
    std::string result = ExifManager::Init_ExifInfoMsg(
        "test.jpg",
        testProgress,
        "task001",
        testControl
    );
    
    std::cout << "Result: " << result << std::endl;
    return 0;
}

﻿#include "../include/StartupManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <set>
#include <io.h>
#include <direct.h>
#include <algorithm>
#include <chrono>
#include <regex>
#include <shlobj.h>
#include <psapi.h>

#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "version.lib")
#pragma comment(lib, "psapi.lib")

// 启动项注册表路径常量定义
const std::vector<std::pair<std::string, std::string>> StartupManager::STARTUP_REGISTRY_KEYS = {
    {"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", u8"当前用户运行"},
    {"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce", u8"当前用户运行一次"},
    {"SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Run", u8"当前用户运行(32位)"},
    {"SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\RunOnce", u8"当前用户运行一次(32位)"}
};

// 系统级注册表路径常量定义
const std::vector<std::pair<std::string, std::string>> StartupManager::SYSTEM_STARTUP_REGISTRY_KEYS = {
    {"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", u8"系统运行"},
    {"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce", u8"系统运行一次"},
    {"SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Run", u8"系统运行(32位)"},
    {"SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\RunOnce", u8"系统运行一次(32位)"},
    {"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunServices", u8"系统服务运行"},
    {"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunServicesOnce", u8"系统服务运行一次"}
};

StartupManager::StartupManager() : m_initialized(false) {
}

StartupManager::~StartupManager() {
    Cleanup();
}

bool StartupManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_startTime = std::chrono::system_clock::now();
    
    // 初始化COM
    HRESULT hr = CoInitializeEx(0, COINIT_MULTITHREADED);
    if (FAILED(hr)) {
        std::cout << u8"初始化COM失败: " << hr << std::endl;
        return false;
    }

    m_initialized = true;
    return true;
}

void StartupManager::Cleanup() {
    if (m_initialized) {
        CoUninitialize();
        m_initialized = false;
    }
}

std::vector<StartupItemData> StartupManager::GetAllStartupItems() {
    std::vector<StartupItemData> allItems;

    if (!m_initialized) {
        return allItems;
    }

    try {
        // 获取注册表启动项
        auto registryItems = GetRegistryStartupItems();
        allItems.insert(allItems.end(), registryItems.begin(), registryItems.end());

        // 获取启动文件夹项
        auto folderItems = GetStartupFolderItems();
        allItems.insert(allItems.end(), folderItems.begin(), folderItems.end());

        // 获取计划任务启动项
        auto taskItems = GetTaskSchedulerStartupItems();
        allItems.insert(allItems.end(), taskItems.begin(), taskItems.end());

        // 获取服务启动项
        auto serviceItems = GetServiceStartupItems();
        allItems.insert(allItems.end(), serviceItems.begin(), serviceItems.end());

        // 分析每个启动项
        for (auto& item : allItems) {
            AnalyzeStartupItem(item);
        }

    } catch (const std::exception& e) {
        std::cout << u8"获取启动项时发生错误: " << e.what() << std::endl;
    }

    return allItems;
}

std::vector<StartupCategoryData> StartupManager::GetStartupItemsByCategory() {
    auto allItems = GetAllStartupItems();
    return CategorizeStartupItems(allItems);
}

StartupStatistics StartupManager::GetStartupStatistics() {
    StartupStatistics stats;
    
    auto allItems = GetAllStartupItems();
    
    stats.scan_time = std::time(nullptr);

    // 计算扫描耗时
    auto endTime = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_startTime);
    stats.scan_duration = std::to_string(duration.count()) + " ms";

    // 统计各类启动项
    stats.total_startup_items = static_cast<int>(allItems.size());
    
    std::set<std::string> locations;
    
    for (const auto& item : allItems) {
        if (item.enabled) stats.enabled_items++;
        else stats.disabled_items++;
        
        if (item.is_system) stats.system_items++;
        else stats.user_items++;
        
        if (item.startup_type == "Registry") stats.registry_items++;
        else if (item.startup_type == "Folder") stats.folder_items++;
        else if (item.startup_type == "Task") stats.task_items++;
        else if (item.startup_type == "Service") stats.service_items++;
        
        if (!item.file_exists) stats.missing_files++;
        
        locations.insert(item.location);
    }
    
    // 转换set到vector
    stats.startup_locations.assign(locations.begin(), locations.end());

    return stats;
}

nlohmann::json StartupManager::GetStartupInfoAsJson() {
    nlohmann::json result;

    try {
        auto allItems = GetAllStartupItems();
        auto categories = GetStartupItemsByCategory();
        auto stats = GetStartupStatistics();

        // 构建JSON结果
        result["metadata"] = {
            {"tool_name", u8"Windows启动项扫描器"},
            {"version", "1.0.0"},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration}
        };

        // 启动项列表
        result["startup_items"] = allItems;

        // 按分类的启动项
        result["categories"] = categories;

        // 统计信息
        result["statistics"] = stats;

        return result;
    }
    catch (const std::exception& e) {
        nlohmann::json error_result;
        error_result["error"] = u8"获取启动项信息失败";
        error_result["details"] = e.what();
        return error_result;
    }
}

bool StartupManager::SaveStartupInfoToFile(const std::string& filename) {
    try {
        nlohmann::json startupInfo = GetStartupInfoAsJson();

        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << u8"无法打开文件进行写入: " << filename << std::endl;
            return false;
        }

        file << startupInfo.dump(4); // 4 spaces indentation
        file.close();

        std::cout << u8"启动项信息已保存到: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cout << u8"保存启动项信息时发生错误: " << e.what() << std::endl;
        return false;
    }
}

// 封装的启动项信息获取接口实现
std::string StartupManager::Init_StartupInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化启动项管理器...", 10);
        }

        // 创建启动项管理器实例
        StartupManager startupManager;
        if (!startupManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化启动项管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描启动项...", 50);
        }

        // 获取启动项信息
        nlohmann::json startupInfo = startupManager.GetStartupInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"启动项扫描完成", 100);
        }

        // 添加任务状态信息
        startupInfo["status"] = "success";
        startupInfo["task_id"] = taskId;
        startupInfo["message"] = u8"启动项信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            startupInfo["request_params"] = params;
        }

        return startupInfo.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"启动项扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}

// 辅助函数实现
std::string StartupManager::ConvertToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::string StartupManager::ConvertToString(LPCWSTR wstr) {
    if (wstr == nullptr) return std::string();

    int len = wcslen(wstr);
    if (len == 0) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr, len, NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr, len, &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::wstring StartupManager::ConvertToWString(const std::string& str) {
    if (str.empty()) return std::wstring();

    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

// 获取注册表启动项
std::vector<StartupItemData> StartupManager::GetRegistryStartupItems() {
    std::vector<StartupItemData> items;

    try {
        // 获取当前用户启动项
        for (const auto& keyInfo : STARTUP_REGISTRY_KEYS) {
            auto userItems = GetRegistryStartupFromKey(HKEY_CURRENT_USER, keyInfo.first, keyInfo.second, false);
            items.insert(items.end(), userItems.begin(), userItems.end());
        }

        // 获取系统启动项
        for (const auto& keyInfo : SYSTEM_STARTUP_REGISTRY_KEYS) {
            auto systemItems = GetRegistryStartupFromKey(HKEY_LOCAL_MACHINE, keyInfo.first, keyInfo.second, true);
            items.insert(items.end(), systemItems.begin(), systemItems.end());
        }

    } catch (const std::exception& e) {
        std::cout << u8"获取注册表启动项时发生错误: " << e.what() << std::endl;
    }

    return items;
}

std::vector<StartupItemData> StartupManager::GetRegistryStartupFromKey(HKEY hKey, const std::string& keyPath, const std::string& location, bool isSystem) {
    std::vector<StartupItemData> items;

    try {
        HKEY hSubKey;
        if (RegOpenKeyExA(hKey, keyPath.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {

            auto valueNames = EnumerateRegistryValues(hSubKey, "");

            for (const auto& valueName : valueNames) {
                StartupItemData item;
                item.name = valueName;
                item.command = ReadRegistryString(hSubKey, "", valueName);
                item.location = location;
                item.registry_key = keyPath;
                item.value_name = valueName;
                item.startup_type = "Registry";
                item.is_system = isSystem;
                item.enabled = true; // 注册表中的项默认启用

                // 提取可执行文件路径
                item.file_path = ExtractExecutablePath(item.command);

                items.push_back(item);
            }

            RegCloseKey(hSubKey);
        }

    } catch (const std::exception& e) {
        std::cout << u8"从注册表键获取启动项时发生错误: " << e.what() << std::endl;
    }

    return items;
}

// 获取启动文件夹项
std::vector<StartupItemData> StartupManager::GetStartupFolderItems() {
    std::vector<StartupItemData> items;

    try {
        // 获取当前用户启动文件夹
        std::string userStartupPath = GetStartupFolderPath(false);
        if (!userStartupPath.empty()) {
            auto userItems = GetStartupFolderFromPath(userStartupPath, "User Startup Folder", false);
            items.insert(items.end(), userItems.begin(), userItems.end());
        }

        // 获取所有用户启动文件夹
        std::string allUsersStartupPath = GetStartupFolderPath(true);
        if (!allUsersStartupPath.empty()) {
            auto allUsersItems = GetStartupFolderFromPath(allUsersStartupPath, "All Users Startup Folder", true);
            items.insert(items.end(), allUsersItems.begin(), allUsersItems.end());
        }

    } catch (const std::exception& e) {
        std::cout << u8"获取启动文件夹项时发生错误: " << e.what() << std::endl;
    }

    return items;
}

std::vector<StartupItemData> StartupManager::GetStartupFolderFromPath(const std::string& folderPath, const std::string& location, bool isSystem) {
    std::vector<StartupItemData> items;

    try {
        // 使用Windows API检查目录是否存在
        DWORD dwAttrib = GetFileAttributesA(folderPath.c_str());
        if (dwAttrib != INVALID_FILE_ATTRIBUTES && (dwAttrib & FILE_ATTRIBUTE_DIRECTORY)) {
            // 使用FindFirstFile/FindNextFile遍历目录
            std::string searchPath = folderPath + "\\*";
            WIN32_FIND_DATAA findData;
            HANDLE hFind = FindFirstFileA(searchPath.c_str(), &findData);

            if (hFind != INVALID_HANDLE_VALUE) {
                do {
                    // 跳过目录和特殊文件
                    if (!(findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) &&
                        strcmp(findData.cFileName, ".") != 0 &&
                        strcmp(findData.cFileName, "..") != 0) {

                        // 过滤系统配置文件
                        std::string fileName = findData.cFileName;
                        if (IsSystemConfigFile(fileName)) {
                            continue; // 跳过系统配置文件
                        }

                        StartupItemData item;
                        item.name = findData.cFileName;
                        item.file_path = folderPath + "\\" + findData.cFileName;
                        item.command = item.file_path;
                        item.location = location;
                        item.startup_type = "Folder";
                        item.is_system = isSystem;
                        item.enabled = true;

                        items.push_back(item);
                    }
                } while (FindNextFileA(hFind, &findData));

                FindClose(hFind);
            }
        }

    } catch (const std::exception& e) {
        std::cout << u8"从启动文件夹获取项时发生错误: " << e.what() << std::endl;
    }

    return items;
}

// 获取计划任务启动项（简化实现）
std::vector<StartupItemData> StartupManager::GetTaskSchedulerStartupItems() {
    std::vector<StartupItemData> items;

    // 简化实现：这里可以通过WMI或Task Scheduler API获取
    // 为了兼容性，暂时返回空列表

    return items;
}

// 获取服务启动项（简化实现）
std::vector<StartupItemData> StartupManager::GetServiceStartupItems() {
    std::vector<StartupItemData> items;

    // 简化实现：这里可以通过Service Control Manager获取自动启动的服务
    // 为了兼容性，暂时返回空列表

    return items;
}

// 获取文件信息
void StartupManager::GetFileInfo(StartupItemData& item) {
    if (item.file_path.empty()) {
        return;
    }

    try {
        // 使用Windows API检查文件是否存在
        DWORD dwAttrib = GetFileAttributesA(item.file_path.c_str());
        item.file_exists = (dwAttrib != INVALID_FILE_ATTRIBUTES && !(dwAttrib & FILE_ATTRIBUTE_DIRECTORY));

        if (item.file_exists) {
            // 获取文件时间和大小
            WIN32_FILE_ATTRIBUTE_DATA fileData;
            if (GetFileAttributesExA(item.file_path.c_str(), GetFileExInfoStandard, &fileData)) {
                item.creation_time = FormatFileTime(fileData.ftCreationTime);
                item.modification_time = FormatFileTime(fileData.ftLastWriteTime);
                item.file_size = FormatFileSize(fileData.nFileSizeLow, fileData.nFileSizeHigh);
            }

            // 获取版本信息
            item.version = GetFileVersion(item.file_path);
            item.description = GetFileDescription(item.file_path);
            item.company = GetFileCompany(item.file_path);
        }

    } catch (const std::exception& e) {
        std::cout << u8"获取文件信息时发生错误: " << e.what() << std::endl;
    }
}

std::string StartupManager::GetFileVersion(const std::string& filePath) {
    try {
        DWORD dwSize = GetFileVersionInfoSizeA(filePath.c_str(), NULL);
        if (dwSize == 0) return "";

        std::vector<BYTE> buffer(dwSize);
        if (!GetFileVersionInfoA(filePath.c_str(), 0, dwSize, buffer.data())) {
            return "";
        }

        VS_FIXEDFILEINFO* pFileInfo = nullptr;
        UINT uLen = 0;
        if (VerQueryValueA(buffer.data(), "\\", (LPVOID*)&pFileInfo, &uLen)) {
            if (pFileInfo != nullptr) {
                return std::to_string(HIWORD(pFileInfo->dwFileVersionMS)) + "." +
                       std::to_string(LOWORD(pFileInfo->dwFileVersionMS)) + "." +
                       std::to_string(HIWORD(pFileInfo->dwFileVersionLS)) + "." +
                       std::to_string(LOWORD(pFileInfo->dwFileVersionLS));
            }
        }
    } catch (...) {
        // 忽略错误
    }
    return "";
}

std::string StartupManager::GetFileDescription(const std::string& filePath) {
    try {
        DWORD dwSize = GetFileVersionInfoSizeA(filePath.c_str(), NULL);
        if (dwSize == 0) return "";

        std::vector<BYTE> buffer(dwSize);
        if (!GetFileVersionInfoA(filePath.c_str(), 0, dwSize, buffer.data())) {
            return "";
        }

        LPVOID lpBuffer = nullptr;
        UINT uLen = 0;
        if (VerQueryValueA(buffer.data(), "\\StringFileInfo\\040904b0\\FileDescription", &lpBuffer, &uLen)) {
            return std::string((char*)lpBuffer);
        }
    } catch (...) {
        // 忽略错误
    }
    return "";
}

std::string StartupManager::GetFileCompany(const std::string& filePath) {
    try {
        DWORD dwSize = GetFileVersionInfoSizeA(filePath.c_str(), NULL);
        if (dwSize == 0) return "";

        std::vector<BYTE> buffer(dwSize);
        if (!GetFileVersionInfoA(filePath.c_str(), 0, dwSize, buffer.data())) {
            return "";
        }

        LPVOID lpBuffer = nullptr;
        UINT uLen = 0;
        if (VerQueryValueA(buffer.data(), "\\StringFileInfo\\040904b0\\CompanyName", &lpBuffer, &uLen)) {
            return std::string((char*)lpBuffer);
        }
    } catch (...) {
        // 忽略错误
    }
    return "";
}

std::string StartupManager::FormatFileSize(DWORD fileSizeLow, DWORD fileSizeHigh) {
    ULONGLONG fileSize = ((ULONGLONG)fileSizeHigh << 32) | fileSizeLow;

    if (fileSize < 1024) {
        return std::to_string(fileSize) + " B";
    } else if (fileSize < 1024 * 1024) {
        return std::to_string(fileSize / 1024) + " KB";
    } else if (fileSize < 1024 * 1024 * 1024) {
        return std::to_string(fileSize / (1024 * 1024)) + " MB";
    } else {
        return std::to_string(fileSize / (1024 * 1024 * 1024)) + " GB";
    }
}

std::string StartupManager::FormatFileTime(const FILETIME& fileTime) {
    SYSTEMTIME systemTime;
    if (FileTimeToSystemTime(&fileTime, &systemTime)) {
        char buffer[64];
        sprintf_s(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
                 systemTime.wYear, systemTime.wMonth, systemTime.wDay,
                 systemTime.wHour, systemTime.wMinute, systemTime.wSecond);
        return std::string(buffer);
    }
    return "";
}

// 路径处理函数
std::string StartupManager::ExpandEnvironmentPath(const std::string& path) {
    char expanded[MAX_PATH];
    if (ExpandEnvironmentStringsA(path.c_str(), expanded, MAX_PATH) > 0) {
        return std::string(expanded);
    }
    return path;
}

std::string StartupManager::ExtractExecutablePath(const std::string& command) {
    std::string cmd = command;

    // 去除前后空格
    while (!cmd.empty() && (cmd.front() == ' ' || cmd.front() == '\t')) {
        cmd = cmd.substr(1);
    }
    while (!cmd.empty() && (cmd.back() == ' ' || cmd.back() == '\t')) {
        cmd = cmd.substr(0, cmd.length() - 1);
    }

    if (cmd.empty()) {
        return "";
    }

    std::string executablePath;

    // 处理带引号的路径
    if (cmd.front() == '"') {
        // 查找结束引号
        size_t endQuotePos = cmd.find('"', 1);
        if (endQuotePos != std::string::npos) {
            executablePath = cmd.substr(1, endQuotePos - 1);
        } else {
            // 没有结束引号，取整个字符串（去掉开始引号）
            executablePath = cmd.substr(1);
        }
    } else {
        // 没有引号，查找第一个空格（参数分隔符）
        size_t spacePos = cmd.find(' ');
        if (spacePos != std::string::npos) {
            executablePath = cmd.substr(0, spacePos);
        } else {
            executablePath = cmd;
        }
    }

    // 展开环境变量
    return ExpandEnvironmentPath(executablePath);
}

bool StartupManager::IsSystemPath(const std::string& path) {
    std::string lowerPath = path;
    std::transform(lowerPath.begin(), lowerPath.end(), lowerPath.begin(), ::tolower);

    return lowerPath.find("\\windows\\") != std::string::npos ||
           lowerPath.find("\\program files\\") != std::string::npos ||
           lowerPath.find("\\program files (x86)\\") != std::string::npos;
}

bool StartupManager::IsSystemConfigFile(const std::string& fileName) {
    // 转换为小写进行比较
    std::string lowerFileName = fileName;
    std::transform(lowerFileName.begin(), lowerFileName.end(), lowerFileName.begin(), ::tolower);

    // 系统配置文件列表
    static const std::vector<std::string> systemConfigFiles = {
        "desktop.ini",      // 文件夹配置文件
        "thumbs.db",        // 缩略图缓存文件
        "folder.htt",       // 文件夹模板文件
        "autorun.inf",      // 自动运行配置文件（通常在光盘中）
        ".ds_store",        // macOS系统文件
        "system volume information", // 系统卷信息
        "$recycle.bin",     // 回收站文件夹
        "hiberfil.sys",     // 休眠文件
        "pagefile.sys",     // 虚拟内存文件
        "swapfile.sys"      // 交换文件
    };

    // 检查是否为系统配置文件
    for (const auto& configFile : systemConfigFiles) {
        if (lowerFileName == configFile) {
            return true;
        }
    }

    // 检查文件扩展名
    size_t dotPos = lowerFileName.find_last_of('.');
    if (dotPos != std::string::npos) {
        std::string extension = lowerFileName.substr(dotPos);

        // 系统文件扩展名
        static const std::vector<std::string> systemExtensions = {
            ".tmp",         // 临时文件
            ".temp",        // 临时文件
            ".log",         // 日志文件
            ".bak",         // 备份文件
            ".old",         // 旧文件
            ".cache",       // 缓存文件
            ".lock",        // 锁定文件
            ".db",          // 数据库文件（通常是系统缓存）
            ".dat",         // 数据文件（可能是系统文件）
            ".sys"          // 系统文件
        };

        for (const auto& sysExt : systemExtensions) {
            if (extension == sysExt) {
                return true;
            }
        }
    }

    // 检查隐藏文件和系统文件属性
    // 注意：这里我们只检查文件名，实际的文件属性检查需要完整路径
    if (!lowerFileName.empty() && lowerFileName[0] == '.') {
        return true; // 隐藏文件（Unix风格）
    }

    return false;
}

// 注册表操作
std::string StartupManager::ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return "";
        }
        needClose = true;
    }

    char buffer[1024];
    DWORD bufferSize = sizeof(buffer);
    DWORD type;

    LONG result = RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &type, (LPBYTE)buffer, &bufferSize);

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    if (result == ERROR_SUCCESS && (type == REG_SZ || type == REG_EXPAND_SZ)) {
        return std::string(buffer);
    }

    return "";
}

std::vector<std::string> StartupManager::EnumerateRegistryValues(HKEY hKey, const std::string& subKey) {
    std::vector<std::string> values;

    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return values;
        }
        needClose = true;
    }

    DWORD index = 0;
    char valueName[256];
    DWORD valueNameSize;

    while (true) {
        valueNameSize = sizeof(valueName);
        LONG result = RegEnumValueA(hSubKey, index, valueName, &valueNameSize, nullptr, nullptr, nullptr, nullptr);

        if (result == ERROR_SUCCESS) {
            values.push_back(std::string(valueName));
            index++;
        } else {
            break;
        }
    }

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    return values;
}

// 特殊文件夹路径获取
std::string StartupManager::GetStartupFolderPath(bool allUsers) {
    char path[MAX_PATH];
    HRESULT hr;

    if (allUsers) {
        hr = SHGetFolderPathA(NULL, CSIDL_COMMON_STARTUP, NULL, SHGFP_TYPE_CURRENT, path);
    } else {
        hr = SHGetFolderPathA(NULL, CSIDL_STARTUP, NULL, SHGFP_TYPE_CURRENT, path);
    }

    if (SUCCEEDED(hr)) {
        return std::string(path);
    }

    return "";
}

std::string StartupManager::GetProgramDataPath() {
    char path[MAX_PATH];
    HRESULT hr = SHGetFolderPathA(NULL, CSIDL_COMMON_APPDATA, NULL, SHGFP_TYPE_CURRENT, path);

    if (SUCCEEDED(hr)) {
        return std::string(path);
    }

    return "";
}

std::string StartupManager::GetUserProfilePath() {
    char path[MAX_PATH];
    HRESULT hr = SHGetFolderPathA(NULL, CSIDL_PROFILE, NULL, SHGFP_TYPE_CURRENT, path);

    if (SUCCEEDED(hr)) {
        return std::string(path);
    }

    return "";
}

// 数据分析
void StartupManager::AnalyzeStartupItem(StartupItemData& item) {
    // 获取文件信息
    GetFileInfo(item);

    // 判断是否为系统启动项
    if (!item.is_system && IsSystemPath(item.file_path)) {
        item.is_system = true;
    }
}

std::vector<StartupCategoryData> StartupManager::CategorizeStartupItems(const std::vector<StartupItemData>& items) {
    std::vector<StartupCategoryData> categories;

    // 按启动类型分类
    std::map<std::string, std::vector<StartupItemData>> categoryMap;

    for (const auto& item : items) {
        categoryMap[item.startup_type].push_back(item);
    }

    for (const auto& pair : categoryMap) {
        StartupCategoryData category;
        category.category_name = pair.first;
        category.items = pair.second;
        category.item_count = static_cast<int>(pair.second.size());

        if (pair.first == "Registry") {
            category.category_description = u8"注册表启动项";
        } else if (pair.first == "Folder") {
            category.category_description = u8"启动文件夹项";
        } else if (pair.first == "Task") {
            category.category_description = u8"计划任务项";
        } else if (pair.first == "Service") {
            category.category_description = u8"服务项";
        } else {
            category.category_description = u8"其他启动项";
        }

        categories.push_back(category);
    }

    return categories;
}

// 时间转换
std::string StartupManager::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    struct tm timeinfo;
    if (localtime_s(&timeinfo, &time_t) == 0) {
        char buffer[64];
        sprintf_s(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
                 timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
                 timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec);
        return std::string(buffer);
    }
    return "Unknown";
}

// 错误处理
std::string StartupManager::GetLastErrorString() {
    DWORD error = GetLastError();
    LPSTR messageBuffer = nullptr;

    FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                   nullptr, error, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                   (LPSTR)&messageBuffer, 0, nullptr);

    std::string message = messageBuffer ? messageBuffer : "Unknown error";
    LocalFree(messageBuffer);

    return message;
}

void StartupManager::LogError(const std::string& error) {
    std::cout << u8"[错误] " << error << std::endl;
}

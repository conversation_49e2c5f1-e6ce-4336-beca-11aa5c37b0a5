﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// Windows共享文件夹数据结构
struct ShareData {
    std::string share_name;             // 共享名称
    std::string share_path;             // 共享路径
    std::string share_type;             // 共享类型 (Disk, Print, IPC, etc.)
    std::string description;            // 共享描述
    DWORD current_uses;                 // 当前连接数
    DWORD max_uses;                     // 最大连接数
    std::string permissions;            // 权限信息
    bool is_hidden;                     // 是否为隐藏共享
    std::string security_descriptor;    // 安全描述符
    DWORD share_flags;                  // 共享标志

    ShareData() : current_uses(0), max_uses(0), is_hidden(false), share_flags(0) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ShareData,
    share_name, share_path, share_type, description,
    current_uses, max_uses, permissions, is_hidden,
    security_descriptor, share_flags)

﻿#include "ImageManager.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <cstring>
#include <io.h>

// 使用Windows API替代std::filesystem以提高兼容性
#include <shlwapi.h>
#pragma comment(lib, "shlwapi.lib")

using namespace Gdiplus;

ImageManager::ImageManager() : m_initialized(false), m_gdiplusToken(0) {
}

ImageManager::~ImageManager() {
    Cleanup();
}

bool ImageManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    try {
        // 初始化GDI+
        GdiplusStartupInput gdiplusStartupInput;
        Status status = GdiplusStartup(&m_gdiplusToken, &gdiplusStartupInput, nullptr);
        
        if (status != Ok) {
            std::cout << u8"初始化GDI+失败，错误代码: " << status << std::endl;
            return false;
        }

        m_initialized = true;
        return true;

    } catch (const std::exception& e) {
        std::cout << u8"初始化ImageManager时发生异常: " << e.what() << std::endl;
        return false;
    }
}

void ImageManager::Cleanup() {
    if (m_initialized && m_gdiplusToken != 0) {
        GdiplusShutdown(m_gdiplusToken);
        m_gdiplusToken = 0;
    }
    m_initialized = false;
}

std::string ImageManager::Init_ImageConversionMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化图片转换器...", 10);
        }

        // 创建图片管理器实例
        ImageManager imageManager;
        if (!imageManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化图片转换器失败"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 解析参数（期望JSON格式的参数）
        std::string imagePath;
        std::string directoryPath;
        bool processDirectory = false;

        if (!params.empty()) {
            try {
                nlohmann::json paramJson = nlohmann::json::parse(params);
                if (paramJson.contains("image_path")) {
                    imagePath = paramJson["image_path"];
                }
                if (paramJson.contains("directory_path")) {
                    directoryPath = paramJson["directory_path"];
                    processDirectory = true;
                }
            } catch (...) {
                // 如果不是JSON格式，将参数作为文件路径处理
                imagePath = params;
            }
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 构建结果JSON
        nlohmann::json result;
        result["metadata"] = {
            {"tool_name", "Windows Image Converter"},
            {"version", "1.0.0"},
            {"scan_time", imageManager.GetCurrentTimestamp()},
            {"supported_formats", imageManager.GetSupportedFormats()}
        };

        if (processDirectory && !directoryPath.empty()) {
            // 处理目录中的所有图片
            if (progressCallback) {
                progressCallback(u8"正在扫描目录中的图片文件...", 30);
            }

            std::vector<ImageInfo> images = imageManager.ConvertImagesInDirectory(directoryPath);
            
            result["conversion_type"] = "directory";
            result["directory_path"] = directoryPath;
            result["total_images_found"] = images.size();
            result["images"] = images;

        } else if (!imagePath.empty()) {
            // 处理单个图片文件
            if (progressCallback) {
                progressCallback(u8"正在转换图片文件...", 50);
            }

            ImageInfo imageInfo;
            bool success = imageManager.ConvertImageToBitmap(imagePath, imageInfo);
            
            result["conversion_type"] = "single_file";
            result["image_path"] = imagePath;
            result["conversion_success"] = success;
            result["image_info"] = imageInfo;

        } else {
            // 没有指定路径，返回错误
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"未指定图片路径或目录路径"},
                {"task_id", taskId},
                {"error_code", "NO_PATH_SPECIFIED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"图片转换完成", 100);
        }

        // 添加任务状态信息
        result["status"] = "success";
        result["task_id"] = taskId;
        result["message"] = u8"图片转换成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            result["request_params"] = params;
        }

        // 生成JSON字符串
        std::string jsonResult = result.dump(4);

        // 自动保存结果到JSON文件
        std::string outputFilename;
        if (processDirectory && !directoryPath.empty()) {
            outputFilename = "directory_conversion_" + taskId + ".json";
        } else if (!imagePath.empty()) {
            outputFilename = imageManager.GenerateOutputFilename(imagePath, "_conversion_result");
        } else {
            outputFilename = "conversion_result_" + taskId + ".json";
        }

        bool saveSuccess = imageManager.SaveResultToJsonFile(jsonResult, outputFilename);

        // 在结果中添加保存信息
        nlohmann::json finalResult = nlohmann::json::parse(jsonResult);
        finalResult["output_file"] = {
            {"filename", outputFilename},
            {"save_success", saveSuccess},
            {"save_message", saveSuccess ? "Result saved to JSON file successfully" : "Failed to save result to JSON file"}
        };

        return finalResult.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"图片转换过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}

bool ImageManager::ConvertImageToBitmap(const std::string& imagePath, ImageInfo& imageInfo) {
    try {
        // 清理路径中的无效字符
        std::string cleanPath = imagePath;
        if (!cleanPath.empty() && cleanPath[0] == '?') {
            cleanPath = cleanPath.substr(1);
        }

        // 初始化图片信息
        imageInfo.file_path = cleanPath;
        imageInfo.filename = GetFilenameFromPath(cleanPath);
        imageInfo.original_format = GetImageFormat(cleanPath);
        imageInfo.file_size = GetFileSize(cleanPath);
        imageInfo.conversion_success = false;

        std::cout << "Debug: Processing file: " << cleanPath << std::endl;
        std::cout << "Debug: File exists check..." << std::endl;

        // 检查文件是否存在
        if (!FileExists(cleanPath)) {
            imageInfo.error_message = "File does not exist";
            std::cout << "Debug: File not found: " << cleanPath << std::endl;
            return false;
        }

        std::cout << "Debug: File exists, size: " << imageInfo.file_size << " bytes" << std::endl;

        // 检查是否为支持的格式
        if (!IsSupportedImageFormat(cleanPath)) {
            imageInfo.error_message = "Unsupported image format";
            std::cout << "Debug: Unsupported format for: " << cleanPath << std::endl;
            return false;
        }

        std::cout << "Debug: Format supported, converting to wide string..." << std::endl;

        // 转换路径为宽字符
        std::wstring wImagePath = StringToWString(cleanPath);

        std::cout << "Debug: Loading image with GDI+..." << std::endl;

        // 使用GDI+加载图片
        Bitmap* bitmap = new Bitmap(wImagePath.c_str());
        Status status = bitmap->GetLastStatus();
        if (status != Ok) {
            imageInfo.error_message = "Failed to load image file. GDI+ Status: " + std::to_string(status);
            std::cout << "Debug: GDI+ load failed with status: " << status << std::endl;
            delete bitmap;
            return false;
        }

        std::cout << "Debug: Image loaded successfully" << std::endl;

        // 获取图片尺寸
        imageInfo.width = bitmap->GetWidth();
        imageInfo.height = bitmap->GetHeight();

        std::cout << "Debug: Image dimensions: " << imageInfo.width << "x" << imageInfo.height << std::endl;

        // 创建位图
        std::cout << "Debug: Creating bitmap from GDI+ bitmap..." << std::endl;
        HBITMAP hBitmap = CreateBitmapFromGdiplusBitmap(bitmap);
        if (hBitmap == nullptr) {
            imageInfo.error_message = "Failed to create bitmap";
            std::cout << "Debug: Failed to create HBITMAP" << std::endl;
            delete bitmap;
            return false;
        }

        std::cout << "Debug: Bitmap created, encoding to Base64..." << std::endl;

        // 转换为Base64
        imageInfo.base64_data = EncodeBitmapToBase64(hBitmap);
        if (imageInfo.base64_data.empty()) {
            imageInfo.error_message = "Base64 encoding failed";
            std::cout << "Debug: Base64 encoding failed" << std::endl;
            DeleteObject(hBitmap);
            delete bitmap;
            return false;
        }

        std::cout << "Debug: Base64 encoding successful, length: " << imageInfo.base64_data.length() << std::endl;

        // 清理资源
        DeleteObject(hBitmap);
        delete bitmap;

        imageInfo.conversion_success = true;
        return true;

    } catch (const std::exception& e) {
        imageInfo.error_message = std::string(u8"转换异常: ") + e.what();
        return false;
    }
}

std::vector<ImageInfo> ImageManager::ConvertImagesInDirectory(const std::string& directoryPath) {
    std::vector<ImageInfo> images;

    try {
        std::vector<std::string> imageFiles = FindImageFiles(directoryPath);

        for (const auto& filePath : imageFiles) {
            ImageInfo imageInfo;
            ConvertImageToBitmap(filePath, imageInfo);
            images.push_back(imageInfo);
        }

    } catch (const std::exception& e) {
        std::cout << u8"扫描目录时发生异常: " << e.what() << std::endl;
    }

    return images;
}

bool ImageManager::IsSupportedImageFormat(const std::string& filePath) {
    std::string extension = GetFileExtension(filePath);
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    std::vector<std::string> supportedFormats = {".png", ".jpg", ".jpeg", ".bmp", ".gif", ".tiff", ".tif"};

    return std::find(supportedFormats.begin(), supportedFormats.end(), extension) != supportedFormats.end();
}

std::string ImageManager::GetImageFormat(const std::string& filePath) {
    std::string extension = GetFileExtension(filePath);
    if (!extension.empty() && extension[0] == '.') {
        extension = extension.substr(1);
    }
    std::transform(extension.begin(), extension.end(), extension.begin(), ::toupper);
    return extension;
}

std::vector<std::string> ImageManager::GetSupportedFormats() {
    return {"PNG", "JPG", "JPEG", "BMP", "GIF", "TIFF", "TIF"};
}

std::vector<std::string> ImageManager::FindImageFiles(const std::string& directoryPath) {
    std::vector<std::string> imageFiles;

    try {
        if (!DirectoryExists(directoryPath)) {
            return imageFiles;
        }

        // 使用Windows API查找文件
        std::string searchPath = directoryPath + "\\*.*";
        std::wstring wSearchPath = StringToWString(searchPath);

        WIN32_FIND_DATAW findData;
        HANDLE hFind = FindFirstFileW(wSearchPath.c_str(), &findData);

        if (hFind != INVALID_HANDLE_VALUE) {
            do {
                if (!(findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)) {
                    std::string fileName = WStringToString(findData.cFileName);
                    std::string fullPath = directoryPath + "\\" + fileName;

                    if (IsSupportedImageFormat(fullPath)) {
                        imageFiles.push_back(fullPath);
                    }
                }
            } while (FindNextFileW(hFind, &findData));

            FindClose(hFind);
        }

    } catch (const std::exception& e) {
        std::cout << u8"查找图片文件时发生异常: " << e.what() << std::endl;
    }

    return imageFiles;
}

size_t ImageManager::GetFileSize(const std::string& filePath) {
    try {
        std::wstring wFilePath = StringToWString(filePath);
        HANDLE hFile = CreateFileW(wFilePath.c_str(), GENERIC_READ, FILE_SHARE_READ,
                                  nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);

        if (hFile != INVALID_HANDLE_VALUE) {
            LARGE_INTEGER fileSize;
            if (GetFileSizeEx(hFile, &fileSize)) {
                CloseHandle(hFile);
                return static_cast<size_t>(fileSize.QuadPart);
            }
            CloseHandle(hFile);
        }
    } catch (...) {
        // 忽略异常
    }
    return 0;
}

std::string ImageManager::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    struct tm timeinfo;
    if (localtime_s(&timeinfo, &time_t) == 0) {
        std::stringstream ss;
        ss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
        return ss.str();
    } else {
        return "Invalid time";
    }
}

// 辅助方法实现
std::wstring ImageManager::StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();

    // 首先尝试UTF-8转换
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    if (size_needed > 0) {
        std::wstring wstrTo(size_needed, 0);
        int result = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
        if (result > 0) {
            return wstrTo;
        }
    }

    // 如果UTF-8转换失败，尝试使用系统默认代码页
    size_needed = MultiByteToWideChar(CP_ACP, 0, &str[0], (int)str.size(), NULL, 0);
    if (size_needed > 0) {
        std::wstring wstrTo(size_needed, 0);
        int result = MultiByteToWideChar(CP_ACP, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
        if (result > 0) {
            return wstrTo;
        }
    }

    return std::wstring();
}

std::string ImageManager::WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

HBITMAP ImageManager::CreateBitmapFromGdiplusBitmap(Gdiplus::Bitmap* gdiplusBitmap) {
    try {
        HBITMAP hBitmap = nullptr;

        // 获取位图的颜色信息
        Color color;
        Status status = gdiplusBitmap->GetHBITMAP(color, &hBitmap);

        if (status != Ok) {
            std::cout << u8"从GDI+位图创建HBITMAP失败，错误代码: " << status << std::endl;
            return nullptr;
        }

        return hBitmap;

    } catch (const std::exception& e) {
        std::cout << u8"创建位图时发生异常: " << e.what() << std::endl;
        return nullptr;
    }
}

std::string ImageManager::EncodeBitmapToBase64(HBITMAP hBitmap) {
    try {
        if (hBitmap == nullptr) {
            return "";
        }

        // 获取位图信息
        BITMAP bmp;
        if (GetObject(hBitmap, sizeof(BITMAP), &bmp) == 0) {
            return "";
        }

        // 创建设备上下文
        HDC hdc = GetDC(nullptr);
        HDC hdcMem = CreateCompatibleDC(hdc);

        if (hdcMem == nullptr) {
            ReleaseDC(nullptr, hdc);
            return "";
        }

        // 选择位图到内存DC
        HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

        // 准备位图信息头（使用32位支持透明度）
        BITMAPINFOHEADER bi;
        bi.biSize = sizeof(BITMAPINFOHEADER);
        bi.biWidth = bmp.bmWidth;
        bi.biHeight = bmp.bmHeight;
        bi.biPlanes = 1;
        bi.biBitCount = 32; // 32位支持透明度
        bi.biCompression = BI_RGB;
        bi.biSizeImage = 0;
        bi.biXPelsPerMeter = 0;
        bi.biYPelsPerMeter = 0;
        bi.biClrUsed = 0;
        bi.biClrImportant = 0;

        // 计算图像数据大小
        int lineSize = ((bmp.bmWidth * 4 + 3) / 4) * 4; // 32位，4字节对齐
        int imageSize = lineSize * bmp.bmHeight;

        // 分配内存存储位图数据
        std::vector<BYTE> imageData(imageSize);

        // 获取位图数据
        if (GetDIBits(hdcMem, hBitmap, 0, bmp.bmHeight, imageData.data(),
                     (BITMAPINFO*)&bi, DIB_RGB_COLORS) == 0) {
            SelectObject(hdcMem, hOldBitmap);
            DeleteDC(hdcMem);
            ReleaseDC(nullptr, hdc);
            return "";
        }

        // 生成完整的BMP文件格式
        DWORD fileSize = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + imageSize;
        std::vector<BYTE> bmpData(fileSize);

        // 构建BMP文件头
        BITMAPFILEHEADER* pFileHeader = (BITMAPFILEHEADER*)bmpData.data();
        pFileHeader->bfType = 0x4D42; // "BM" - BMP文件标识
        pFileHeader->bfSize = fileSize;
        pFileHeader->bfReserved1 = 0;
        pFileHeader->bfReserved2 = 0;
        pFileHeader->bfOffBits = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);

        // 构建BMP信息头
        BITMAPINFOHEADER* pInfoHeader = (BITMAPINFOHEADER*)(bmpData.data() + sizeof(BITMAPFILEHEADER));
        *pInfoHeader = bi;
        pInfoHeader->biSizeImage = imageSize;

        // 复制图像数据
        BYTE* pImageData = bmpData.data() + sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);
        memcpy(pImageData, imageData.data(), imageSize);

        // 清理GDI资源
        SelectObject(hdcMem, hOldBitmap);
        DeleteDC(hdcMem);
        ReleaseDC(nullptr, hdc);

        // 转换完整的BMP文件为Base64
        return EncodeToBase64(bmpData);

    } catch (const std::exception& e) {
        std::cout << u8"编码位图为Base64时发生异常: " << e.what() << std::endl;
        return "";
    }
}

std::string ImageManager::EncodeToBase64(const std::vector<BYTE>& data) {
    const char* chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    std::string result;

    int val = 0;
    int valb = -6;

    for (BYTE c : data) {
        val = (val << 8) + c;
        valb += 8;
        while (valb >= 0) {
            result.push_back(chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }

    if (valb > -6) {
        result.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }

    while (result.size() % 4) {
        result.push_back('=');
    }

    return result;
}

// 辅助函数实现
bool ImageManager::FileExists(const std::string& filePath) {
    std::wstring wFilePath = StringToWString(filePath);
    if (wFilePath.empty()) {
        std::cout << "Debug: Failed to convert path to wide string: " << filePath << std::endl;
        return false;
    }

    std::wcout << L"Debug: Checking file existence: " << wFilePath << std::endl;

    DWORD attributes = GetFileAttributesW(wFilePath.c_str());
    bool exists = (attributes != INVALID_FILE_ATTRIBUTES && !(attributes & FILE_ATTRIBUTE_DIRECTORY));

    std::cout << "Debug: File exists result: " << (exists ? "true" : "false") << std::endl;
    if (!exists) {
        DWORD error = GetLastError();
        std::cout << "Debug: GetFileAttributes error: " << error << std::endl;
    }

    return exists;
}

bool ImageManager::DirectoryExists(const std::string& directoryPath) {
    std::wstring wDirectoryPath = StringToWString(directoryPath);
    DWORD attributes = GetFileAttributesW(wDirectoryPath.c_str());
    return (attributes != INVALID_FILE_ATTRIBUTES && (attributes & FILE_ATTRIBUTE_DIRECTORY));
}

std::string ImageManager::GetFilenameFromPath(const std::string& filePath) {
    size_t pos = filePath.find_last_of("\\/");
    if (pos != std::string::npos) {
        return filePath.substr(pos + 1);
    }
    return filePath;
}

std::string ImageManager::GetFileExtension(const std::string& filePath) {
    size_t pos = filePath.find_last_of('.');
    if (pos != std::string::npos) {
        return filePath.substr(pos);
    }
    return "";
}

// JSON文件保存方法实现
bool ImageManager::SaveResultToJsonFile(const std::string& jsonContent, const std::string& outputPath) {
    try {
        std::string filePath = outputPath;

        // 如果没有指定输出路径，使用默认路径
        if (filePath.empty()) {
            filePath = "image_conversion_result_" + GetCurrentTimestamp() + ".json";
            // 替换时间戳中的特殊字符
            std::replace(filePath.begin(), filePath.end(), ':', '-');
            std::replace(filePath.begin(), filePath.end(), ' ', '_');
        }

        // 确保文件扩展名是.json
        if (GetFileExtension(filePath) != ".json") {
            filePath += ".json";
        }

        std::cout << "Saving JSON result to: " << filePath << std::endl;

        // 写入文件
        std::ofstream outFile(filePath, std::ios::out | std::ios::trunc);
        if (!outFile.is_open()) {
            std::cout << "Failed to open output file: " << filePath << std::endl;
            return false;
        }

        outFile << jsonContent;
        outFile.close();

        if (outFile.good()) {
            std::cout << "JSON result saved successfully to: " << filePath << std::endl;
            return true;
        } else {
            std::cout << "Failed to write to file: " << filePath << std::endl;
            return false;
        }

    } catch (const std::exception& e) {
        std::cout << "Exception while saving JSON file: " << e.what() << std::endl;
        return false;
    }
}

std::string ImageManager::GenerateOutputFilename(const std::string& inputPath, const std::string& suffix) {
    try {
        std::string filename = GetFilenameFromPath(inputPath);
        std::string nameWithoutExt = filename;

        // 移除文件扩展名
        size_t dotPos = nameWithoutExt.find_last_of('.');
        if (dotPos != std::string::npos) {
            nameWithoutExt = nameWithoutExt.substr(0, dotPos);
        }

        // 生成输出文件名
        std::string outputFilename = nameWithoutExt + suffix + ".json";

        return outputFilename;

    } catch (const std::exception& e) {
        std::cout << "Exception while generating output filename: " << e.what() << std::endl;
        return "conversion_result.json";
    }
}

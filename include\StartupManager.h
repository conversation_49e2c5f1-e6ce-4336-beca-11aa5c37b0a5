﻿#pragma once
#include "StartupData.h"
#include <vector>
#include <string>
#include <functional>
#include <chrono>
#include <windows.h>
#include <nlohmann/json.hpp>

// 任务控制回调函数类型定义
typedef std::function<bool(const std::string&)> QueryTaskControlCallback;

class StartupManager {
public:
    StartupManager();
    ~StartupManager();

    // 初始化启动项管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有启动项
    std::vector<StartupItemData> GetAllStartupItems();

    // 按分类获取启动项
    std::vector<StartupCategoryData> GetStartupItemsByCategory();

    // 获取启动项统计信息
    StartupStatistics GetStartupStatistics();

    // 获取完整的启动项信息并返回JSON格式
    nlohmann::json GetStartupInfoAsJson();

    // 保存启动项信息到JSON文件
    bool SaveStartupInfoToFile(const std::string& filename);

    // 封装的启动项信息获取接口
    static std::string Init_StartupInfoMsg(
        const std::string& params,
        void(*progressCallback)(const std::string&, int),
        const std::string& taskId,
        QueryTaskControlCallback queryTaskControlCb
    );

private:
    bool m_initialized;
    std::chrono::system_clock::time_point m_startTime;

    // 辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::string ConvertToString(LPCWSTR wstr);
    std::wstring ConvertToWString(const std::string& str);

    // 注册表启动项获取
    std::vector<StartupItemData> GetRegistryStartupItems();
    std::vector<StartupItemData> GetRegistryStartupFromKey(HKEY hKey, const std::string& keyPath, const std::string& location, bool isSystem);

    // 启动文件夹项获取
    std::vector<StartupItemData> GetStartupFolderItems();
    std::vector<StartupItemData> GetStartupFolderFromPath(const std::string& folderPath, const std::string& location, bool isSystem);

    // 计划任务启动项获取
    std::vector<StartupItemData> GetTaskSchedulerStartupItems();

    // 服务启动项获取
    std::vector<StartupItemData> GetServiceStartupItems();

    // 文件信息获取
    void GetFileInfo(StartupItemData& item);
    std::string GetFileVersion(const std::string& filePath);
    std::string GetFileDescription(const std::string& filePath);
    std::string GetFileCompany(const std::string& filePath);
    std::string FormatFileSize(DWORD fileSizeLow, DWORD fileSizeHigh);
    std::string FormatFileTime(const FILETIME& fileTime);

    // 路径处理
    std::string ExpandEnvironmentPath(const std::string& path);
    std::string ExtractExecutablePath(const std::string& command);
    bool IsSystemPath(const std::string& path);
    bool IsSystemConfigFile(const std::string& fileName);

    // 注册表操作
    std::string ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName);
    std::vector<std::string> EnumerateRegistryValues(HKEY hKey, const std::string& subKey);

    // 特殊文件夹路径获取
    std::string GetStartupFolderPath(bool allUsers = false);
    std::string GetProgramDataPath();
    std::string GetUserProfilePath();

    // 数据分析
    void AnalyzeStartupItem(StartupItemData& item);
    std::vector<StartupCategoryData> CategorizeStartupItems(const std::vector<StartupItemData>& items);

    // 时间转换
    std::string GetCurrentTimestamp();

    // 错误处理
    std::string GetLastErrorString();
    void LogError(const std::string& error);

    // 常用注册表路径
    static const std::vector<std::pair<std::string, std::string>> STARTUP_REGISTRY_KEYS;
    static const std::vector<std::pair<std::string, std::string>> SYSTEM_STARTUP_REGISTRY_KEYS;
};

<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{647e1240-0ce5-43d0-9bb0-efdbb8f2655f}</ProjectGuid>
    <RootNamespace>Tool</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141_xp</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;_WIN32_WINNT=0x0501;WINVER=0x0501;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>false</ConformanceMode>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <AdditionalIncludeDirectories>include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>sqlite3.lib;wlanapi.lib;ole32.lib;advapi32.lib;psapi.lib;netapi32.lib;version.lib;wintrust.lib;shell32.lib;gdiplus.lib;shlwapi.lib;ws2_32.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_WIN32_WINNT=0x0501;WINVER=0x0501;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>false</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>sqlite3.lib;wlanapi.lib;ole32.lib;advapi32.lib;psapi.lib;netapi32.lib;version.lib;wintrust.lib;shell32.lib;gdiplus.lib;shlwapi.lib;ws2_32.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>sqlite3.lib;wlanapi.lib;ole32.lib;advapi32.lib;psapi.lib;netapi32.lib;version.lib;wintrust.lib;shell32.lib;gdiplus.lib;shlwapi.lib;ws2_32.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>sqlite3.lib;wlanapi.lib;ole32.lib;advapi32.lib;psapi.lib;netapi32.lib;version.lib;wintrust.lib;shell32.lib;gdiplus.lib;shlwapi.lib;ws2_32.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="src\AccountLockoutPolicyManager.cpp" />
    <ClCompile Include="src\DriverManager.cpp" />
    <ClCompile Include="src\ExifManager.cpp" />
    <ClCompile Include="src\FirewallManager.cpp" />
    <ClCompile Include="src\ImageManager.cpp" />
    <ClCompile Include="src\NetworkConnectionManager.cpp" />
    <ClCompile Include="src\PasswordPolicyManager.cpp" />
    <ClCompile Include="src\ProcessManager.cpp" />
    <ClCompile Include="src\ScreensaverManager.cpp" />
    <ClCompile Include="src\ServiceManager.cpp" />
    <ClCompile Include="src\ShareManager.cpp" />
    <ClCompile Include="src\StartupManager.cpp" />
    <ClCompile Include="src\SystemLogManager.cpp" />
    <ClCompile Include="src\SystemLogManager_Core.cpp" />
    <ClCompile Include="src\SystemLogManager_Utils.cpp" />
    <ClCompile Include="Tool.cpp" />
    <ClCompile Include="src\WiFiManager.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\AccountLockoutPolicyData.h" />
    <ClInclude Include="include\AccountLockoutPolicyManager.h" />
    <ClInclude Include="include\DriverData.h" />
    <ClInclude Include="include\DriverManager.h" />
    <ClInclude Include="include\ExifData.h" />
    <ClInclude Include="include\ExifManager.h" />
    <ClInclude Include="include\FirewallData.h" />
    <ClInclude Include="include\FirewallManager.h" />
    <ClInclude Include="include\ImageManager.h" />
    <ClInclude Include="include\NetworkConnectionData.h" />
    <ClInclude Include="include\NetworkConnectionManager.h" />
    <ClInclude Include="include\PasswordPolicyData.h" />
    <ClInclude Include="include\PasswordPolicyManager.h" />
    <ClInclude Include="include\ProcessData.h" />
    <ClInclude Include="include\ProcessManager.h" />
    <ClInclude Include="include\ScreensaverData.h" />
    <ClInclude Include="include\ScreensaverManager.h" />
    <ClInclude Include="include\ServiceData.h" />
    <ClInclude Include="include\ServiceManager.h" />
    <ClInclude Include="include\ShareData.h" />
    <ClInclude Include="include\ShareManager.h" />
    <ClInclude Include="include\StartupData.h" />
    <ClInclude Include="include\StartupManager.h" />
    <ClInclude Include="include\SystemLogData.h" />
    <ClInclude Include="include\SystemLogManager.h" />
    <ClInclude Include="include\WiFiData.h" />
    <ClInclude Include="include\WiFiManager.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
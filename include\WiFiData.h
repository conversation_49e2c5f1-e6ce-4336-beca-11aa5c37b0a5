﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// WiFi连接记录数据结构
struct WiFiData {
    std::string ssid;               // WiFi网络名称
    std::string password;           // WiFi密码（或状态信息）
    std::string description;        // 注册表Description字段
    std::string last_connected;     // 最后连接时间
    bool has_saved_config;          // 是否有保存的配置文件

    WiFiData() : has_saved_config(false) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(WiFiData,
    ssid, password, description, last_connected, has_saved_config)

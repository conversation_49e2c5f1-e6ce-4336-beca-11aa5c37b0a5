#define _CRT_SECURE_NO_WARNINGS
#include "../include/SystemLogManager.h"
#include <windows.h>
#include <winnt.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <chrono>

// 获取系统日志
std::vector<LogEntry> SystemLogManager::GetSystemLogs(const LogQueryParams& params) {
    std::vector<LogEntry> logs;
    
    try {
        HANDLE hEventLog = OpenEventLog(SYSTEM_LOG_NAME);
        if (hEventLog == NULL) {
            LogError("OpenEventLog", GetLastError());
            return logs;
        }
        
        logs = ReadEventLogEntries(hEventLog, params);
        CloseEventLog(hEventLog);
        
    } catch (const std::exception& e) {
        m_lastError = "Failed to get system logs: " + std::string(e.what());
    }
    
    return logs;
}

// 获取安全日志
std::vector<LogEntry> SystemLogManager::GetSecurityLogs(const LogQueryParams& params) {
    std::vector<LogEntry> logs;
    
    try {
        HANDLE hEventLog = OpenEventLog(SECURITY_LOG_NAME);
        if (hEventLog == NULL) {
            LogError("OpenEventLog", GetLastError());
            return logs;
        }
        
        logs = ReadEventLogEntries(hEventLog, params);
        CloseEventLog(hEventLog);
        
    } catch (const std::exception& e) {
        m_lastError = "Failed to get security logs: " + std::string(e.what());
    }
    
    return logs;
}

// 获取应用程序日志
std::vector<LogEntry> SystemLogManager::GetApplicationLogs(const LogQueryParams& params) {
    std::vector<LogEntry> logs;
    
    try {
        HANDLE hEventLog = OpenEventLog(APPLICATION_LOG_NAME);
        if (hEventLog == NULL) {
            LogError("OpenEventLog", GetLastError());
            return logs;
        }
        
        logs = ReadEventLogEntries(hEventLog, params);
        CloseEventLog(hEventLog);
        
    } catch (const std::exception& e) {
        m_lastError = "Failed to get application logs: " + std::string(e.what());
    }
    
    return logs;
}

// 获取登录日志
std::vector<LoginLogEntry> SystemLogManager::GetLoginLogs(int maxEntries, int daysBack) {
    std::vector<LoginLogEntry> loginLogs;
    
    try {
        LogQueryParams params;
        params.log_type = LogType::Security;
        params.max_entries = maxEntries;
        params.event_ids = LOGIN_EVENT_IDS;
        
        // 设置时间范围
        auto now = std::chrono::system_clock::now();
        auto startTime = now - std::chrono::hours(24 * daysBack);
        auto startTimeT = std::chrono::system_clock::to_time_t(startTime);
        
        std::tm tm_info;
        localtime_s(&tm_info, &startTimeT);
        
        std::ostringstream oss;
        oss << std::put_time(&tm_info, "%Y-%m-%d %H:%M:%S");
        params.start_time = oss.str();
        
        // 获取安全日志
        std::vector<LogEntry> securityLogs = GetSecurityLogs(params);
        
        // 转换为登录日志格式
        for (const auto& log : securityLogs) {
            // 只处理登录相关的事件ID
            if (std::find(LOGIN_EVENT_IDS.begin(), LOGIN_EVENT_IDS.end(), log.event_id) != LOGIN_EVENT_IDS.end()) {
                LoginLogEntry loginLog;
                loginLog.event_id = log.event_id;
                loginLog.time_stamp = log.time_generated;
                loginLog.user_name = log.user_name;
                loginLog.computer_name = log.computer_name;
                
                // 根据事件ID设置状态和登录类型
                if (log.event_id == "4624") {
                    loginLog.status = "Successful Login";
                    loginLog.login_type = GetLoginTypeString(2); // 默认交互式登录
                } else if (log.event_id == "4625") {
                    loginLog.status = "Login Failed";
                    loginLog.failure_reason = "Invalid username or password";
                } else if (log.event_id == "4634" || log.event_id == "4647") {
                    loginLog.status = "Logout";
                }
                
                // 从描述中提取更多信息
                if (log.description.find("Logon Type:") != std::string::npos) {
                    // 解析登录类型
                    size_t pos = log.description.find("Logon Type:");
                    if (pos != std::string::npos) {
                        std::string typeStr = log.description.substr(pos + 11, 2);
                        try {
                            int type = std::stoi(typeStr);
                            loginLog.login_type = GetLoginTypeString(type);
                        } catch (...) {
                            loginLog.login_type = "Unknown";
                        }
                    }
                }
                
                loginLogs.push_back(loginLog);
            }
        }
        
    } catch (const std::exception& e) {
        m_lastError = "Failed to get login logs: " + std::string(e.what());
    }
    
    return loginLogs;
}

// 获取开关机日志
std::vector<PowerLogEntry> SystemLogManager::GetPowerLogs(int maxEntries, int daysBack) {
    std::vector<PowerLogEntry> powerLogs;
    
    try {
        LogQueryParams params;
        params.log_type = LogType::System;
        params.max_entries = maxEntries;
        params.event_ids = POWER_EVENT_IDS;
        
        // 设置时间范围
        auto now = std::chrono::system_clock::now();
        auto startTime = now - std::chrono::hours(24 * daysBack);
        auto startTimeT = std::chrono::system_clock::to_time_t(startTime);
        
        std::tm tm_info;
        localtime_s(&tm_info, &startTimeT);
        
        std::ostringstream oss;
        oss << std::put_time(&tm_info, "%Y-%m-%d %H:%M:%S");
        params.start_time = oss.str();
        
        // 获取系统日志
        std::vector<LogEntry> systemLogs = GetSystemLogs(params);
        
        // 转换为开关机日志格式
        for (const auto& log : systemLogs) {
            if (std::find(POWER_EVENT_IDS.begin(), POWER_EVENT_IDS.end(), log.event_id) != POWER_EVENT_IDS.end()) {
                PowerLogEntry powerLog;
                powerLog.event_id = log.event_id;
                powerLog.time_stamp = log.time_generated;
                powerLog.computer_name = log.computer_name;
                powerLog.user_name = log.user_name;
                powerLog.action = GetPowerActionString(log.event_id);
                
                // 根据事件ID设置具体信息
                if (log.event_id == "6005") {
                    powerLog.action = "System Startup";
                    powerLog.shutdown_type = "Normal Startup";
                } else if (log.event_id == "6006") {
                    powerLog.action = "System Shutdown";
                    powerLog.shutdown_type = "Normal Shutdown";
                } else if (log.event_id == "6008") {
                    powerLog.action = "Unexpected Shutdown";
                    powerLog.shutdown_type = "Abnormal Shutdown";
                    powerLog.reason_text = "System unexpected shutdown";
                } else if (log.event_id == "1074") {
                    powerLog.action = "System Restart";
                    powerLog.shutdown_type = "User Restart";
                }
                
                powerLog.reason_text = log.description;
                powerLogs.push_back(powerLog);
            }
        }
        
    } catch (const std::exception& e) {
        m_lastError = "Failed to get power logs: " + std::string(e.what());
    }
    
    return powerLogs;
}

// 打开事件日志
HANDLE SystemLogManager::OpenEventLog(const std::string& logName) {
    HANDLE hEventLog = ::OpenEventLogA(NULL, logName.c_str());
    if (hEventLog == NULL) {
        DWORD error = GetLastError();
        LogError("OpenEventLog", error);
    }
    return hEventLog;
}

// 关闭事件日志
void SystemLogManager::CloseEventLog(HANDLE hEventLog) {
    if (hEventLog != NULL) {
        ::CloseEventLog(hEventLog);
    }
}

// 获取日志级别字符串
std::string SystemLogManager::GetLogLevelString(WORD eventType) {
    switch (eventType) {
        case EVENTLOG_ERROR_TYPE:
            return "Error";
        case EVENTLOG_WARNING_TYPE:
            return "Warning";
        case EVENTLOG_INFORMATION_TYPE:
            return "Information";
        case EVENTLOG_AUDIT_SUCCESS:
            return "Success Audit";
        case EVENTLOG_AUDIT_FAILURE:
            return "Failure Audit";
        default:
            return "Unknown";
    }
}

// 获取登录类型字符串
std::string SystemLogManager::GetLoginTypeString(int loginType) {
    switch (loginType) {
        case 2: return "Interactive Login";
        case 3: return "Network Login";
        case 4: return "Batch Login";
        case 5: return "Service Login";
        case 7: return "Unlock Login";
        case 8: return "Network Clear Text Login";
        case 9: return "New Credentials Login";
        case 10: return "Remote Interactive Login";
        case 11: return "Cached Interactive Login";
        default: return "Unknown Login Type";
    }
}

// 获取开关机动作字符串
std::string SystemLogManager::GetPowerActionString(const std::string& eventId) {
    if (eventId == "6005") return "System Startup";
    if (eventId == "6006") return "System Shutdown";
    if (eventId == "6008") return "Unexpected Shutdown";
    if (eventId == "6009") return "System Version Info";
    if (eventId == "6013") return "System Uptime";
    if (eventId == "1074") return "System Restart";
    if (eventId == "1076") return "System Shutdown Reason";
    if (eventId == "42") return "System Sleep";
    if (eventId == "107") return "System Wake";
    if (eventId == "109") return "Kernel Power Manager";
    return "Unknown Power Event";
}

// 获取计算机名
std::string SystemLogManager::GetComputerName() {
    char computerName[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD size = sizeof(computerName);
    
    if (::GetComputerNameA(computerName, &size)) {
        return std::string(computerName);
    }
    
    return "Unknown Computer";
}

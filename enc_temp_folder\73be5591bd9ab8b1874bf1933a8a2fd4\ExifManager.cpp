﻿#include "../include/ExifManager.h"
#include <sstream>
#include <iomanip>
#include <chrono>
#include <comdef.h>
#include <gdiplus.h>
#include <algorithm>
#include <vector>
#include <thread>
#include <io.h>
#include <direct.h>
#include <locale>
#include <codecvt>
#include <fstream>
#include <ctime>

#pragma comment(lib, "gdiplus.lib")

// 改进的UTF-8字符串清理函数 - 保留有效的UTF-8字符
std::string CleanUtf8String(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    std::string result;
    result.reserve(input.length() * 2); // 预留更多空间用于转义字符

    for (size_t i = 0; i < input.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(input[i]);

        // 处理ASCII字符
        if (c < 128) {
            // 处理控制字符
            if (c < 32) {
                if (c == '\n') {
                    result += "\\n";
                } else if (c == '\r') {
                    result += "\\r";
                } else if (c == '\t') {
                    result += "\\t";
                } else if (c == '\b') {
                    result += "\\b";
                } else if (c == '\f') {
                    result += "\\f";
                } else if (c == '\0') {
                    // 跳过null字符
                    continue;
                } else {
                    // 其他控制字符用Unicode转义
                    char buffer[8];
                    sprintf_s(buffer, "\\u%04x", c);
                    result += buffer;
                }
            }
            // 处理JSON特殊字符
            else if (c == '"') {
                result += "\\\"";
            } else if (c == '\\') {
                result += "\\\\";
            } else {
                result += c;
            }
        }
        // 处理UTF-8多字节字符 - 保留有效的UTF-8序列
        else {
            // 检查是否是有效的UTF-8序列开始
            if ((c & 0xE0) == 0xC0) {
                // 2字节UTF-8序列
                if (i + 1 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    if ((c2 & 0xC0) == 0x80 && c >= 0xC2) {
                        result += c;
                        result += c2;
                        i++;
                        continue;
                    }
                }
            }
            else if ((c & 0xF0) == 0xE0) {
                // 3字节UTF-8序列
                if (i + 2 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        i += 2;
                        continue;
                    }
                }
            }
            else if ((c & 0xF8) == 0xF0) {
                // 4字节UTF-8序列
                if (i + 3 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    unsigned char c4 = static_cast<unsigned char>(input[i + 3]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80 && (c4 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        result += c4;
                        i += 3;
                        continue;
                    }
                }
            }

            // 无效的UTF-8字节，替换为?
            result += '?';
        }
    }

    return result;
}

// 专门用于文件路径的清理函数（保留中文字符，不进行JSON转义）
std::string CleanFilePathString(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    std::string result;
    result.reserve(input.length());

    for (size_t i = 0; i < input.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(input[i]);

        // 处理ASCII字符
        if (c < 128) {
            // 跳过控制字符，但保留可打印字符
            if (c >= 32 || c == '\t') {
                result += c;
            }
        }
        // 处理UTF-8多字节字符 - 保留有效的UTF-8序列
        else {
            // 检查是否是有效的UTF-8序列开始
            if ((c & 0xE0) == 0xC0) {
                // 2字节UTF-8序列
                if (i + 1 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    if ((c2 & 0xC0) == 0x80 && c >= 0xC2) {
                        result += c;
                        result += c2;
                        i++;
                        continue;
                    }
                }
            }
            else if ((c & 0xF0) == 0xE0) {
                // 3字节UTF-8序列
                if (i + 2 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        i += 2;
                        continue;
                    }
                }
            }
            else if ((c & 0xF8) == 0xF0) {
                // 4字节UTF-8序列
                if (i + 3 < input.length()) {
                    unsigned char c2 = static_cast<unsigned char>(input[i + 1]);
                    unsigned char c3 = static_cast<unsigned char>(input[i + 2]);
                    unsigned char c4 = static_cast<unsigned char>(input[i + 3]);
                    if ((c2 & 0xC0) == 0x80 && (c3 & 0xC0) == 0x80 && (c4 & 0xC0) == 0x80) {
                        result += c;
                        result += c2;
                        result += c3;
                        result += c4;
                        i += 3;
                        continue;
                    }
                }
            }

            // 无效的UTF-8字节，替换为?
            result += '?';
        }
    }

    return result;
}

// 生成带时间戳的JSON文件名
std::string GenerateJsonFileName() {
    time_t now = time(0);
    struct tm timeinfo;
    localtime_s(&timeinfo, &now);

    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &timeinfo);

    return std::string("EXIF_Analysis_") + timestamp + ".json";
}

// 保存JSON结果到文件
bool SaveJsonToFile(const std::string& jsonContent, const std::string& fileName) {
    try {
        std::ofstream file(fileName, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            return false;
        }

        file << jsonContent;
        file.close();
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

// 创建只包含ASCII字符的安全JSON
nlohmann::json CreateSafeJson(const nlohmann::json& originalJson) {
    nlohmann::json safeJson;

    try {
        // 递归清理JSON中的所有字符串
        for (auto it = originalJson.begin(); it != originalJson.end(); ++it) {
            const std::string& key = it.key();
            std::string safeKey = CleanUtf8String(key);

            if (it.value().is_string()) {
                std::string originalStr = it.value().get<std::string>();
                safeJson[safeKey] = CleanUtf8String(originalStr);
            } else if (it.value().is_number()) {
                safeJson[safeKey] = it.value();
            } else if (it.value().is_boolean()) {
                safeJson[safeKey] = it.value();
            } else if (it.value().is_object()) {
                safeJson[safeKey] = CreateSafeJson(it.value());
            } else if (it.value().is_array()) {
                nlohmann::json safeArray = nlohmann::json::array();
                for (const auto& item : it.value()) {
                    if (item.is_string()) {
                        safeArray.push_back(CleanUtf8String(item.get<std::string>()));
                    } else if (item.is_object()) {
                        safeArray.push_back(CreateSafeJson(item));
                    } else {
                        safeArray.push_back(item);
                    }
                }
                safeJson[safeKey] = safeArray;
            } else {
                // 对于其他类型，直接使用原值，避免双重转义
                safeJson[safeKey] = it.value();
            }
        }
    } catch (...) {
        // 如果清理过程失败，返回基本错误信息
        safeJson["status"] = "error";
        safeJson["message"] = "Failed to clean JSON data";
    }

    return safeJson;
}

// 安全的JSON转换函数
std::string SafeJsonDump(const nlohmann::json& json) {
    try {
        return json.dump(4);
    } catch (const nlohmann::json::exception& e) {
        // 如果JSON dump失败，创建清理后的版本
        try {
            nlohmann::json safeJson = CreateSafeJson(json);
            return safeJson.dump(4);
        } catch (...) {
            // 最后的备用方案
            nlohmann::json errorJson;
            errorJson["status"] = "error";
            errorJson["message"] = "JSON encoding error";
            errorJson["error_code"] = e.id;
            errorJson["error_message"] = "Failed to encode JSON due to invalid characters";

            try {
                return errorJson.dump(4);
            } catch (...) {
                return "{\"status\":\"error\",\"message\":\"Critical JSON encoding failure\"}";
            }
        }
    }
}

ExifExtractor::ExifExtractor() : m_initialized(false)
{
}

ExifExtractor::~ExifExtractor()
{
    Cleanup();
}

bool ExifExtractor::Initialize()
{
    if (m_initialized)
        return true;

    // 初始化GDI+
    Gdiplus::GdiplusStartupInput gdiplusStartupInput;
    Gdiplus::Status status = Gdiplus::GdiplusStartup(&m_gdiplusToken, &gdiplusStartupInput, NULL);
    if (status != Gdiplus::Ok)
    {
        SetError("Failed to initialize GDI+");
        return false;
    }

    m_initialized = true;
    return true;
}

void ExifExtractor::Cleanup()
{
    if (m_initialized)
    {
        Gdiplus::GdiplusShutdown(m_gdiplusToken);
        m_initialized = false;
    }
}

bool ExifExtractor::ExtractExifInfo(const std::string& filePath, ExifInfo& exifInfo)
{
    if (!m_initialized)
    {
        SetError("ExifExtractor not initialized");
        return false;
    }

    exifInfo.Clear();

    // 转换文件路径为宽字符 - 优先使用UTF-8转换（与文件扫描保持一致）
    int widePathLength = MultiByteToWideChar(CP_UTF8, 0, filePath.c_str(), -1, NULL, 0);
    std::vector<wchar_t> widePath;

    if (widePathLength > 0) {
        widePath.resize(widePathLength);
        int result = MultiByteToWideChar(CP_UTF8, 0, filePath.c_str(), -1, &widePath[0], widePathLength);
        if (result == 0) {
            // UTF-8转换失败，尝试ANSI转换
            widePathLength = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, NULL, 0);
            if (widePathLength > 0) {
                widePath.resize(widePathLength);
                result = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, &widePath[0], widePathLength);
                if (result == 0) {
                    SetError("Failed to convert file path from both UTF-8 and ANSI");
                    return false;
                }
            } else {
                SetError("Failed to convert file path to wide characters");
                return false;
            }
        }
    } else {
        // UTF-8转换失败，尝试ANSI转换
        widePathLength = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, NULL, 0);
        if (widePathLength > 0) {
            widePath.resize(widePathLength);
            int result = MultiByteToWideChar(CP_ACP, 0, filePath.c_str(), -1, &widePath[0], widePathLength);
            if (result == 0) {
                SetError("Failed to convert file path to wide characters");
                return false;
            }
        } else {
            SetError("Failed to convert file path to wide characters");
            return false;
        }
    }

    if (widePath.empty())
    {
        SetError("Failed to convert file path to wide characters");
        return false;
    }

    // 使用GDI+加载图像
    Gdiplus::Image* image = new Gdiplus::Image(&widePath[0]);
    if (!image || image->GetLastStatus() != Gdiplus::Ok)
    {
        std::string errorMsg = "Failed to load image with GDI+";
        if (image) {
            Gdiplus::Status status = image->GetLastStatus();
            errorMsg += " (Status: " + std::to_string(status) + ")";
            delete image;
        }

        // 检查文件是否真的存在
        DWORD attributes = GetFileAttributesW(&widePath[0]);
        if (attributes == INVALID_FILE_ATTRIBUTES) {
            errorMsg += " - File not found";
        } else {
            errorMsg += " - File exists but cannot be loaded";
        }

        SetError(errorMsg);
        return false;
    }

    // 获取图像尺寸
    exifInfo.width = std::to_string(image->GetWidth());
    exifInfo.height = std::to_string(image->GetHeight());

    // 获取属性项数量
    UINT totalBufferSize, numProperties;
    image->GetPropertySize(&totalBufferSize, &numProperties);

    if (numProperties == 0)
    {
        // 没有EXIF数据，设置默认值
        exifInfo.manufacturer = "Unknown";
        exifInfo.model = "Unknown";
        exifInfo.dateTime = "Unknown";
        delete image;
        return true;
    }

    // 获取所有属性项
    Gdiplus::PropertyItem* propertyItems = (Gdiplus::PropertyItem*)malloc(totalBufferSize);
    if (!propertyItems)
    {
        SetError("Failed to allocate memory for property items");
        delete image;
        return false;
    }

    Gdiplus::Status status = image->GetAllPropertyItems(totalBufferSize, numProperties, propertyItems);
    if (status != Gdiplus::Ok)
    {
        SetError("Failed to get property items");
        free(propertyItems);
        delete image;
        return false;
    }

    // 解析EXIF数据
    for (UINT i = 0; i < numProperties; i++)
    {
        Gdiplus::PropertyItem* item = &propertyItems[i];

        switch (item->id)
        {
        case 0x010F: // 制造商
            if (item->type == 2 && item->value && item->length > 0) // ASCII字符串
            {
                std::string rawStr((char*)item->value, item->length - 1); // 排除null终止符
                exifInfo.manufacturer = CleanUtf8String(rawStr);
                if (exifInfo.manufacturer.empty()) {
                    exifInfo.manufacturer = "Unknown";
                }
            }
            break;

        case 0x0110: // 相机型号
            if (item->type == 2 && item->value && item->length > 0) // ASCII字符串
            {
                std::string rawStr((char*)item->value, item->length - 1); // 排除null终止符
                exifInfo.model = CleanUtf8String(rawStr);
                if (exifInfo.model.empty()) {
                    exifInfo.model = "Unknown";
                }
            }
            break;

        case 0x0132: // 拍摄日期时间
            if (item->type == 2 && item->value && item->length > 0) // ASCII字符串
            {
                std::string rawStr((char*)item->value, item->length - 1); // 排除null终止符
                exifInfo.dateTime = CleanUtf8String(rawStr);
                if (exifInfo.dateTime.empty()) {
                    exifInfo.dateTime = "Unknown";
                }
            }
            break;
        }
    }

    free(propertyItems);
    delete image;
    return true;
}

void ExifExtractor::SetError(const std::string& error)
{
    m_lastError = error;
}

bool ExifExtractor::HasGpsInfo(const ExifInfo& exifInfo)
{
    return !exifInfo.gpsLatitude.empty() && !exifInfo.gpsLongitude.empty();
}

std::vector<std::string> ExifExtractor::ScanImageFiles(void(*progressCallback)(const std::string&, int))
{
    std::vector<std::string> imageFiles;
    std::vector<std::string> imageExtensions = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif"};

    if (progressCallback) {
        progressCallback("Scanning for image files...", 5);
    }

    // 获取所有驱动器
    DWORD drives = GetLogicalDrives();
    std::vector<std::string> driveLetters;

    for (int i = 0; i < 26; i++) {
        if (drives & (1 << i)) {
            char driveLetter = 'A' + i;
            std::string drivePath = std::string(1, driveLetter) + ":\\";

            // 检查驱动器类型，只扫描固定驱动器和可移动驱动器
            UINT driveType = GetDriveTypeA(drivePath.c_str());
            if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                driveLetters.push_back(drivePath);
            }
        }
    }

    if (progressCallback) {
        progressCallback("Found " + std::to_string(driveLetters.size()) + " drives to scan", 10);
    }

    int totalProgress = 10;
    int driveCount = (int)driveLetters.size();
    int progressPerDrive = 80 / (driveCount > 0 ? driveCount : 1);

    for (const auto& drive : driveLetters) {
        if (progressCallback) {
            progressCallback("Starting full scan of drive " + drive, totalProgress);
        }

        // 直接从驱动器根目录开始全盘搜索
        std::wstring drivePath = std::wstring(drive.begin(), drive.end());
        std::vector<std::wstring> wideImagePaths;

        if (progressCallback) {
            progressCallback("Performing complete recursive search on " + drive, totalProgress + 5);
        }

        // 使用完全递归搜索整个驱动器
        ScanDirectoryW(drivePath, wideImagePaths);

        if (progressCallback) {
            progressCallback("Found " + std::to_string(wideImagePaths.size()) + " images on " + drive, totalProgress + 10);
        }

        // 转换宽字符路径为UTF-8并添加到结果中
        for (const auto& widePath : wideImagePaths) {
            int pathLength = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
            std::string narrowPath;

            if (pathLength > 0) {
                std::vector<char> pathBuffer(pathLength);
                int convertResult = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
                if (convertResult > 0) {
                    narrowPath = std::string(&pathBuffer[0]);
                }
            }

            if (narrowPath.empty()) {
                pathLength = WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
                if (pathLength > 0) {
                    std::vector<char> pathBuffer(pathLength);
                    WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
                    narrowPath = std::string(&pathBuffer[0]);
                }
            }

            if (!narrowPath.empty()) {
                imageFiles.push_back(narrowPath);
            }
        }



        totalProgress += progressPerDrive;
    }

    if (progressCallback) {
        progressCallback("Found " + std::to_string(imageFiles.size()) + " image files", 90);
    }

    return imageFiles;
}

// 检查是否为图片文件（宽字符版本）
bool ExifExtractor::IsImageFileW(const std::wstring& filename) {
    std::vector<std::wstring> imageExtensions = {
        L".jpg", L".jpeg", L".png", L".bmp", L".gif", L".tiff", L".webp", L".ico"
    };

    // 转换文件名为小写进行比较
    std::wstring lowerFilename = filename;
    std::transform(lowerFilename.begin(), lowerFilename.end(), lowerFilename.begin(), ::towlower);

    for (const auto& ext : imageExtensions) {
        if (lowerFilename.length() >= ext.length() &&
            lowerFilename.compare(lowerFilename.length() - ext.length(), ext.length(), ext) == 0) {
            return true;
        }
    }
    return false;
}

// 宽字符版本的目录扫描函数 - 完全递归搜索
void ExifExtractor::ScanDirectoryW(const std::wstring& directory,
                                   std::vector<std::wstring>& imagePaths) {
    WIN32_FIND_DATAW findFileData;
    HANDLE hFind = INVALID_HANDLE_VALUE;

    // 构建目录搜索模式
    std::wstring searchPath = directory + L"\\*";
    hFind = FindFirstFileW(searchPath.c_str(), &findFileData);

    if (hFind == INVALID_HANDLE_VALUE) {
        return; // 无法打开目录
    }

    do {
        const std::wstring fileName = findFileData.cFileName;

        // 排除 "." 和 ".."
        if (fileName == L"." || fileName == L"..") continue;

        std::wstring fullPath = directory + L"\\" + fileName;

        if (findFileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) {
            // 如果是目录，递归查找
            ScanDirectoryW(fullPath, imagePaths);
        }
        else {
            // 如果是文件，检查是否为图片
            if (IsImageFileW(fileName)) {
                imagePaths.push_back(fullPath);
            }
        }
    } while (FindNextFileW(hFind, &findFileData) != 0);

    FindClose(hFind);
}

// 兼容性包装函数 - 转换为多字节字符串版本
void ExifExtractor::ScanDirectory(const std::string& dirPath,
                                  const std::vector<std::string>& extensions,
                                  std::vector<std::string>& imageFiles)
{
    // 转换输入路径为宽字符
    int widePathLength = MultiByteToWideChar(CP_UTF8, 0, dirPath.c_str(), -1, NULL, 0);
    if (widePathLength == 0) {
        // UTF-8转换失败，尝试ANSI
        widePathLength = MultiByteToWideChar(CP_ACP, 0, dirPath.c_str(), -1, NULL, 0);
        if (widePathLength == 0) {
            return;
        }
    }

    std::vector<wchar_t> wideDirPath(widePathLength);
    int result = MultiByteToWideChar(CP_UTF8, 0, dirPath.c_str(), -1, &wideDirPath[0], widePathLength);
    if (result == 0) {
        // UTF-8转换失败，尝试ANSI
        result = MultiByteToWideChar(CP_ACP, 0, dirPath.c_str(), -1, &wideDirPath[0], widePathLength);
        if (result == 0) {
            return;
        }
    }

    std::wstring wideDirectory(&wideDirPath[0]);
    std::vector<std::wstring> wideImagePaths;

    // 使用宽字符版本扫描（完全递归）
    ScanDirectoryW(wideDirectory, wideImagePaths);

    // 转换结果为多字节字符串
    for (const auto& widePath : wideImagePaths) {
        // 优先使用UTF-8转换
        int pathLength = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
        std::string narrowPath;

        if (pathLength > 0) {
            std::vector<char> pathBuffer(pathLength);
            int convertResult = WideCharToMultiByte(CP_UTF8, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
            if (convertResult > 0) {
                narrowPath = std::string(&pathBuffer[0]);
            }
        }

        // 如果UTF-8转换失败，使用ANSI作为备用
        if (narrowPath.empty()) {
            pathLength = WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, NULL, 0, NULL, NULL);
            if (pathLength > 0) {
                std::vector<char> pathBuffer(pathLength);
                WideCharToMultiByte(CP_ACP, 0, widePath.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
                narrowPath = std::string(&pathBuffer[0]);
            }
        }

        // 只有成功转换的路径才添加到结果中
        if (!narrowPath.empty()) {
            imageFiles.push_back(narrowPath);
        }
    }
}

std::string ExifExtractor::Init_ExifExtractorMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
)
{
    ExifExtractor extractor;

    if (!extractor.Initialize()) {
        nlohmann::json result;
        result["status"] = "error";
        result["message"] = "Failed to initialize EXIF extractor";
        result["error"] = extractor.GetLastError();
        return SafeJsonDump(result);
    }

    if (progressCallback) {
        progressCallback("Starting device image scan and EXIF analysis...", 0);
    }

    if (queryTaskControlCb && queryTaskControlCb(taskId)) {
        nlohmann::json result;
        result["status"] = "cancelled";
        result["message"] = "Task cancelled";
        return SafeJsonDump(result);
    }

    // 第一步：扫描设备上的所有图片文件
    std::vector<std::string> imageFiles;
    try {
        imageFiles = extractor.ScanImageFiles(progressCallback);
    } catch (const std::exception& e) {
        nlohmann::json result;
        result["status"] = "error";
        result["message"] = "Failed to scan image files";
        result["error"] = e.what();
        return SafeJsonDump(result);
    }

    if (queryTaskControlCb && queryTaskControlCb(taskId)) {
        nlohmann::json result;
        result["status"] = "cancelled";
        result["message"] = "Task cancelled during file scan";
        return SafeJsonDump(result);
    }

    if (progressCallback) {
        progressCallback("Analyzing EXIF data for " + std::to_string(imageFiles.size()) + " images...", 95);
    }

    // 如果没有找到图片文件，返回空结果
    if (imageFiles.empty()) {
        nlohmann::json emptyResult;
        emptyResult["status"] = "success";
        emptyResult["message"] = "No image files found on device";
        emptyResult["task_id"] = taskId;
        emptyResult["total_images_found"] = 0;
        emptyResult["statistics"] = nlohmann::json::object();
        emptyResult["statistics"]["total_images"] = 0;
        emptyResult["statistics"]["successful_extractions"] = 0;
        emptyResult["statistics"]["extraction_success_rate"] = 0.0;
        emptyResult["statistics"]["top_manufacturers"] = nlohmann::json::object();
        emptyResult["statistics"]["top_models"] = nlohmann::json::object();
        emptyResult["image_analysis"] = nlohmann::json::array();

        if (progressCallback) {
            progressCallback("No images found to analyze", 100);
        }

        // 保存空结果到文件
        std::string jsonResult = SafeJsonDump(emptyResult);
        std::string fileName = GenerateJsonFileName();
        if (SaveJsonToFile(jsonResult, fileName)) {
            emptyResult["output_file"] = fileName;
            emptyResult["file_saved"] = true;
            if (progressCallback) {
                progressCallback("Empty results saved to " + fileName, 100);
            }
        }

        return SafeJsonDump(emptyResult);
    }

    // 第二步：分析每个图片的EXIF信息
    nlohmann::json result;
    result["status"] = "success";
    result["message"] = "EXIF analysis completed";
    result["task_id"] = taskId;
    result["total_images_found"] = (int)imageFiles.size();

    nlohmann::json imageAnalysis = nlohmann::json::array();
    nlohmann::json statistics;

    // 统计信息
    std::map<std::string, int> manufacturerCount;
    std::map<std::string, int> modelCount;
    int successfulExtractions = 0;
    int totalSize = 0;

    for (size_t i = 0; i < imageFiles.size(); i++) {
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            result["status"] = "cancelled";
            result["message"] = "Task cancelled during EXIF analysis";
            return SafeJsonDump(result);
        }

        const std::string& originalFilePath = imageFiles[i];
        ExifInfo exifInfo;
        // 使用原始路径进行EXIF提取
        bool success = extractor.ExtractExifInfo(originalFilePath, exifInfo);

        nlohmann::json imageData;
        // 对于JSON输出，使用清理后的路径以避免编码问题
        imageData["file_path"] = CleanFilePathString(originalFilePath);
        imageData["success"] = success;

        if (success) {
            successfulExtractions++;
            imageData["manufacturer"] = CleanUtf8String(exifInfo.manufacturer);
            imageData["model"] = CleanUtf8String(exifInfo.model);
            imageData["date_time"] = CleanUtf8String(exifInfo.dateTime);
            imageData["width"] = CleanUtf8String(exifInfo.width);
            imageData["height"] = CleanUtf8String(exifInfo.height);

            // 统计制造商和型号（清理字符串）
            std::string cleanManufacturer = CleanUtf8String(exifInfo.manufacturer);
            std::string cleanModel = CleanUtf8String(exifInfo.model);

            if (!cleanManufacturer.empty() && cleanManufacturer != "Unknown") {
                manufacturerCount[cleanManufacturer]++;
            }
            if (!cleanModel.empty() && cleanModel != "Unknown") {
                modelCount[cleanModel]++;
            }

            // 计算图片尺寸
            try {
                int width = std::stoi(exifInfo.width);
                int height = std::stoi(exifInfo.height);
                totalSize += width * height;
            } catch (...) {
                // 忽略转换错误
            }
        } else {
            imageData["error"] = CleanUtf8String(extractor.GetLastError());
            // 添加调试信息
            imageData["debug_original_path_length"] = (int)originalFilePath.length();
            imageData["debug_path_has_high_bytes"] = false;
            for (unsigned char c : originalFilePath) {
                if (c >= 128) {
                    imageData["debug_path_has_high_bytes"] = true;
                    break;
                }
            }
        }

        imageAnalysis.push_back(imageData);

        // 限制返回的详细信息数量，避免JSON过大
        if (i >= 5000) { // 增加到5000个图片的详细信息，确保覆盖更多目录
            break;
        }
    }

    // 构建统计信息
    try {
        statistics["total_images"] = (int)imageFiles.size();
        statistics["successful_extractions"] = successfulExtractions;

        double successRate = imageFiles.size() > 0 ?
            (double)successfulExtractions / imageFiles.size() * 100.0 : 0.0;
        statistics["extraction_success_rate"] = successRate;

        // 构建制造商统计
        nlohmann::json manufacturers = nlohmann::json::object();
        for (const auto& pair : manufacturerCount) {
            if (!pair.first.empty()) {
                manufacturers[pair.first] = pair.second;
            }
        }
        statistics["top_manufacturers"] = manufacturers;

        // 构建型号统计
        nlohmann::json models = nlohmann::json::object();
        for (const auto& pair : modelCount) {
            if (!pair.first.empty()) {
                models[pair.first] = pair.second;
            }
        }
        statistics["top_models"] = models;

    } catch (const std::exception& e) {
        // 如果统计信息构建失败，提供基本信息
        statistics["total_images"] = (int)imageFiles.size();
        statistics["successful_extractions"] = successfulExtractions;
        statistics["extraction_success_rate"] = 0.0;
        statistics["top_manufacturers"] = nlohmann::json::object();
        statistics["top_models"] = nlohmann::json::object();
        statistics["error"] = "Failed to build detailed statistics";
    }

    try {
        result["statistics"] = statistics;
        result["image_analysis"] = imageAnalysis;

        if (!params.empty()) {
            result["request_params"] = params;
        }

        if (progressCallback) {
            progressCallback("EXIF analysis completed for " + std::to_string(imageFiles.size()) + " images", 100);
        }

        // 生成JSON字符串
        std::string jsonResult = SafeJsonDump(result);

        // 保存结果到JSON文件
        std::string fileName = GenerateJsonFileName();
        if (SaveJsonToFile(jsonResult, fileName)) {
            if (progressCallback) {
                progressCallback("Results saved to " + fileName, 100);
            }

            // 在结果中添加文件保存信息
            try {
                nlohmann::json resultWithFile = nlohmann::json::parse(jsonResult);
                resultWithFile["output_file"] = fileName;
                resultWithFile["file_saved"] = true;
                return SafeJsonDump(resultWithFile);
            } catch (...) {
                // 如果解析失败，返回原始结果
                return jsonResult;
            }
        } else {
            if (progressCallback) {
                progressCallback("Warning: Failed to save results to file", 100);
            }

            // 在结果中添加文件保存失败信息
            try {
                nlohmann::json resultWithFile = nlohmann::json::parse(jsonResult);
                resultWithFile["output_file"] = fileName;
                resultWithFile["file_saved"] = false;
                resultWithFile["save_error"] = "Failed to write JSON file";
                return SafeJsonDump(resultWithFile);
            } catch (...) {
                return jsonResult;
            }
        }

    } catch (const std::exception& e) {
        // 如果JSON构建失败，返回最简化的结果
        try {
            nlohmann::json errorResult;
            errorResult["status"] = "error";
            errorResult["message"] = "Failed to build JSON result";
            errorResult["error"] = CleanUtf8String(std::string(e.what()));
            errorResult["total_images_found"] = (int)imageFiles.size();
            errorResult["successful_extractions"] = successfulExtractions;

            std::string jsonResult = SafeJsonDump(errorResult);

            // 尝试保存错误结果到文件
            std::string fileName = GenerateJsonFileName();
            if (SaveJsonToFile(jsonResult, fileName)) {
                errorResult["output_file"] = fileName;
                errorResult["file_saved"] = true;
            } else {
                errorResult["output_file"] = fileName;
                errorResult["file_saved"] = false;
            }

            return SafeJsonDump(errorResult);
        } catch (...) {
            // 最终备用方案：纯ASCII JSON
            std::ostringstream oss;
            oss << "{"
                << "\"status\":\"error\","
                << "\"message\":\"Critical JSON encoding failure\","
                << "\"total_images_found\":" << imageFiles.size() << ","
                << "\"successful_extractions\":" << successfulExtractions
                << "}";

            // 尝试保存最简结果
            std::string simpleResult = oss.str();
            std::string fileName = GenerateJsonFileName();
            SaveJsonToFile(simpleResult, fileName);

            return simpleResult;
        }
    }
}

std::string ExifManager::Init_ExifInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
)
{
    return ExifExtractor::Init_ExifExtractorMsg(params, progressCallback, taskId, queryTaskControlCb);
}

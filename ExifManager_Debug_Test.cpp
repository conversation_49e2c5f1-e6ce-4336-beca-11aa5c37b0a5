#include "include/ExifManager.h"
#include <iostream>
#include <string>

// 简单的进度回调函数
void debugProgressCallback(const std::string& message, int progress) {
    std::cout << "[DEBUG Progress " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool debugQueryTaskControl(const std::string& taskId) {
    // 总是返回false，表示不取消任务
    return false;
}

int main() {
    std::cout << "=== EXIF Manager Debug Test ===" << std::endl;
    
    std::string taskId = "debug_test_001";
    
    std::cout << "Testing EXIF Manager with error handling..." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            debugProgressCallback,
            taskId,
            debugQueryTaskControl
        );
        
        std::cout << "\n=== Raw JSON Result ===" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        std::cout << "First 500 characters:" << std::endl;
        std::cout << result.substr(0, 500) << std::endl;
        
        // 尝试解析JSON
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            std::cout << "\n=== JSON Parse Success ===" << std::endl;
            std::cout << "Status: " << jsonResult["status"] << std::endl;
            std::cout << "Message: " << jsonResult["message"] << std::endl;
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            if (jsonResult.find("statistics") != jsonResult.end()) {
                std::cout << "Statistics section found" << std::endl;
                auto stats = jsonResult["statistics"];
                
                if (stats.find("total_images") != stats.end()) {
                    std::cout << "  Total images: " << stats["total_images"] << std::endl;
                }
                if (stats.find("successful_extractions") != stats.end()) {
                    std::cout << "  Successful extractions: " << stats["successful_extractions"] << std::endl;
                }
            }
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "\n=== JSON Parse Error ===" << std::endl;
            std::cout << "JSON parse error: " << e.what() << std::endl;
            std::cout << "Error ID: " << e.id << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "\n=== General Error ===" << std::endl;
        std::cout << "Error: " << e.what() << std::endl;
    }
    
    std::cout << "\nDebug test completed. Press any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

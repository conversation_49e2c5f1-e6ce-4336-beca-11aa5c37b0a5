﻿// Tool.cpp : 系统信息提取工具
// 功能：提取WiFi连接记录、Windows服务等系统信息

#define _CRT_SECURE_NO_WARNINGS
#include <iostream>
#include <iomanip>
#include <ctime>
#include <string>
#include <fstream>
#include <nlohmann/json.hpp>
#include "include/WiFiManager.h"
#include "include/ServiceManager.h"
#include "include/ProcessManager.h"
#include "include/ShareManager.h"
#include "include/DriverManager.h"
#include "include/ScreensaverManager.h"
#include "include/FirewallManager.h"
#include "include/PasswordPolicyManager.h"
#include "include/AccountLockoutPolicyManager.h"
#include "include/StartupManager.h"
#include "include/ImageManager.h"
#include "include/NetworkConnectionManager.h"
#include "include/ExifManager.h"
#include "include/SystemLogManager.h"

// 进度回调函数
void progressCallback(const std::string& message, int progress) {
    std::cout << "[Progress " << progress << "%] " << message << std::endl;
}

// 任务控制回调函数（示例实现，总是返回false表示不取消任务）
bool queryTaskControl(const std::string& taskId) {
    // 在实际应用中，这里可以检查是否有取消任务的请求
    // 例如检查文件、数据库、网络请求等
    return false; // false表示继续执行，true表示取消任务
}

int main()
{
    std::cout << "========================================" << std::endl;
    std::cout << "        System Information Tool" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "Version: 15.1 (Device-wide EXIF Analysis Support)" << std::endl;
    std::cout << "Function: Extract WiFi records, Windows services, process list, network shares, drivers, screensavers, firewall policies, password policies, account lockout policies, startup items, network connections, device-wide EXIF metadata analysis, system logs and other system info" << std::endl;
    std::cout << "Note: Some functions may require administrator privileges" << std::endl;
    std::cout << "========================================" << std::endl << std::endl;

    // 显示功能选择菜单
    std::cout << "Please select function to execute:" << std::endl;
    std::cout << "1. WiFi connection records only" << std::endl;
    std::cout << "2. Windows services list only" << std::endl;
    std::cout << "3. Process list only" << std::endl;
    std::cout << "4. Network shares only" << std::endl;
    std::cout << "5. Driver list only" << std::endl;
    std::cout << "6. Screensaver information only" << std::endl;
    std::cout << "7. Firewall policies only" << std::endl;
    std::cout << "8. User accounts only" << std::endl;
    std::cout << "9. Password policies only" << std::endl;
    std::cout << "10. Account lockout policies only" << std::endl;
    std::cout << "11. Startup items only" << std::endl;
    std::cout << "12. Network connections only" << std::endl;
    std::cout << "13. Image conversion (PNG/JPG to Bitmap Base64)" << std::endl;
    std::cout << "14. Device-wide EXIF metadata analysis" << std::endl;
    std::cout << "15. System logs (System/Security/Login/Power)" << std::endl;
    std::cout << "16. Login logs only" << std::endl;
    std::cout << "17. Security logs only" << std::endl;
    std::cout << "18. Power logs (Startup/Shutdown) only" << std::endl;
    std::cout << "19. WiFi and Services" << std::endl;
    std::cout << "20. All system information (WiFi + Services + Processes + Shares + Drivers + Screensavers + Firewall + User Accounts + Password Policy + Account Lockout Policy + Startup Items + Network Connections + System Logs)" << std::endl;
    std::cout << "Enter your choice (1-20): ";

    int choice;
    std::cin >> choice;
    std::cin.ignore(); // 清除输入缓冲区

    std::string params = "";
    std::string taskId = "system_scan_" + std::to_string(std::time(nullptr));
    std::string result;

    std::cout << std::endl << "Starting task with ID: " << taskId << std::endl << std::endl;

    switch (choice) {
        case 1: {
            // 仅WiFi扫描
            std::cout << "=== Starting WiFi Connection Records Scan ===" << std::endl;
            result = WiFiManager::Init_WifiInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== WiFi Connection Records (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 2: {
            // 仅Windows服务扫描
            std::cout << "=== Starting Windows Services Scan ===" << std::endl;
            result = ServiceManager::Init_ServiceInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Windows Services List (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 3: {
            // 仅进程列表扫描
            std::cout << "=== Starting Process List Scan ===" << std::endl;
            result = ProcessManager::Init_ProcessInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Process List (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 4: {
            // 仅网络共享扫描
            std::cout << "=== Starting Network Shares Scan ===" << std::endl;
            result = ShareManager::Init_ShareInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Network Shares List (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 5: {
            // 仅驱动程序扫描
            std::cout << "=== Starting Driver List Scan ===" << std::endl;
            result = DriverManager::Init_DriverInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Driver List (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 6: {
            // 仅屏保信息扫描
            std::cout << "=== Starting Screensaver Information Scan ===" << std::endl;
            result = ScreensaverManager::Init_ScreensaverInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Screensaver Information (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 7: {
            // 仅防火墙策略扫描
            std::cout << "=== Starting Firewall Policy Scan ===" << std::endl;
            result = FirewallManager::Init_FirewallInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Firewall Policy Information (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 8: {
            // 仅用户账户扫描
            std::cout << "=== Starting User Account Scan ===" << std::endl;
            result = PasswordPolicyManager::Init_UserAccountInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== User Account Information (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 9: {
            // 仅密码策略扫描
            std::cout << "=== Starting Password Policy Scan ===" << std::endl;
            result = PasswordPolicyManager::Init_PasswordPolicyInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Password Policy Information (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 10: {
            // 仅账户锁定策略扫描
            std::cout << "=== Starting Account Lockout Policy Scan ===" << std::endl;
            result = AccountLockoutPolicyManager::Init_AccountLockoutPolicyInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Account Lockout Policy Information (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 11: {
            // 仅启动项扫描
            std::cout << "=== Starting Startup Items Scan ===" << std::endl;
            result = StartupManager::Init_StartupInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Startup Items Information (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 12: {
            // 仅网络连接扫描
            std::cout << "=== Starting Network Connections Scan ===" << std::endl;
            result = NetworkConnectionManager::Init_NetworkConnectionInfoMsg(params, progressCallback, taskId, queryTaskControl);
            std::cout << std::endl << "=== Network Connections Information (JSON Format) ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 13: {
            // 图片转换功能
            std::cout << "=== Starting Image Conversion ===" << std::endl;
            std::cout << "Please choose conversion mode:" << std::endl;
            std::cout << "1. Convert single image file" << std::endl;
            std::cout << "2. Convert all images in directory" << std::endl;
            std::cout << "Enter your choice (1-2): ";

            int conversionChoice;
            std::cin >> conversionChoice;
            std::cin.ignore(); // 清除输入缓冲区

            std::string conversionParams;
            if (conversionChoice == 1) {
                std::cout << "Enter image file path: ";
                std::string imagePath;
                std::getline(std::cin, imagePath);

                nlohmann::json paramJson;
                paramJson["image_path"] = imagePath;
                conversionParams = paramJson.dump();

            } else if (conversionChoice == 2) {
                std::cout << "Enter directory path: ";
                std::string directoryPath;
                std::getline(std::cin, directoryPath);

                nlohmann::json paramJson;
                paramJson["directory_path"] = directoryPath;
                conversionParams = paramJson.dump();

            } else {
                std::cout << "Invalid choice. Using default single file mode." << std::endl;
                conversionParams = "";
            }

            std::cout << std::endl << "Converting image(s)..." << std::endl;
            result = ImageManager::Init_ImageConversionMsg(conversionParams, progressCallback, taskId, queryTaskControl);

            std::cout << std::endl << "=== Image Conversion Results ===" << std::endl;

            // 解析结果以显示保存信息
            try {
                nlohmann::json resultJson = nlohmann::json::parse(result);

                std::cout << "Status: " << resultJson["status"] << std::endl;
                std::cout << "Message: " << resultJson["message"] << std::endl;

                if (resultJson.contains("output_file")) {
                    auto outputInfo = resultJson["output_file"];
                    std::cout << "Output file: " << outputInfo["filename"] << std::endl;
                    std::cout << "Save status: " << (outputInfo["save_success"] ? "SUCCESS" : "FAILED") << std::endl;
                    std::cout << "Save message: " << outputInfo["save_message"] << std::endl;
                }

                if (resultJson.contains("image_info")) {
                    auto imageInfo = resultJson["image_info"];
                    std::cout << "Conversion success: " << imageInfo["conversion_success"] << std::endl;
                    if (imageInfo["conversion_success"] == true) {
                        std::string base64Data = imageInfo["base64_data"];
                        std::cout << "Base64 data length: " << base64Data.length() << " characters" << std::endl;
                    }
                }

                if (resultJson.contains("images")) {
                    auto images = resultJson["images"];
                    std::cout << "Total images processed: " << images.size() << std::endl;
                }

            } catch (const std::exception& e) {
                std::cout << "Failed to parse result JSON: " << e.what() << std::endl;
            }

            std::cout << std::endl << "=== Full JSON Result ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 14: {
            // EXIF元数据分析 - 设备级扫描
            std::cout << "=== Starting Device-wide EXIF Metadata Analysis ===" << std::endl;
            std::cout << "This will scan all drives for image files and extract EXIF data" << std::endl;
            std::cout << "Supported formats: JPG, PNG, BMP, TIFF, GIF" << std::endl;
            std::cout << "Please wait, this may take several minutes..." << std::endl;

            std::cout << std::endl << "Starting device-wide image scan and EXIF analysis..." << std::endl;
            result = ExifManager::Init_ExifInfoMsg("", progressCallback, taskId, queryTaskControl);

            std::cout << std::endl << "=== Device EXIF Analysis Results ===" << std::endl;

            // 解析结果以显示统计信息
            try {
                nlohmann::json resultJson = nlohmann::json::parse(result);

                if (resultJson.find("statistics") != resultJson.end()) {
                    auto stats = resultJson["statistics"];
                    std::cout << "=== Analysis Statistics ===" << std::endl;

                    // 安全地访问统计字段
                    if (stats.find("total_images") != stats.end()) {
                        std::cout << "Total images found: " << stats["total_images"] << std::endl;
                    }
                    if (stats.find("successful_extractions") != stats.end()) {
                        std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                    }
                    if (stats.find("extraction_success_rate") != stats.end()) {
                        std::cout << "Success rate: " << std::fixed << std::setprecision(1)
                                  << stats["extraction_success_rate"].get<double>() << "%" << std::endl;
                    }

                    if (stats.find("top_manufacturers") != stats.end()) {
                        std::cout << "\nTop camera manufacturers:" << std::endl;
                        for (auto it = stats["top_manufacturers"].begin(); it != stats["top_manufacturers"].end(); ++it) {
                            std::cout << "  " << it.key() << ": " << it.value() << " images" << std::endl;
                        }
                    }

                    if (stats.find("top_models") != stats.end()) {
                        std::cout << "\nTop camera models:" << std::endl;
                        for (auto it = stats["top_models"].begin(); it != stats["top_models"].end(); ++it) {
                            std::cout << "  " << it.key() << ": " << it.value() << " images" << std::endl;
                        }
                    }
                    std::cout << std::endl;
                }

                if (resultJson.find("image_analysis") != resultJson.end()) {
                    auto imageAnalysis = resultJson["image_analysis"];
                    std::cout << "Detailed analysis available for " << imageAnalysis.size() << " image(s)" << std::endl;
                }

                // 检查是否保存了文件
                if (resultJson.find("output_file") != resultJson.end()) {
                    std::string outputFile = resultJson["output_file"];
                    bool fileSaved = false;
                    if (resultJson.find("file_saved") != resultJson.end()) {
                        fileSaved = resultJson["file_saved"].get<bool>();
                    }

                    if (fileSaved) {
                        std::cout << "\n✓ Results saved to file: " << outputFile << std::endl;
                    } else {
                        std::cout << "\n⚠ Failed to save results to file: " << outputFile << std::endl;
                    }
                }

            } catch (const std::exception& e) {
                std::cout << "Failed to parse EXIF result JSON: " << e.what() << std::endl;
                std::cout << "Raw result (first 200 chars): " << result.substr(0, 200) << "..." << std::endl;
            }

            std::cout << std::endl << "=== Full EXIF JSON Result ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 15: {
            // 系统日志分析
            std::cout << "=== Starting System Logs Analysis ===" << std::endl;

            std::cout << "Please select log type:" << std::endl;
            std::cout << "1. System logs" << std::endl;
            std::cout << "2. Login logs" << std::endl;
            std::cout << "3. Security logs" << std::endl;
            std::cout << "4. Power logs (Startup/Shutdown)" << std::endl;
            std::cout << "5. All logs" << std::endl;
            std::cout << "Enter your choice (1-5): ";

            int logChoice;
            std::cin >> logChoice;
            std::cin.ignore();

            // 构建查询参数
            nlohmann::json logParams;
            logParams["max_entries"] = 100;
            logParams["days_back"] = 7;

            std::string logResult;

            switch (logChoice) {
                case 1:
                    std::cout << "Analyzing system logs..." << std::endl;
                    logResult = SystemLogManager::Init_SystemLogInfoMsg(logParams.dump(), progressCallback, taskId + "_system", queryTaskControl);
                    break;
                case 2:
                    std::cout << "Analyzing login logs..." << std::endl;
                    logResult = SystemLogManager::Init_LoginLogInfoMsg(logParams.dump(), progressCallback, taskId + "_login", queryTaskControl);
                    break;
                case 3:
                    std::cout << "Analyzing security logs..." << std::endl;
                    logResult = SystemLogManager::Init_SecurityLogInfoMsg(logParams.dump(), progressCallback, taskId + "_security", queryTaskControl);
                    break;
                case 4:
                    std::cout << "Analyzing power logs..." << std::endl;
                    logResult = SystemLogManager::Init_PowerLogInfoMsg(logParams.dump(), progressCallback, taskId + "_power", queryTaskControl);
                    break;
                case 5: {
                    std::cout << "Analyzing all logs..." << std::endl;

                    std::string sysResult = SystemLogManager::Init_SystemLogInfoMsg(logParams.dump(), progressCallback, taskId + "_sys", queryTaskControl);
                    std::string loginResult = SystemLogManager::Init_LoginLogInfoMsg(logParams.dump(), progressCallback, taskId + "_login", queryTaskControl);
                    std::string secResult = SystemLogManager::Init_SecurityLogInfoMsg(logParams.dump(), progressCallback, taskId + "_sec", queryTaskControl);
                    std::string powerResult = SystemLogManager::Init_PowerLogInfoMsg(logParams.dump(), progressCallback, taskId + "_power", queryTaskControl);

                    // 合并结果
                    nlohmann::json combinedResult;
                    combinedResult["status"] = "success";
                    combinedResult["message"] = "All system logs analysis completed";
                    combinedResult["system_logs"] = nlohmann::json::parse(sysResult);
                    combinedResult["login_logs"] = nlohmann::json::parse(loginResult);
                    combinedResult["security_logs"] = nlohmann::json::parse(secResult);
                    combinedResult["power_logs"] = nlohmann::json::parse(powerResult);

                    logResult = combinedResult.dump(4);
                    break;
                }
                default:
                    std::cout << "Invalid choice, using system logs..." << std::endl;
                    logResult = SystemLogManager::Init_SystemLogInfoMsg(logParams.dump(), progressCallback, taskId + "_system", queryTaskControl);
                    break;
            }

            std::cout << std::endl << "=== System Logs Analysis Results ===" << std::endl;

            // 解析结果以显示统计信息
            try {
                nlohmann::json resultJson = nlohmann::json::parse(logResult);

                if (resultJson.contains("statistics")) {
                    auto stats = resultJson["statistics"];
                    std::cout << "=== Log Statistics ===" << std::endl;
                    if (stats.contains("total_entries")) {
                        std::cout << "Total log entries: " << stats["total_entries"] << std::endl;
                    }
                    if (stats.contains("scan_time")) {
                        std::cout << "Scan time: " << stats["scan_time"] << std::endl;
                    }
                    std::cout << std::endl;
                }

                // 显示部分日志条目
                if (resultJson.contains("logs")) {
                    auto logs = resultJson["logs"];
                    std::cout << "Found " << logs.size() << " log entries (showing first 3):" << std::endl;

                    int count = 0;
                    for (const auto& log : logs) {
                        if (count >= 3) break;
                        std::cout << "[" << (count + 1) << "] ";
                        std::cout << "Time: " << log["time_generated"] << " | ";
                        std::cout << "Event ID: " << log["event_id"] << " | ";
                        std::cout << "Level: " << log["level"] << std::endl;
                        count++;
                    }
                }

            } catch (const std::exception& e) {
                std::cout << "Failed to parse log result JSON: " << e.what() << std::endl;
            }

            std::cout << std::endl << "=== Full System Logs JSON Result ===" << std::endl;
            std::cout << logResult << std::endl;
            break;
        }
        case 16: {
            // 仅登录日志
            std::cout << "=== Starting Login Logs Analysis ===" << std::endl;

            nlohmann::json loginParams;
            loginParams["max_entries"] = 50;
            loginParams["days_back"] = 30;

            result = SystemLogManager::Init_LoginLogInfoMsg(loginParams.dump(), progressCallback, taskId + "_login", queryTaskControl);

            std::cout << std::endl << "=== Login Logs Results ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 17: {
            // 仅安全日志
            std::cout << "=== Starting Security Logs Analysis ===" << std::endl;

            nlohmann::json secParams;
            secParams["max_entries"] = 100;
            secParams["days_back"] = 7;

            result = SystemLogManager::Init_SecurityLogInfoMsg(secParams.dump(), progressCallback, taskId + "_security", queryTaskControl);

            std::cout << std::endl << "=== Security Logs Results ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 18: {
            // 仅开关机日志
            std::cout << "=== Starting Power Logs Analysis ===" << std::endl;

            nlohmann::json powerParams;
            powerParams["max_entries"] = 50;
            powerParams["days_back"] = 30;

            result = SystemLogManager::Init_PowerLogInfoMsg(powerParams.dump(), progressCallback, taskId + "_power", queryTaskControl);

            std::cout << std::endl << "=== Power Logs Results ===" << std::endl;
            std::cout << result << std::endl;
            break;
        }
        case 19: {
            // WiFi和Windows服务扫描
            std::cout << "=== Starting WiFi and Services Scan ===" << std::endl;

            // WiFi扫描
            std::cout << "[1/2] Scanning WiFi connection records..." << std::endl;
            std::string wifi_result = WiFiManager::Init_WifiInfoMsg(params, progressCallback, taskId + "_wifi", queryTaskControl);

            // Windows服务扫描
            std::cout << "[2/2] Scanning Windows services..." << std::endl;
            std::string service_result = ServiceManager::Init_ServiceInfoMsg(params, progressCallback, taskId + "_services", queryTaskControl);

            // 合并结果
            std::cout << std::endl << "=== WiFi Connection Records ===" << std::endl;
            std::cout << wifi_result << std::endl;
            std::cout << std::endl << "=== Windows Services List ===" << std::endl;
            std::cout << service_result << std::endl;
            break;
        }
        case 20: {
            // 全面扫描：WiFi、Windows服务、进程列表、网络共享、驱动程序、屏保信息、防火墙策略、用户账户、密码策略、账户锁定策略、启动项、网络连接、EXIF元数据和系统日志
            std::cout << "=== Starting Comprehensive System Scan (Including EXIF and System Logs) ===" << std::endl;

            // WiFi扫描
            std::cout << "[1/14] Scanning WiFi connection records..." << std::endl;
            std::string wifi_result = WiFiManager::Init_WifiInfoMsg(params, progressCallback, taskId + "_wifi", queryTaskControl);

            // Windows服务扫描
            std::cout << "[2/14] Scanning Windows services..." << std::endl;
            std::string service_result = ServiceManager::Init_ServiceInfoMsg(params, progressCallback, taskId + "_services", queryTaskControl);

            // 进程列表扫描
            std::cout << "[3/14] Scanning process list..." << std::endl;
            std::string process_result = ProcessManager::Init_ProcessInfoMsg(params, progressCallback, taskId + "_processes", queryTaskControl);

            // 网络共享扫描
            std::cout << "[4/14] Scanning network shares..." << std::endl;
            std::string share_result = ShareManager::Init_ShareInfoMsg(params, progressCallback, taskId + "_shares", queryTaskControl);

            // 驱动程序扫描
            std::cout << "[5/14] Scanning driver list..." << std::endl;
            std::string driver_result = DriverManager::Init_DriverInfoMsg(params, progressCallback, taskId + "_drivers", queryTaskControl);

            // 屏保信息扫描
            std::cout << "[6/14] Scanning screensaver information..." << std::endl;
            std::string screensaver_result = ScreensaverManager::Init_ScreensaverInfoMsg(params, progressCallback, taskId + "_screensavers", queryTaskControl);

            // 防火墙策略扫描
            std::cout << "[7/14] Scanning firewall policies..." << std::endl;
            std::string firewall_result = FirewallManager::Init_FirewallInfoMsg(params, progressCallback, taskId + "_firewall", queryTaskControl);

            // 用户账户扫描
            std::cout << "[8/14] Scanning user accounts..." << std::endl;
            std::string user_account_result = PasswordPolicyManager::Init_UserAccountInfoMsg(params, progressCallback, taskId + "_user_accounts", queryTaskControl);

            // 密码策略扫描
            std::cout << "[9/14] Scanning password policies..." << std::endl;
            std::string password_result = PasswordPolicyManager::Init_PasswordPolicyInfoMsg(params, progressCallback, taskId + "_password", queryTaskControl);

            // 账户锁定策略扫描
            std::cout << "[10/14] Scanning account lockout policies..." << std::endl;
            std::string lockout_result = AccountLockoutPolicyManager::Init_AccountLockoutPolicyInfoMsg(params, progressCallback, taskId + "_lockout", queryTaskControl);

            // 启动项扫描
            std::cout << "[11/14] Scanning startup items..." << std::endl;
            std::string startup_result = StartupManager::Init_StartupInfoMsg(params, progressCallback, taskId + "_startup", queryTaskControl);

            // 网络连接扫描
            std::cout << "[12/14] Scanning network connections..." << std::endl;
            std::string network_result = NetworkConnectionManager::Init_NetworkConnectionInfoMsg(params, progressCallback, taskId + "_network", queryTaskControl);

            // EXIF元数据扫描
            std::cout << "[13/14] Scanning device-wide EXIF metadata..." << std::endl;
            std::string exif_result = ExifManager::Init_ExifInfoMsg("", progressCallback, taskId + "_exif", queryTaskControl);

            // 系统日志扫描
            std::cout << "[14/14] Scanning system logs..." << std::endl;
            nlohmann::json logParams;
            logParams["max_entries"] = 50;
            logParams["days_back"] = 7;
            std::string system_logs_result = SystemLogManager::Init_SystemLogInfoMsg(logParams.dump(), progressCallback, taskId + "_logs", queryTaskControl);

            // 合并结果
            std::cout << std::endl << "=== WiFi Connection Records ===" << std::endl;
            std::cout << wifi_result << std::endl;
            std::cout << std::endl << "=== Windows Services List ===" << std::endl;
            std::cout << service_result << std::endl;
            std::cout << std::endl << "=== Process List ===" << std::endl;
            std::cout << process_result << std::endl;
            std::cout << std::endl << "=== Network Shares List ===" << std::endl;
            std::cout << share_result << std::endl;
            std::cout << std::endl << "=== Driver List ===" << std::endl;
            std::cout << driver_result << std::endl;
            std::cout << std::endl << "=== Screensaver Information ===" << std::endl;
            std::cout << screensaver_result << std::endl;
            std::cout << std::endl << "=== Firewall Policy Information ===" << std::endl;
            std::cout << firewall_result << std::endl;
            std::cout << std::endl << "=== User Account Information ===" << std::endl;
            std::cout << user_account_result << std::endl;
            std::cout << std::endl << "=== Password Policy Information ===" << std::endl;
            std::cout << password_result << std::endl;
            std::cout << std::endl << "=== Account Lockout Policy Information ===" << std::endl;
            std::cout << lockout_result << std::endl;
            std::cout << std::endl << "=== Startup Items Information ===" << std::endl;
            std::cout << startup_result << std::endl;
            std::cout << std::endl << "=== Network Connections Information ===" << std::endl;
            std::cout << network_result << std::endl;
            std::cout << std::endl << "=== EXIF Metadata Analysis Results ===" << std::endl;
            std::cout << exif_result << std::endl;

            // 检查EXIF结果是否保存了文件
            try {
                nlohmann::json exifJson = nlohmann::json::parse(exif_result);
                if (exifJson.find("output_file") != exifJson.end()) {
                    std::string outputFile = exifJson["output_file"];
                    bool fileSaved = false;
                    if (exifJson.find("file_saved") != exifJson.end()) {
                        fileSaved = exifJson["file_saved"].get<bool>();
                    }

                    if (fileSaved) {
                        std::cout << "\n✓ EXIF analysis results saved to: " << outputFile << std::endl;
                    } else {
                        std::cout << "\n⚠ Failed to save EXIF results to: " << outputFile << std::endl;
                    }
                }
            } catch (...) {
                // 忽略JSON解析错误
            }

            std::cout << std::endl << "=== System Logs Information ===" << std::endl;
            std::cout << system_logs_result << std::endl;
            break;
        }
        default: {
            std::cout << "Invalid choice, program exiting." << std::endl;
            return 1;
        }
    }

    std::cout << std::endl << "Scan completed! Press any key to exit...";
    std::cin.get();

    return 0;
}


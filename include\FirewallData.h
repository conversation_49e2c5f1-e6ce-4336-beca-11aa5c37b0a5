﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// Windows防火墙规则数据结构
struct FirewallRuleData {
    std::string name;                   // 规则名称
    std::string description;            // 规则描述
    std::string group_name;             // 规则组名
    bool enabled;                       // 是否启用
    std::string direction;              // 方向 (Inbound/Outbound)
    std::string action;                 // 动作 (Allow/Block)
    std::string protocol;               // 协议 (TCP/UDP/ICMP/Any)
    std::string local_addresses;        // 本地地址
    std::string remote_addresses;       // 远程地址
    std::string local_ports;            // 本地端口
    std::string remote_ports;           // 远程端口
    std::string service_name;           // 服务名称
    std::string profiles;               // 配置文件 (Domain/Private/Public)
    std::string interface_types;        // 接口类型
    std::string edge_traversal;         // 边缘遍历设置
    bool is_builtin;                    // 是否为内置规则
    std::string modification_time;      // 修改时间
    std::string rule_id;                // 规则ID
    std::string security;               // 安全设置
    std::string encryption;             // 加密设置
    std::string source;                 // 规则来源

    FirewallRuleData() : enabled(false), is_builtin(false) {}
};

// Windows防火墙配置文件数据结构
struct FirewallProfileData {
    std::string name;                   // 配置文件名称 (Domain/Private/Public)
    bool enabled;                       // 是否启用
    bool default_inbound_action_block;  // 默认入站动作是否为阻止
    bool default_outbound_action_block; // 默认出站动作是否为阻止
    bool notifications_disabled;        // 是否禁用通知
    bool unicast_responses_disabled;    // 是否禁用单播响应
    bool log_allowed;                   // 是否记录允许的连接
    bool log_blocked;                   // 是否记录被阻止的连接
    std::string log_file_path;          // 日志文件路径
    int log_max_size_kb;               // 日志最大大小(KB)
    int active_rules_count;            // 活动规则数量
    std::string status;                // 状态描述

    FirewallProfileData() : enabled(false), default_inbound_action_block(true), 
                           default_outbound_action_block(false), notifications_disabled(false),
                           unicast_responses_disabled(false), log_allowed(false), 
                           log_blocked(false), log_max_size_kb(4096), active_rules_count(0) {}
};

// 防火墙服务信息
struct FirewallServiceData {
    std::string service_name;           // 服务名称
    std::string display_name;           // 显示名称
    std::string status;                 // 服务状态
    std::string startup_type;           // 启动类型
    std::string version;                // 版本信息
    std::string description;            // 描述
    bool is_running;                    // 是否正在运行
    std::string executable_path;        // 可执行文件路径
    std::string dependencies;           // 依赖服务

    FirewallServiceData() : is_running(false) {}
};

// 防火墙统计信息
struct FirewallStatistics {
    int total_rules;                    // 总规则数
    int enabled_rules;                  // 启用的规则数
    int disabled_rules;                 // 禁用的规则数
    int inbound_rules;                  // 入站规则数
    int outbound_rules;                 // 出站规则数
    int allow_rules;                    // 允许规则数
    int block_rules;                    // 阻止规则数
    int builtin_rules;                  // 内置规则数
    int custom_rules;                   // 自定义规则数
    int domain_profile_rules;           // 域配置文件规则数
    int private_profile_rules;          // 专用配置文件规则数
    int public_profile_rules;           // 公用配置文件规则数
    std::string firewall_status;        // 防火墙总体状态
    std::time_t scan_time;              // 扫描时间
    std::string scan_duration;          // 扫描耗时

    FirewallStatistics() : total_rules(0), enabled_rules(0), disabled_rules(0),
                          inbound_rules(0), outbound_rules(0), allow_rules(0), block_rules(0),
                          builtin_rules(0), custom_rules(0), domain_profile_rules(0),
                          private_profile_rules(0), public_profile_rules(0), scan_time(0) {}
};

// 防火墙事件日志条目
struct FirewallLogEntry {
    std::string timestamp;              // 时间戳
    std::string action;                 // 动作 (ALLOW/DROP)
    std::string protocol;               // 协议
    std::string src_ip;                 // 源IP
    std::string dst_ip;                 // 目标IP
    std::string src_port;               // 源端口
    std::string dst_port;               // 目标端口
    std::string size;                   // 数据包大小
    std::string tcpflags;               // TCP标志
    std::string tcpsyn;                 // TCP SYN
    std::string tcpack;                 // TCP ACK
    std::string tcpwin;                 // TCP窗口
    std::string icmptype;               // ICMP类型
    std::string icmpcode;               // ICMP代码
    std::string info;                   // 附加信息
    std::string path;                   // 应用程序路径

    FirewallLogEntry() {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FirewallRuleData,
    name, description, group_name, enabled, direction, action,
    protocol, local_addresses, remote_addresses, local_ports, remote_ports,
    service_name, profiles, interface_types, edge_traversal,
    is_builtin, modification_time, rule_id, security,
    encryption, source)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FirewallProfileData,
    name, enabled, default_inbound_action_block, default_outbound_action_block,
    notifications_disabled, unicast_responses_disabled, log_allowed, log_blocked,
    log_file_path, log_max_size_kb, active_rules_count, status)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FirewallServiceData,
    service_name, display_name, status, startup_type, version,
    description, is_running, executable_path, dependencies)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FirewallStatistics,
    total_rules, enabled_rules, disabled_rules, inbound_rules, outbound_rules,
    allow_rules, block_rules, builtin_rules, custom_rules, domain_profile_rules,
    private_profile_rules, public_profile_rules, firewall_status, scan_time, scan_duration)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FirewallLogEntry,
    timestamp, action, protocol, src_ip, dst_ip, src_port, dst_port,
    size, tcpflags, tcpsyn, tcpack, tcpwin, icmptype, icmpcode, info, path)

﻿#include "../include/ProcessManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <algorithm>
#include <tlhelp32.h>
#include <psapi.h>
#include <sddl.h>
#include <shellapi.h>
#include <shlobj.h>
#include <gdiplus.h>

// 确保使用正确的Windows版本
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0600  // Windows Vista+
#endif

#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "gdiplus.lib")

#pragma comment(lib, "psapi.lib")

ProcessManager::ProcessManager() : m_initialized(false), m_useFastMode(true) {
}

ProcessManager::~ProcessManager() {
    Cleanup();
}

bool ProcessManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_initialized = true;
    return true;
}

void ProcessManager::Cleanup() {
    m_initialized = false;
}

std::vector<ProcessData> ProcessManager::GetAllProcesses() {
    if (m_useFastMode) {
        return GetAllProcessesFast();
    } else {
        return GetAllProcessesDetailed();
    }
}

// 快速版本：只获取基本信息
std::vector<ProcessData> ProcessManager::GetAllProcessesFast() {
    std::vector<ProcessData> allProcesses;

    if (!m_initialized) {
        return allProcesses;
    }

    std::cout << "Using fast process enumeration..." << std::endl;

    // 创建进程快照
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        std::cout << "Failed to create process snapshot: " << GetLastError() << std::endl;
        return allProcesses;
    }

    PROCESSENTRY32 processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32);

    // 获取第一个进程
    if (Process32First(snapshot, &processEntry)) {
        do {
            ProcessData processData;

            // 基本信息（快速获取）
            processData.pid = processEntry.th32ProcessID;
            processData.name = ConvertToString(processEntry.szExeFile);
            processData.status = "Running";

            // 获取进程详细信息
            HANDLE processHandle = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processEntry.th32ProcessID);
            if (processHandle != nullptr) {
                // 获取内存信息
                PROCESS_MEMORY_COUNTERS memCounters;
                if (GetProcessMemoryInfo(processHandle, &memCounters, sizeof(memCounters))) {
                    processData.memory = FormatMemorySize(memCounters.WorkingSetSize) + " MB";
                } else {
                    processData.memory = "0.0 MB";
                }

                // 获取进程路径
                processData.path = GetProcessPath(processHandle);

                // 获取进程创建时间
                processData.create_time = GetProcessCreateTime(processHandle);

                CloseHandle(processHandle);
            } else {
                processData.memory = "Access Denied";
                processData.path = "Access Denied";
                processData.create_time = "Access Denied";
            }

            // 获取进程图标
            processData.icon = GetProcessIcon(processData.path);

            allProcesses.push_back(processData);

        } while (Process32Next(snapshot, &processEntry));
    }

    CloseHandle(snapshot);
    std::cout << "Fast enumeration completed. Found " << allProcesses.size() << " processes." << std::endl;
    return allProcesses;
}

// 完整版本：获取所有详细信息（保留原有功能）
std::vector<ProcessData> ProcessManager::GetAllProcessesDetailed() {
    std::vector<ProcessData> allProcesses;

    if (!m_initialized) {
        return allProcesses;
    }

    std::cout << "Using detailed process enumeration..." << std::endl;

    // 创建进程快照
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        std::cout << "Failed to create process snapshot: " << GetLastError() << std::endl;
        return allProcesses;
    }

    PROCESSENTRY32 processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32);

    // 获取第一个进程
    if (Process32First(snapshot, &processEntry)) {
        do {
            ProcessData processData;

            // 基本信息
            processData.pid = processEntry.th32ProcessID;
            processData.name = ConvertToString(processEntry.szExeFile);

            // 获取详细信息
            ProcessData detailedInfo = GetProcessDetails(processEntry.th32ProcessID, processData.name);
            if (!detailedInfo.name.empty()) {
                processData.status = detailedInfo.status;
                processData.memory = detailedInfo.memory;
                processData.path = detailedInfo.path;
                processData.create_time = detailedInfo.create_time;
                processData.icon = detailedInfo.icon;
            } else {
                // 如果无法获取详细信息，设置默认值
                processData.status = "Running";
                processData.memory = "0.0 MB";
                processData.path = "Unknown";
                processData.create_time = "Unknown";
                processData.icon = "default";
            }

            allProcesses.push_back(processData);

        } while (Process32Next(snapshot, &processEntry));
    }

    CloseHandle(snapshot);
    std::cout << "Detailed enumeration completed. Found " << allProcesses.size() << " processes." << std::endl;
    return allProcesses;
}

nlohmann::json ProcessManager::GetProcessesInfoAsJson() {
    nlohmann::json result;

    // 获取所有进程
    std::vector<ProcessData> processes = GetAllProcesses();
    result["processes"] = processes;

    // 统计信息
    int runningCount = 0;
    double totalMemoryUsage = 0.0;

    for (const auto& process : processes) {
        if (process.status == "Running" || process.status.empty()) {
            runningCount++;
        }
        // 从memory字段中提取数值（格式为"XX.XX MB"）
        std::string memStr = process.memory;
        if (!memStr.empty() && memStr.find("MB") != std::string::npos) {
            try {
                double memValue = std::stod(memStr.substr(0, memStr.find(" ")));
                totalMemoryUsage += memValue;
            } catch (...) {
                // 忽略解析错误
            }
        }
    }

    // 添加元数据
    result["metadata"] = {
        {"total_processes", processes.size()},
        {"running_processes", runningCount},
        {"total_memory_usage_mb", std::to_string(totalMemoryUsage) + " MB"},
        {"scan_time", std::time(nullptr)},
        {"version", "1.0"}
    };

    return result;
}

nlohmann::json ProcessManager::GetProcessesInfoAsJsonFast() {
    nlohmann::json result;

    // 强制使用快速模式
    std::vector<ProcessData> processes = GetAllProcessesFast();
    result["processes"] = processes;

    // 快速统计信息（只统计基本信息）
    int runningCount = 0;
    double totalMemoryUsage = 0.0;

    for (const auto& process : processes) {
        if (process.status == "Running" || process.status.empty()) {
            runningCount++;
        }
        // 从memory字段中提取数值（格式为"XX.XX MB"）
        std::string memStr = process.memory;
        if (!memStr.empty() && memStr.find("MB") != std::string::npos) {
            try {
                double memValue = std::stod(memStr.substr(0, memStr.find(" ")));
                totalMemoryUsage += memValue;
            } catch (...) {
                // 忽略解析错误
            }
        }
    }

    // 添加元数据
    result["metadata"] = {
        {"total_processes", processes.size()},
        {"running_processes", runningCount},
        {"total_memory_usage_mb", std::to_string(totalMemoryUsage) + " MB"},
        {"scan_time", std::time(nullptr)},
        {"version", "1.0"},
        {"scan_mode", "fast"},
        {"note", "Fast mode: CPU, disk, network, and user info not collected for performance"}
    };

    return result;
}

bool ProcessManager::SaveProcessesInfoToFile(const std::string& filename) {
    try {
        // 获取进程信息的JSON数据
        nlohmann::json processInfo = GetProcessesInfoAsJson();

        // 打开文件进行写入
        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << "Failed to open file for writing: " << filename << std::endl;
            return false;
        }

        // 将JSON数据写入文件（格式化输出，缩进为4个空格）
        file << processInfo.dump(4);
        file.close();

        std::cout << "Processes information saved to: " << filename << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cout << "Error saving processes information to file: " << e.what() << std::endl;
        return false;
    }
}

// 封装的进程信息获取接口实现
std::string ProcessManager::Init_ProcessInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback("Initializing Process Manager...", 10);
        }

        // 创建进程管理器实例
        ProcessManager processManager;

        // 检查参数中是否指定了详细模式
        bool useDetailedMode = (params.find("detailed=true") != std::string::npos);
        processManager.SetFastMode(!useDetailedMode);

        if (!processManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize Process Manager."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            if (processManager.IsFastMode()) {
                progressCallback("Enumerating running processes (fast mode)...", 50);
            } else {
                progressCallback("Enumerating running processes (detailed mode)...", 50);
            }
        }

        // 获取进程信息
        nlohmann::json processInfo = processManager.GetProcessesInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：保存到文件
        if (progressCallback) {
            progressCallback("Saving processes information to file...", 90);
        }

        // 保存进程信息到JSON文件
        std::string filename = "processes_data.json";
        bool saveSuccess = processManager.SaveProcessesInfoToFile(filename);

        // 报告进度：完成
        if (progressCallback) {
            if (saveSuccess) {
                progressCallback("Process enumeration and file save completed successfully", 100);
            } else {
                progressCallback("Process enumeration completed, but file save failed", 100);
            }
        }

        // 添加任务状态信息
        processInfo["status"] = "success";
        processInfo["task_id"] = taskId;
        processInfo["message"] = "Windows processes information retrieved successfully";

        // 添加文件保存状态信息
        processInfo["file_save"] = {
            {"filename", filename},
            {"save_success", saveSuccess},
            {"save_message", saveSuccess ? "Processes data saved to file successfully" : "Failed to save processes data to file"}
        };

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            processInfo["request_params"] = params;
        }

        return processInfo.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during process enumeration", -1);
        }

        return errorResult.dump();
    }
}

// 辅助函数实现
std::wstring ProcessManager::ConvertToWString(const std::string& str) {
    if (str.empty()) return L"";

    int len = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
    if (len == 0) return L"";

    std::wstring result(len - 1, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &result[0], len);
    return result;
}

std::string ProcessManager::ConvertToString(const std::wstring& wstr) {
    if (wstr.empty()) return "";

    int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len == 0) return "";

    std::string result(len - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], len, nullptr, nullptr);
    return result;
}

std::string ProcessManager::FormatMemorySize(SIZE_T bytes) {
    double mb = static_cast<double>(bytes) / (1024.0 * 1024.0);
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << mb;
    return oss.str();
}

ProcessData ProcessManager::GetProcessDetails(DWORD processId, const std::string& processName) {
    ProcessData processData;
    processData.pid = processId;
    processData.name = processName;
    processData.status = "Running";

    // 打开进程句柄
    HANDLE processHandle = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (processHandle == nullptr) {
        // 如果无法打开进程，返回基本信息
        processData.status = "Access Denied";
        processData.memory = "0.0 MB";
        processData.path = "Access Denied";
        processData.create_time = "Access Denied";
        processData.icon = "default";
        return processData;
    }

    try {
        // 获取进程内存信息
        PROCESS_MEMORY_COUNTERS memCounters;
        if (GetProcessMemoryInfo(processHandle, &memCounters, sizeof(memCounters))) {
            processData.memory = FormatMemorySize(memCounters.WorkingSetSize) + " MB";
        } else {
            processData.memory = "0.0 MB";
        }

        // 获取进程路径
        processData.path = GetProcessPath(processHandle);

        // 获取进程创建时间
        processData.create_time = GetProcessCreateTime(processHandle);

    } catch (...) {
        // 忽略异常，设置默认值
        processData.memory = "0.0 MB";
        processData.path = "Unknown";
        processData.create_time = "Unknown";
    }

    // 获取进程图标
    processData.icon = GetProcessIcon(processData.path);

    CloseHandle(processHandle);
    return processData;
}

ULONGLONG ProcessManager::FileTimeToULongLong(const FILETIME& ft) {
    ULARGE_INTEGER uli;
    uli.LowPart = ft.dwLowDateTime;
    uli.HighPart = ft.dwHighDateTime;
    return uli.QuadPart;
}

std::string ProcessManager::CalculateProcessCpuUsage(HANDLE processHandle) {
    try {
        // 获取系统时间
        FILETIME sysIdle, sysKernel, sysUser;
        FILETIME procCreation, procExit, procKernel, procUser;

        if (!GetSystemTimes(&sysIdle, &sysKernel, &sysUser)) {
            return "0.0%";
        }

        if (!GetProcessTimes(processHandle, &procCreation, &procExit, &procKernel, &procUser)) {
            return "0.0%";
        }

        // 等待一小段时间来计算差值
        Sleep(100);

        FILETIME sysIdle2, sysKernel2, sysUser2;
        FILETIME procKernel2, procUser2;

        if (!GetSystemTimes(&sysIdle2, &sysKernel2, &sysUser2)) {
            return "0.0%";
        }

        if (!GetProcessTimes(processHandle, &procCreation, &procExit, &procKernel2, &procUser2)) {
            return "0.0%";
        }

        // 计算时间差
        ULONGLONG sysKernelDiff = FileTimeToULongLong(sysKernel2) - FileTimeToULongLong(sysKernel);
        ULONGLONG sysUserDiff = FileTimeToULongLong(sysUser2) - FileTimeToULongLong(sysUser);
        ULONGLONG procKernelDiff = FileTimeToULongLong(procKernel2) - FileTimeToULongLong(procKernel);
        ULONGLONG procUserDiff = FileTimeToULongLong(procUser2) - FileTimeToULongLong(procUser);

        ULONGLONG sysTotal = sysKernelDiff + sysUserDiff;
        ULONGLONG procTotal = procKernelDiff + procUserDiff;

        if (sysTotal == 0) {
            return "0.0%";
        }

        double cpuUsage = (double)procTotal / (double)sysTotal * 100.0;

        std::ostringstream oss;
        oss << std::fixed << std::setprecision(1) << cpuUsage << "%";
        return oss.str();

    } catch (...) {
        return "0.0%";
    }
}

std::string ProcessManager::GetProcessDiskUsage(DWORD processId) {
    try {
        // 打开进程句柄
        HANDLE processHandle = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (processHandle == nullptr) {
            return "0.0 MB/s";
        }

        // 获取进程I/O计数器
        IO_COUNTERS ioCounters1, ioCounters2;
        if (!GetProcessIoCounters(processHandle, &ioCounters1)) {
            CloseHandle(processHandle);
            return "0.0 MB/s";
        }

        // 等待一段时间
        Sleep(100);

        if (!GetProcessIoCounters(processHandle, &ioCounters2)) {
            CloseHandle(processHandle);
            return "0.0 MB/s";
        }

        CloseHandle(processHandle);

        // 计算读写字节差值
        ULONGLONG readBytes = ioCounters2.ReadTransferCount - ioCounters1.ReadTransferCount;
        ULONGLONG writeBytes = ioCounters2.WriteTransferCount - ioCounters1.WriteTransferCount;
        ULONGLONG totalBytes = readBytes + writeBytes;

        // 转换为MB/s (100ms间隔，所以乘以10)
        double mbPerSec = (double)totalBytes * 10.0 / (1024.0 * 1024.0);

        std::ostringstream oss;
        oss << std::fixed << std::setprecision(1) << mbPerSec << " MB/s";
        return oss.str();

    } catch (...) {
        return "0.0 MB/s";
    }
}

std::string ProcessManager::GetProcessNetworkUsage(DWORD processId) {
    // 网络使用率检测比较复杂，需要使用WMI或其他方法
    // 这里先返回占位符，可以后续实现
    return "0.0 KB/s";
}

std::string ProcessManager::GetProcessUserName(HANDLE processHandle) {
    try {
        HANDLE tokenHandle;
        if (!OpenProcessToken(processHandle, TOKEN_QUERY, &tokenHandle)) {
            return "Unknown";
        }

        DWORD tokenInfoLength = 0;
        GetTokenInformation(tokenHandle, TokenUser, nullptr, 0, &tokenInfoLength);

        if (tokenInfoLength == 0) {
            CloseHandle(tokenHandle);
            return "Unknown";
        }

        std::vector<BYTE> tokenInfo(tokenInfoLength);
        if (!GetTokenInformation(tokenHandle, TokenUser, tokenInfo.data(), tokenInfoLength, &tokenInfoLength)) {
            CloseHandle(tokenHandle);
            return "Unknown";
        }

        PTOKEN_USER tokenUser = reinterpret_cast<PTOKEN_USER>(tokenInfo.data());

        wchar_t userName[256];
        wchar_t domainName[256];
        DWORD userNameSize = 256;
        DWORD domainNameSize = 256;
        SID_NAME_USE sidType;

        if (LookupAccountSid(nullptr, tokenUser->User.Sid, userName, &userNameSize,
                            domainName, &domainNameSize, &sidType)) {
            CloseHandle(tokenHandle);

            // 如果域名不为空，返回"域\用户名"格式
            if (domainNameSize > 0 && wcslen(domainName) > 0) {
                std::wstring fullName = std::wstring(domainName) + L"\\" + std::wstring(userName);
                return ConvertToString(fullName);
            } else {
                // 只返回用户名
                return ConvertToString(userName);
            }
        }

        CloseHandle(tokenHandle);
        return "Unknown";

    } catch (...) {
        return "Unknown";
    }
}

// 获取进程可执行文件路径
std::string ProcessManager::GetProcessPath(HANDLE processHandle) {
    if (processHandle == nullptr) {
        return "Unknown";
    }

    try {
        // 方法1: 尝试使用QueryFullProcessImageNameW (Vista+)
        typedef BOOL (WINAPI *QueryFullProcessImageNameWFunc)(HANDLE, DWORD, LPWSTR, PDWORD);
        HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
        if (kernel32) {
            QueryFullProcessImageNameWFunc queryFunc =
                (QueryFullProcessImageNameWFunc)GetProcAddress(kernel32, "QueryFullProcessImageNameW");

            if (queryFunc) {
                wchar_t processPath[MAX_PATH];
                DWORD pathSize = MAX_PATH;

                if (queryFunc(processHandle, 0, processPath, &pathSize)) {
                    return ConvertToString(std::wstring(processPath));
                }
            }
        }

        // 方法2: 使用GetModuleFileNameEx (XP兼容)
        wchar_t processPath[MAX_PATH];
        if (GetModuleFileNameExW(processHandle, nullptr, processPath, MAX_PATH)) {
            return ConvertToString(std::wstring(processPath));
        }

        // 方法3: 使用GetProcessImageFileName (备用方法)
        if (GetProcessImageFileNameW(processHandle, processPath, MAX_PATH)) {
            std::wstring pathStr(processPath);
            // GetProcessImageFileName返回的是设备路径，需要转换
            if (pathStr.find(L"\\Device\\") == 0) {
                // 简化处理：只返回文件名部分
                size_t lastSlash = pathStr.find_last_of(L'\\');
                if (lastSlash != std::wstring::npos) {
                    return ConvertToString(pathStr.substr(lastSlash + 1));
                }
            }
            return ConvertToString(pathStr);
        }

    } catch (...) {
        // 忽略所有异常
    }

    return "Unknown";
}

// 获取进程创建时间
std::string ProcessManager::GetProcessCreateTime(HANDLE processHandle) {
    if (processHandle == nullptr) {
        return "Unknown";
    }

    FILETIME createTime, exitTime, kernelTime, userTime;
    if (GetProcessTimes(processHandle, &createTime, &exitTime, &kernelTime, &userTime)) {
        // 转换FILETIME到SYSTEMTIME
        SYSTEMTIME systemTime;
        if (FileTimeToSystemTime(&createTime, &systemTime)) {
            char timeStr[64];
            sprintf_s(timeStr, sizeof(timeStr), "%04d-%02d-%02d %02d:%02d:%02d",
                systemTime.wYear, systemTime.wMonth, systemTime.wDay,
                systemTime.wHour, systemTime.wMinute, systemTime.wSecond);
            return std::string(timeStr);
        }
    }

    return "Unknown";
}

// 获取进程图标（提取真实图标并转换为Base64）
std::string ProcessManager::GetProcessIcon(const std::string& processPath) {
    if (processPath.empty() || processPath == "Unknown" || processPath == "Access Denied") {
        return "";
    }

    try {
        // 提取程序图标并转换为Base64编码的BMP格式
        std::string iconBase64 = ExtractProcessIconAsBase64(processPath, 32);
        if (!iconBase64.empty()) {
            return iconBase64;
        }

        // 备用方案：根据文件类型返回图标类型标识
        return GetIconTypeByPath(processPath);

    } catch (...) {
        // 异常时返回空字符串
        return "";
    }
}

// 提取程序图标并转换为Base64编码的BMP格式
std::string ProcessManager::ExtractProcessIconAsBase64(const std::string& processPath, int iconSize) {
    if (processPath.empty()) {
        return "";
    }

    try {
        // 转换为宽字符
        std::wstring wPath = ConvertToWString(processPath);

        HICON hIcon = nullptr;

        // 方法1: 使用SHGetFileInfo获取文件图标
        SHFILEINFOW sfi = {0};
        DWORD_PTR result = SHGetFileInfoW(wPath.c_str(), 0, &sfi, sizeof(sfi),
            SHGFI_ICON | SHGFI_LARGEICON);

        if (result != 0 && sfi.hIcon) {
            hIcon = sfi.hIcon;
        } else {
            // 尝试小图标
            result = SHGetFileInfoW(wPath.c_str(), 0, &sfi, sizeof(sfi),
                SHGFI_ICON | SHGFI_SMALLICON);
            if (result != 0 && sfi.hIcon) {
                hIcon = sfi.hIcon;
            }
        }

        // 方法2: 对于可执行文件，尝试使用ExtractIcon
        if (hIcon == nullptr || hIcon == (HICON)1) {
            std::string lowerPath = processPath;
            std::transform(lowerPath.begin(), lowerPath.end(), lowerPath.begin(), ::tolower);

            if (lowerPath.find(".exe") != std::string::npos ||
                lowerPath.find(".dll") != std::string::npos) {
                HICON extractedIcon = ExtractIconA(GetModuleHandle(nullptr), processPath.c_str(), 0);
                if (extractedIcon != nullptr && extractedIcon != (HICON)1) {
                    if (hIcon != nullptr) {
                        DestroyIcon(hIcon);
                    }
                    hIcon = extractedIcon;
                }
            }
        }

        // 转换图标为BMP格式的Base64
        if (hIcon != nullptr && hIcon != (HICON)1) {
            std::vector<BYTE> bmpData = ExtractIconToBMP(hIcon, iconSize);
            DestroyIcon(hIcon);

            if (!bmpData.empty()) {
                // 使用正确的BMP格式Base64编码
                return EncodeBase64(bmpData.data(), bmpData.size());
            }
        }

    } catch (...) {
        // 忽略异常
    }

    return "";
}

// 提取程序图标信息（简化版）
std::string ProcessManager::ExtractIconInfo(const std::string& processPath) {
    if (processPath.empty()) {
        return "";
    }

    try {
        // 转换为宽字符
        std::wstring wPath = ConvertToWString(processPath);

        // 使用SHGetFileInfo获取图标信息
        SHFILEINFOW sfi = {0};
        DWORD_PTR result = SHGetFileInfoW(wPath.c_str(), 0, &sfi, sizeof(sfi),
            SHGFI_ICON | SHGFI_LARGEICON | SHGFI_TYPENAME | SHGFI_DISPLAYNAME);

        if (result != 0) {
            // 构建图标信息字符串
            std::string iconInfo = "icon_info:";

            // 添加显示名称
            if (sfi.szDisplayName[0] != 0) {
                iconInfo += "name=" + ConvertToString(std::wstring(sfi.szDisplayName)) + ";";
            }

            // 添加类型名称
            if (sfi.szTypeName[0] != 0) {
                iconInfo += "type=" + ConvertToString(std::wstring(sfi.szTypeName)) + ";";
            }

            // 添加图标索引
            iconInfo += "index=" + std::to_string(sfi.iIcon) + ";";

            // 清理图标句柄
            if (sfi.hIcon) {
                DestroyIcon(sfi.hIcon);
            }

            return iconInfo;
        }

    } catch (...) {
        // 忽略异常
    }

    return "";
}

// 提取程序图标并转换为Base64（保留原方法名以兼容）
std::string ProcessManager::ExtractIconAsBase64(const std::string& processPath) {
    if (processPath.empty()) {
        return "";
    }

    try {
        // 转换为宽字符
        std::wstring wPath = ConvertToWString(processPath);

        // 获取大图标
        HICON hIcon = nullptr;
        UINT iconCount = ExtractIconExW(wPath.c_str(), 0, &hIcon, nullptr, 1);

        if (iconCount == 0 || hIcon == nullptr) {
            // 尝试获取小图标
            iconCount = ExtractIconExW(wPath.c_str(), 0, nullptr, &hIcon, 1);
        }

        if (iconCount == 0 || hIcon == nullptr) {
            // 尝试使用SHGetFileInfo获取图标
            SHFILEINFOW sfi = {0};
            if (SHGetFileInfoW(wPath.c_str(), 0, &sfi, sizeof(sfi), SHGFI_ICON | SHGFI_LARGEICON)) {
                hIcon = sfi.hIcon;
            }
        }

        if (hIcon != nullptr) {
            // 将图标转换为Base64
            std::string base64Icon = ConvertIconToBase64(hIcon);
            DestroyIcon(hIcon);
            return base64Icon;
        }

    } catch (...) {
        // 忽略异常
    }

    return "";
}

// 获取图标文件路径
std::string ProcessManager::GetIconFilePath(const std::string& processPath) {
    if (processPath.empty()) {
        return "";
    }

    try {
        // 检查是否有关联的图标文件
        std::string iconPath = processPath;
        size_t lastDot = iconPath.find_last_of('.');
        if (lastDot != std::string::npos) {
            iconPath = iconPath.substr(0, lastDot) + ".ico";

            // 检查图标文件是否存在
            DWORD attributes = GetFileAttributesA(iconPath.c_str());
            if (attributes != INVALID_FILE_ATTRIBUTES && !(attributes & FILE_ATTRIBUTE_DIRECTORY)) {
                return iconPath;
            }
        }

        // 检查同目录下的icon.ico
        size_t lastSlash = processPath.find_last_of("\\/");
        if (lastSlash != std::string::npos) {
            std::string dirPath = processPath.substr(0, lastSlash + 1);
            std::string iconFile = dirPath + "icon.ico";

            DWORD attributes = GetFileAttributesA(iconFile.c_str());
            if (attributes != INVALID_FILE_ATTRIBUTES && !(attributes & FILE_ATTRIBUTE_DIRECTORY)) {
                return iconFile;
            }
        }

    } catch (...) {
        // 忽略异常
    }

    return "";
}

// 根据路径获取图标类型
std::string ProcessManager::GetIconTypeByPath(const std::string& processPath) {
    if (processPath.empty()) {
        return "default";
    }

    // 提取文件名
    std::string fileName = processPath;
    size_t lastSlash = fileName.find_last_of("\\/");
    if (lastSlash != std::string::npos) {
        fileName = fileName.substr(lastSlash + 1);
    }

    // 转换为小写
    std::transform(fileName.begin(), fileName.end(), fileName.begin(), ::tolower);

    // 网络相关程序
    if (fileName.find("chrome") != std::string::npos ||
        fileName.find("firefox") != std::string::npos ||
        fileName.find("edge") != std::string::npos ||
        fileName.find("opera") != std::string::npos ||
        fileName.find("safari") != std::string::npos) {
        return "browser";
    }

    // 网络工具
    if (fileName.find("wireshark") != std::string::npos ||
        fileName.find("fiddler") != std::string::npos ||
        fileName.find("netstat") != std::string::npos ||
        fileName.find("tcpview") != std::string::npos) {
        return "network";
    }

    // 通讯软件
    if (fileName.find("qq") != std::string::npos ||
        fileName.find("wechat") != std::string::npos ||
        fileName.find("skype") != std::string::npos ||
        fileName.find("teams") != std::string::npos ||
        fileName.find("discord") != std::string::npos) {
        return "chat";
    }

    // 下载工具
    if (fileName.find("thunder") != std::string::npos ||
        fileName.find("idm") != std::string::npos ||
        fileName.find("utorrent") != std::string::npos ||
        fileName.find("bittorrent") != std::string::npos) {
        return "download";
    }

    // 系统进程
    if (fileName.find("svchost") != std::string::npos ||
        fileName.find("winlogon") != std::string::npos ||
        fileName.find("csrss") != std::string::npos ||
        fileName.find("lsass") != std::string::npos) {
        return "service";
    }

    // 文本编辑器
    if (fileName.find("notepad") != std::string::npos ||
        fileName.find("wordpad") != std::string::npos ||
        fileName.find("code") != std::string::npos ||
        fileName.find("sublime") != std::string::npos) {
        return "text";
    }

    // 文件管理器
    if (fileName.find("explorer") != std::string::npos ||
        fileName.find("totalcmd") != std::string::npos) {
        return "folder";
    }

    // 媒体播放器
    if (fileName.find("vlc") != std::string::npos ||
        fileName.find("media") != std::string::npos ||
        fileName.find("player") != std::string::npos) {
        return "media";
    }

    // 安全软件
    if (fileName.find("antivirus") != std::string::npos ||
        fileName.find("defender") != std::string::npos ||
        fileName.find("kaspersky") != std::string::npos ||
        fileName.find("norton") != std::string::npos) {
        return "security";
    }

    // 开发工具
    if (fileName.find("visual") != std::string::npos ||
        fileName.find("studio") != std::string::npos ||
        fileName.find("devenv") != std::string::npos ||
        fileName.find("git") != std::string::npos) {
        return "development";
    }

    // 系统工具
    if (fileName.find("cmd") != std::string::npos ||
        fileName.find("powershell") != std::string::npos ||
        fileName.find("regedit") != std::string::npos ||
        fileName.find("taskmgr") != std::string::npos) {
        return "system";
    }

    // 默认应用程序图标
    if (fileName.find(".exe") != std::string::npos) {
        return "application";
    }

    return "default";
}

// 将图标转换为Base64字符串
std::string ProcessManager::ConvertIconToBase64(HICON hIcon) {
    if (hIcon == nullptr) {
        return "";
    }

    try {
        // 获取图标信息
        ICONINFO iconInfo;
        if (!GetIconInfo(hIcon, &iconInfo)) {
            return "";
        }

        // 获取位图信息
        BITMAP bitmap;
        if (GetObject(iconInfo.hbmColor, sizeof(BITMAP), &bitmap) == 0) {
            DeleteObject(iconInfo.hbmColor);
            DeleteObject(iconInfo.hbmMask);
            return "";
        }

        // 创建设备上下文
        HDC hdc = GetDC(nullptr);
        HDC hdcMem = CreateCompatibleDC(hdc);

        // 创建DIB
        BITMAPINFOHEADER bih = {0};
        bih.biSize = sizeof(BITMAPINFOHEADER);
        bih.biWidth = bitmap.bmWidth;
        bih.biHeight = -bitmap.bmHeight; // 负值表示从上到下
        bih.biPlanes = 1;
        bih.biBitCount = 32;
        bih.biCompression = BI_RGB;

        void* pBits = nullptr;
        HBITMAP hDIB = CreateDIBSection(hdcMem, (BITMAPINFO*)&bih, DIB_RGB_COLORS, &pBits, nullptr, 0);

        if (hDIB && pBits) {
            // 选择DIB到内存DC
            HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hDIB);

            // 绘制图标到DIB
            DrawIconEx(hdcMem, 0, 0, hIcon, bitmap.bmWidth, bitmap.bmHeight, 0, nullptr, DI_NORMAL);

            // 计算数据大小
            int dataSize = bitmap.bmWidth * bitmap.bmHeight * 4; // 32位 = 4字节

            // 转换为Base64
            std::string base64 = EncodeBase64((unsigned char*)pBits, dataSize);

            // 清理资源
            SelectObject(hdcMem, hOldBitmap);
            DeleteObject(hDIB);

            // 清理其他资源
            DeleteDC(hdcMem);
            ReleaseDC(nullptr, hdc);
            DeleteObject(iconInfo.hbmColor);
            DeleteObject(iconInfo.hbmMask);

            // 返回Base64数据（不添加错误的PNG前缀）
            return base64;
        }

        // 清理资源
        DeleteDC(hdcMem);
        ReleaseDC(nullptr, hdc);
        DeleteObject(iconInfo.hbmColor);
        DeleteObject(iconInfo.hbmMask);

    } catch (...) {
        return "";
    }
}

// Base64编码
std::string ProcessManager::EncodeBase64(const unsigned char* data, size_t length) {
    if (data == nullptr || length == 0) {
        return "";
    }

    const char base64_chars[] =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    std::string result;
    int val = 0, valb = -6;

    for (size_t i = 0; i < length; ++i) {
        val = (val << 8) + data[i];
        valb += 8;
        while (valb >= 0) {
            result.push_back(base64_chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }

    if (valb > -6) {
        result.push_back(base64_chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }

    while (result.size() % 4) {
        result.push_back('=');
    }

    return result;
}

// 将图标转换为标准BMP格式的二进制数据
// 技术要求：生成标准BMP文件格式（包含完整文件头），32x32像素，支持透明度
std::vector<BYTE> ProcessManager::ExtractIconToBMP(HICON hIcon, int size) {
    std::vector<BYTE> bmpData;

    // 输入验证
    if (!hIcon) {
        return bmpData;
    }

    // 获取设备上下文
    HDC hdc = GetDC(nullptr);
    if (!hdc) {
        return bmpData;
    }

    // 创建兼容的内存设备上下文
    HDC hdcMem = CreateCompatibleDC(hdc);
    if (!hdcMem) {
        ReleaseDC(nullptr, hdc);
        return bmpData;
    }

    // 创建32位ARGB位图信息（支持透明度）
    BITMAPINFO bmi = {};
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = size;
    bmi.bmiHeader.biHeight = -size; // 负值表示自上而下的位图
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 32; // 32位支持透明度
    bmi.bmiHeader.biCompression = BI_RGB;

    // 创建DIB位图
    void* pBits = nullptr;
    HBITMAP hBitmap = CreateDIBSection(hdcMem, &bmi, DIB_RGB_COLORS, &pBits, nullptr, 0);
    if (!hBitmap || !pBits) {
        DeleteDC(hdcMem);
        ReleaseDC(nullptr, hdc);
        return bmpData;
    }

    HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

    // 填充透明背景（白色，便于图标显示）
    RECT rect = {0, 0, size, size};
    HBRUSH hBrush = CreateSolidBrush(RGB(255, 255, 255));
    FillRect(hdcMem, &rect, hBrush);
    DeleteObject(hBrush);

    // 使用Windows Shell API绘制图标
    BOOL drawResult = DrawIconEx(hdcMem, 0, 0, hIcon, size, size, 0, nullptr, DI_NORMAL);

    if (drawResult) {
        // 生成标准BMP文件格式（包含完整文件头）
        DWORD imageSize = size * size * 4; // 32位ARGB
        DWORD fileSize = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + imageSize;

        bmpData.resize(fileSize);

        // 构建BMP文件头
        BITMAPFILEHEADER* pFileHeader = (BITMAPFILEHEADER*)bmpData.data();
        pFileHeader->bfType = 0x4D42; // "BM" - BMP文件标识
        pFileHeader->bfSize = fileSize;
        pFileHeader->bfReserved1 = 0;
        pFileHeader->bfReserved2 = 0;
        pFileHeader->bfOffBits = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);

        // 构建BMP信息头
        BITMAPINFOHEADER* pInfoHeader = (BITMAPINFOHEADER*)(bmpData.data() + sizeof(BITMAPFILEHEADER));
        *pInfoHeader = bmi.bmiHeader;
        pInfoHeader->biHeight = size; // 正值表示自下而上的位图（BMP标准）
        pInfoHeader->biSizeImage = imageSize;

        // 复制并转换图像数据（BMP格式要求自下而上存储）
        BYTE* pImageData = bmpData.data() + sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);
        BYTE* pSourceData = (BYTE*)pBits;

        // 逐行复制并垂直翻转以符合BMP标准
        for (int y = 0; y < size; y++) {
            BYTE* pDestRow = pImageData + (size - 1 - y) * size * 4;
            BYTE* pSourceRow = pSourceData + y * size * 4;
            memcpy(pDestRow, pSourceRow, size * 4);
        }
    }

    // 确保内存资源正确释放
    SelectObject(hdcMem, hOldBitmap);
    DeleteObject(hBitmap);
    DeleteDC(hdcMem);
    ReleaseDC(nullptr, hdc);

    return bmpData;
}

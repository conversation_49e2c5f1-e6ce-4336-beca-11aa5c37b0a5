// 网络连接管理器测试和使用示例
// 支持Windows XP及以上版本

#include "../include/NetworkConnectionManager.h"
#include <iostream>
#include <fstream>

// 测试网络连接功能
void TestNetworkConnectionManager() {
    std::cout << "=== 网络连接管理器测试 ===" << std::endl;
    
    NetworkConnectionManager manager;
    
    // 测试1: 获取所有网络连接
    std::cout << "\n1. 获取所有网络连接..." << std::endl;
    std::vector<NetworkConnectionInfo> allConnections = manager.GetAllNetworkConnections();
    std::cout << "总连接数: " << allConnections.size() << std::endl;
    
    // 显示前5个连接的详细信息
    int count = 0;
    for (const auto& conn : allConnections) {
        if (count >= 5) break;
        std::cout << "  连接 " << (count + 1) << ": " 
                  << conn.protocol << " "
                  << conn.localAddress << ":" << conn.localPort << " -> "
                  << conn.remoteAddress << ":" << conn.remotePort
                  << " (PID: " << conn.owningPid << ")" << std::endl;
        count++;
    }
    
    // 测试2: 获取所有网络进程
    std::cout << "\n2. 获取所有网络进程..." << std::endl;
    std::vector<NetworkProcessInfo> networkProcesses = manager.GetAllNetworkProcesses();
    std::cout << "有网络连接的进程数: " << networkProcesses.size() << std::endl;
    
    // 显示前5个进程的详细信息
    count = 0;
    for (const auto& process : networkProcesses) {
        if (count >= 5) break;
        std::cout << "  进程 " << (count + 1) << ": " 
                  << process.processName << " (PID: " << process.pid << ")"
                  << " 连接数: " << process.connections.size() << std::endl;
        count++;
    }
    
    // 测试3: 获取活跃网络连接
    std::cout << "\n3. 获取活跃网络连接..." << std::endl;
    json activeConnections = manager.GetActiveNetworkConnections();
    std::cout << "活跃连接数: " << activeConnections.size() << std::endl;
    
    // 测试4: 获取网络连接统计信息
    std::cout << "\n4. 获取网络连接统计信息..." << std::endl;
    json stats = manager.GetNetworkConnectionStats();
    std::cout << "统计信息:" << std::endl;
    std::cout << "  总连接数: " << stats["totalConnections"] << std::endl;
    std::cout << "  TCP连接数: " << stats["tcpConnections"] << std::endl;
    std::cout << "  UDP连接数: " << stats["udpConnections"] << std::endl;
    std::cout << "  已建立连接数: " << stats["establishedConnections"] << std::endl;
    std::cout << "  监听连接数: " << stats["listeningConnections"] << std::endl;
    std::cout << "  唯一进程数: " << stats["uniqueProcesses"] << std::endl;
    
    // 测试5: 根据进程名查找
    std::cout << "\n5. 查找Chrome浏览器的网络连接..." << std::endl;
    std::vector<NetworkProcessInfo> chromeProcesses = manager.GetNetworkProcessesByName("chrome");
    std::cout << "找到Chrome进程数: " << chromeProcesses.size() << std::endl;
    
    for (const auto& process : chromeProcesses) {
        std::cout << "  Chrome进程: " << process.processName 
                  << " (PID: " << process.pid << ")"
                  << " 连接数: " << process.connections.size() << std::endl;
    }
    
    // 测试6: 根据端口查找
    std::cout << "\n6. 查找使用端口80的进程..." << std::endl;
    std::vector<NetworkProcessInfo> port80Processes = manager.FindProcessesByPort(80);
    std::cout << "使用端口80的进程数: " << port80Processes.size() << std::endl;
    
    for (const auto& process : port80Processes) {
        std::cout << "  进程: " << process.processName 
                  << " (PID: " << process.pid << ")" << std::endl;
    }
    
    // 测试7: 保存JSON数据到文件
    std::cout << "\n7. 保存网络连接数据到JSON文件..." << std::endl;
    try {
        // 保存所有网络连接
        std::string allConnectionsJson = manager.GetAllNetworkConnectionsAsJsonString();
        std::ofstream allFile("network_connections_all.json");
        if (allFile.is_open()) {
            allFile << allConnectionsJson;
            allFile.close();
            std::cout << "  已保存所有网络连接到 network_connections_all.json" << std::endl;
        }
        
        // 保存活跃连接
        json activeConnectionsJson = manager.GetActiveNetworkConnections();
        std::ofstream activeFile("network_connections_active.json");
        if (activeFile.is_open()) {
            activeFile << activeConnectionsJson.dump(4);
            activeFile.close();
            std::cout << "  已保存活跃连接到 network_connections_active.json" << std::endl;
        }
        
        // 保存统计信息
        json statsJson = manager.GetNetworkConnectionStats();
        std::ofstream statsFile("network_connections_stats.json");
        if (statsFile.is_open()) {
            statsFile << statsJson.dump(4);
            statsFile.close();
            std::cout << "  已保存统计信息到 network_connections_stats.json" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "保存JSON文件时出错: " << e.what() << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
}

// 实时监控网络连接变化
void MonitorNetworkConnections(int durationSeconds = 30) {
    std::cout << "=== 实时网络连接监控 (持续 " << durationSeconds << " 秒) ===" << std::endl;
    
    NetworkConnectionManager manager;
    
    int interval = 5; // 每5秒检查一次
    int iterations = durationSeconds / interval;
    
    for (int i = 0; i < iterations; i++) {
        std::cout << "\n--- 监控周期 " << (i + 1) << " ---" << std::endl;
        
        // 获取当前网络连接统计
        json stats = manager.GetNetworkConnectionStats();
        std::cout << "时间: " << (i * interval) << "s - "
                  << "总连接: " << stats["totalConnections"] << ", "
                  << "TCP: " << stats["tcpConnections"] << ", "
                  << "UDP: " << stats["udpConnections"] << ", "
                  << "已建立: " << stats["establishedConnections"] << std::endl;
        
        // 获取活跃连接
        json activeConnections = manager.GetActiveNetworkConnections();
        std::cout << "活跃连接数: " << activeConnections.size() << std::endl;
        
        // 显示新的网络活动
        if (activeConnections.size() > 0) {
            std::cout << "最近活跃的连接:" << std::endl;
            int count = 0;
            for (const auto& conn : activeConnections) {
                if (count >= 3) break; // 只显示前3个
                std::cout << "  " << conn["processName"] << " (" << conn["protocol"] << ") "
                          << conn["localAddress"] << ":" << conn["localPort"] << " -> "
                          << conn["remoteAddress"] << ":" << conn["remotePort"] << std::endl;
                count++;
            }
        }
        
        // 等待下一个检查周期
        if (i < iterations - 1) {
            Sleep(interval * 1000);
        }
    }
    
    std::cout << "\n=== 监控结束 ===" << std::endl;
}

// 主函数 - 演示网络连接管理器的使用
int main() {
    std::cout << "Windows XP兼容网络连接管理器演示" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    try {
        // 运行基本测试
        TestNetworkConnectionManager();
        
        // 询问用户是否要运行实时监控
        std::cout << "\n是否要运行实时网络监控? (y/n): ";
        char choice;
        std::cin >> choice;
        
        if (choice == 'y' || choice == 'Y') {
            MonitorNetworkConnections(30); // 监控30秒
        }
        
    } catch (const std::exception& e) {
        std::cerr << "程序运行出错: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n按任意键退出..." << std::endl;
    std::cin.ignore();
    std::cin.get();
    
    return 0;
}

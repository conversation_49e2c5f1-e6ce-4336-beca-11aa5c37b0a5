#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <functional>
#include <nlohmann/json.hpp>

// miniblink相关头文件（需要根据实际miniblink SDK路径调整）
#ifdef _WIN64
#pragma comment(lib, "node_x64.lib")
#else
#pragma comment(lib, "node.lib")
#endif

// miniblink WebView接口
extern "C" {
    typedef void* wkeWebView;
    typedef void* wkeWebFrameHandle;
    
    // miniblink基础函数声明
    void wkeInitialize();
    void wkeFinalize();
    wkeWebView wkeCreateWebView();
    void wkeDestroyWebView(wkeWebView webView);
    void wkeLoadHTML(wkeWebView webView, const char* html);
    void wkeLoadFile(wkeWebView webView, const char* filename);
    void wkeLoadURL(wkeWebView webView, const char* url);
    void wkeResize(wkeWebView webView, int w, int h);
    void wkeShowWindow(wkeWebView webView, bool show);
    void wkeSetWindowTitle(wkeWebView webView, const char* title);
    HWND wkeGetWindowHandle(wkeWebView webView);
    void wkeRunJS(wkeWebView webView, const char* script);
    
    // 回调函数类型
    typedef void(*wkeOnWindowClosing)(wkeWebView webView, void* param);
    typedef void(*wkeOnDocumentReady)(wkeWebView webView, void* param);
    
    void wkeOnWindowClosing(wkeWebView webView, wkeOnWindowClosing callback, void* param);
    void wkeOnDocumentReady(wkeWebView webView, wkeOnDocumentReady callback, void* param);
}

// 图片信息结构
struct ImageInfo {
    std::string fileName;       // 文件名
    std::string filePath;       // 完整路径
    std::string fileSize;       // 文件大小
    std::string dimensions;     // 图片尺寸
    std::string createTime;     // 创建时间
    std::string format;         // 图片格式
    bool hasExifData;          // 是否有EXIF数据
    
    ImageInfo() : hasExifData(false) {}
};

// miniblink图片查看器类
class MinibinkImageViewer {
public:
    MinibinkImageViewer();
    ~MinibinkImageViewer();
    
    // 初始化和清理
    bool Initialize();
    void Cleanup();
    
    // 窗口管理
    bool CreateWindow(int width = 1024, int height = 768);
    void ShowWindow(bool show = true);
    void SetWindowTitle(const std::string& title);
    HWND GetWindowHandle() const;
    
    // 图片显示功能
    bool LoadImageViewer();
    bool DisplayImage(const std::string& imagePath);
    bool DisplayImageWithInfo(const std::string& imagePath, const ImageInfo& info);
    bool SetImageList(const std::vector<ImageInfo>& images);
    
    // 文件协议支持
    std::string ConvertToFileUrl(const std::string& localPath);
    bool IsValidImageFile(const std::string& filePath);
    
    // 图片信息获取
    ImageInfo GetImageInfo(const std::string& imagePath);
    std::vector<ImageInfo> ScanImageFolder(const std::string& folderPath, bool recursive = false);
    
    // JavaScript交互
    bool ExecuteScript(const std::string& script);
    bool CallJavaScriptFunction(const std::string& functionName, const std::vector<std::string>& params);

    // 前端文件选择支持
    bool TriggerFileSelection();
    bool TriggerFolderSelection();
    bool ClearImages();

    // 获取前端选择的文件信息
    bool IsFileSelectionSupported() const;
    std::string GetSelectedFilesInfo();
    
    // 事件处理
    void SetOnWindowClosing(std::function<void()> callback);
    void SetOnDocumentReady(std::function<void()> callback);
    
    // 消息循环
    void RunMessageLoop();
    void ProcessMessages();
    
private:
    wkeWebView m_webView;
    HWND m_hwnd;
    bool m_initialized;
    std::string m_htmlTemplate;
    std::vector<ImageInfo> m_imageList;
    int m_currentIndex;
    
    // 回调函数
    std::function<void()> m_onWindowClosing;
    std::function<void()> m_onDocumentReady;
    
    // 静态回调函数
    static void OnWindowClosingCallback(wkeWebView webView, void* param);
    static void OnDocumentReadyCallback(wkeWebView webView, void* param);
    
    // 辅助方法
    bool LoadHtmlTemplate();
    std::string EscapeJsonString(const std::string& input);
    std::string GetFileSize(const std::string& filePath);
    std::string GetFileCreateTime(const std::string& filePath);
    std::string GetImageDimensions(const std::string& imagePath);
    std::vector<std::string> GetSupportedImageExtensions();
};

// 全局函数
namespace MinibinkImageViewerUtils {
    // 初始化miniblink环境
    bool InitializeMiniblink();

    // 清理miniblink环境
    void FinalizeMiniblink();

    // 创建简单的图片查看器窗口
    MinibinkImageViewer* CreateImageViewer(const std::string& imagePath = "");

    // 显示图片文件夹
    bool ShowImageFolder(const std::string& folderPath);

    // 显示单个图片
    bool ShowSingleImage(const std::string& imagePath);

    // 创建支持前端文件选择的查看器
    MinibinkImageViewer* CreateInteractiveImageViewer();

    // 显示交互式图片查看器（用户可以在前端选择文件）
    bool ShowInteractiveImageViewer();
}

﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Tool.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\WiFiManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\ServiceManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\ShareManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\DriverManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\AccountLockoutPolicyManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\FirewallManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\ImageManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\NetworkConnectionManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\PasswordPolicyManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\ScreensaverManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\StartupManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\ExifManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\WiFiData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\WiFiManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ProcessData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ProcessManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ServiceData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ServiceManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ShareData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ShareManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\DriverData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\DriverManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\AccountLockoutPolicyData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\AccountLockoutPolicyManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\FirewallData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\FirewallManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ImageManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\NetworkConnectionData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\NetworkConnectionManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\PasswordPolicyData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\PasswordPolicyManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ScreensaverData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ScreensaverManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\StartupData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\StartupManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ExifData.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\ExifManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
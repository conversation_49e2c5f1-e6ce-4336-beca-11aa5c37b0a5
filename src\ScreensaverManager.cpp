﻿
#include "../include/ScreensaverManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <set>
#include <algorithm>
#include <chrono>
#include <winver.h>
#include <wintrust.h>
#include <softpub.h>
#include <shlobj.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <wtsapi32.h>
#include <userenv.h>
#include <sddl.h>

#pragma comment(lib, "version.lib")
#pragma comment(lib, "wintrust.lib")
#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "wtsapi32.lib")
#pragma comment(lib, "userenv.lib")

ScreensaverManager::ScreensaverManager() : m_initialized(false) {
}

ScreensaverManager::~ScreensaverManager() {
    Cleanup();
}

bool ScreensaverManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_startTime = std::chrono::system_clock::now();
    m_initialized = true;
    return true;
}

void ScreensaverManager::Cleanup() {
    m_initialized = false;
}

std::vector<ScreensaverData> ScreensaverManager::GetAllScreensavers() {
    std::vector<ScreensaverData> allScreensavers;
    std::set<std::string> processedFiles; // 避免重复

    if (!m_initialized) {
        return allScreensavers;
    }

    try {
        // 1. 扫描系统屏保
        std::vector<ScreensaverData> systemScreensavers = ScanSystemScreensavers();
        for (auto& screensaver : systemScreensavers) {
            if (processedFiles.find(screensaver.file_path) == processedFiles.end()) {
                allScreensavers.push_back(screensaver);
                processedFiles.insert(screensaver.file_path);
            }
        }

        // 2. 扫描注册表中的屏保
        std::vector<ScreensaverData> registryScreensavers = ScanRegistryScreensavers();
        for (auto& screensaver : registryScreensavers) {
            if (processedFiles.find(screensaver.file_path) == processedFiles.end()) {
                allScreensavers.push_back(screensaver);
                processedFiles.insert(screensaver.file_path);
            }
        }

        // 3. 扫描常见目录
        std::vector<std::string> directories = GetCommonScreensaverDirectories();
        for (const auto& dir : directories) {
            std::vector<ScreensaverData> dirScreensavers = ScanDirectoryScreensavers(dir);
            for (auto& screensaver : dirScreensavers) {
                if (processedFiles.find(screensaver.file_path) == processedFiles.end()) {
                    allScreensavers.push_back(screensaver);
                    processedFiles.insert(screensaver.file_path);
                }
            }
        }

        // 4. 获取当前活动屏保设置
        ScreensaverData activeScreensaver;
        if (GetCurrentScreensaverSettings(activeScreensaver)) {
            // 标记活动屏保
            for (auto& screensaver : allScreensavers) {
                if (screensaver.file_path == activeScreensaver.file_path) {
                    screensaver.is_active = true;
                    screensaver.is_enabled = activeScreensaver.is_enabled;
                    screensaver.password_protected = activeScreensaver.password_protected;
                    break;
                }
            }
        }

    } catch (const std::exception& e) {
        std::cout << "Error scanning screensavers: " << e.what() << std::endl;
    }

    return allScreensavers;
}

ScreensaverStatistics ScreensaverManager::GetScreensaverStatistics() {
    ScreensaverStatistics stats;
    
    std::vector<ScreensaverData> screensavers = GetAllScreensavers();
    stats.total_screensavers = static_cast<int>(screensavers.size());
    stats.scan_time = std::time(nullptr);

    // 计算扫描耗时
    auto endTime = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_startTime);
    stats.scan_duration = std::to_string(duration.count()) + " ms";

    // 统计各类屏保
    for (const auto& screensaver : screensavers) {
        if (screensaver.category == "System") {
            stats.system_screensavers++;
        } else if (screensaver.category == "Third-party") {
            stats.third_party_screensavers++;
        }

        if (screensaver.is_active) {
            stats.screensaver_enabled = screensaver.is_enabled;
            stats.password_protected = screensaver.password_protected;
        }
    }

    return stats;
}

nlohmann::json ScreensaverManager::GetScreensaversInfoAsJson() {
    nlohmann::json result;

    try {
        std::vector<ScreensaverData> screensavers = GetAllScreensavers();
        ScreensaverStatistics stats = GetScreensaverStatistics();

        // 构建JSON结果
        result["metadata"] = {
            {"tool_name", "Windows Screensaver Scanner"},
            {"version", "1.0.0"},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration},
            {"total_screensavers_found", stats.total_screensavers}
        };

        result["statistics"] = stats;

        // 按分类组织屏保
        nlohmann::json categorized_screensavers;
        for (const auto& screensaver : screensavers) {
            categorized_screensavers[screensaver.category].push_back(screensaver);
        }

        result["screensavers_by_category"] = categorized_screensavers;
        result["all_screensavers"] = screensavers;

        return result;
    }
    catch (const std::exception& e) {
        nlohmann::json error_result;
        error_result["error"] = "Failed to get screensaver information";
        error_result["details"] = e.what();
        return error_result;
    }
}

bool ScreensaverManager::SaveScreensaversInfoToFile(const std::string& filename) {
    try {
        nlohmann::json screensaversInfo = GetScreensaversInfoAsJson();

        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << "Failed to open file for writing: " << filename << std::endl;
            return false;
        }

        file << screensaversInfo.dump(4); // 4 spaces indentation
        file.close();

        std::cout << "Screensaver information saved to: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "Error saving screensaver information: " << e.what() << std::endl;
        return false;
    }
}

std::string ScreensaverManager::Init_ScreensaverInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb) {

    try {
        if (progressCallback) {
            progressCallback("Initializing screensaver manager...", 10);
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            return "{\"error\":\"Task cancelled by user\"}";
        }

        ScreensaverManager manager;
        if (!manager.Initialize()) {
            return "{\"error\":\"Failed to initialize screensaver manager\"}";
        }

        if (progressCallback) {
            progressCallback("Scanning system screensavers...", 30);
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            return "{\"error\":\"Task cancelled by user\"}";
        }

        if (progressCallback) {
            progressCallback("Getting screensaver details...", 60);
        }

        nlohmann::json screensaversInfo = manager.GetScreensaversInfoAsJson();

        if (progressCallback) {
            progressCallback("Processing screensaver information...", 80);
        }

        // 保存到文件
        manager.SaveScreensaversInfoToFile("screensavers_data.json");

        if (progressCallback) {
            progressCallback("Screensaver scan completed", 100);
        }

        return screensaversInfo.dump(4);
    }
    catch (const std::exception& e) {
        std::string error = "{\"error\":\"Exception occurred: ";
        error += e.what();
        error += "\"}";
        return error;
    }
}

// 辅助函数实现
std::string ScreensaverManager::ConvertToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::wstring ScreensaverManager::ConvertToWString(const std::string& str) {
    if (str.empty()) return std::wstring();

    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

std::vector<ScreensaverData> ScreensaverManager::ScanSystemScreensavers() {
    std::vector<ScreensaverData> screensavers;

    try {
        // 获取系统目录
        std::vector<std::string> systemDirs = GetSystemDirectories();

        for (const auto& dir : systemDirs) {
            std::vector<ScreensaverData> dirScreensavers = ScanDirectoryScreensavers(dir);
            for (auto& screensaver : dirScreensavers) {
                screensaver.is_system_screensaver = true;
                screensaver.category = "System";
                screensavers.push_back(screensaver);
            }
        }
    }
    catch (const std::exception& e) {
        std::cout << "Error scanning system screensavers: " << e.what() << std::endl;
    }

    return screensavers;
}

std::vector<ScreensaverData> ScreensaverManager::ScanRegistryScreensavers() {
    std::vector<ScreensaverData> screensavers;

    try {
        // 读取当前用户的屏保设置
        std::string screensaverPath = ReadRegistryString(HKEY_CURRENT_USER,
            "Control Panel\\Desktop", "SCRNSAVE.EXE");

        if (!screensaverPath.empty() && FileExists(screensaverPath)) {
            ScreensaverData data;
            if (GetScreensaverDetails(screensaverPath, data)) {
                data.is_active = true;
                screensavers.push_back(data);
            }
        }

        // 扫描注册表中注册的屏保
        // HKEY_LOCAL_MACHINE\SOFTWARE\Classes\.scr
        // 这里可以添加更多注册表扫描逻辑
    }
    catch (const std::exception& e) {
        std::cout << "Error scanning registry screensavers: " << e.what() << std::endl;
    }

    return screensavers;
}

std::vector<ScreensaverData> ScreensaverManager::ScanDirectoryScreensavers(const std::string& directory) {
    std::vector<ScreensaverData> screensavers;

    try {
        if (!std::filesystem::exists(directory)) {
            return screensavers;
        }

        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                std::string filePath = entry.path().string();
                std::string extension = entry.path().extension().string();

                // 转换为小写进行比较
                std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

                if (extension == ".scr") {
                    ScreensaverData data;
                    if (GetScreensaverDetails(filePath, data)) {
                        screensavers.push_back(data);
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        std::cout << "Error scanning directory " << directory << ": " << e.what() << std::endl;
    }

    return screensavers;
}

bool ScreensaverManager::GetScreensaverDetails(const std::string& filePath, ScreensaverData& data) {
    if (!FileExists(filePath)) {
        return false;
    }

    try {
        data.file_path = NormalizePath(filePath);
        data.name = ParseScreensaverName(filePath);
        data.display_name = GetScreensaverDisplayName(filePath);

        // 获取文件信息
        data.version = GetFileVersion(filePath);
        data.manufacturer = GetFileManufacturer(filePath);
        data.description = GetFileDescription(filePath);
        data.file_size = GetFileSize(filePath);
        data.creation_time = GetFileCreationTime(filePath);
        data.modification_time = GetFileModificationTime(filePath);

        // 签名信息 (不再存储到结构体中)
        bool isSigned = IsFileSigned(filePath);

        // 分类
        data.category = ClassifyScreensaver(filePath, data.manufacturer);
        data.is_system_screensaver = IsSystemScreensaver(filePath);

        // 获取屏保配置信息
        GetScreensaverConfiguration(filePath, data);

        // 检查屏保是否支持配置对话框
        data.command_line = GetScreensaverCommandLine(filePath);

        // 获取屏保注册表信息
        data.registry_key = GetScreensaverRegistryKey(filePath);

        return true;
    }
    catch (const std::exception& e) {
        std::cout << "Error getting screensaver details for " << filePath << ": " << e.what() << std::endl;
        return false;
    }
}

bool ScreensaverManager::GetCurrentScreensaverSettings(ScreensaverData& activeScreensaver) {
    try {
        // 读取当前屏保设置
        std::string screensaverPath = ReadRegistryString(HKEY_CURRENT_USER,
            "Control Panel\\Desktop", "SCRNSAVE.EXE");

        if (screensaverPath.empty()) {
            return false;
        }

        activeScreensaver.file_path = ExpandEnvironmentPath(screensaverPath);
        activeScreensaver.is_active = true;

        // 读取屏保启用状态
        activeScreensaver.is_enabled = ReadRegistryBool(HKEY_CURRENT_USER,
            "Control Panel\\Desktop", "ScreenSaveActive");

        // 读取超时时间 (不再存储到结构体中)
        DWORD timeout = ReadRegistryDWORD(HKEY_CURRENT_USER,
            "Control Panel\\Desktop", "ScreenSaveTimeOut");

        // 读取密码保护设置
        activeScreensaver.password_protected = ReadRegistryBool(HKEY_CURRENT_USER,
            "Control Panel\\Desktop", "ScreenSaverIsSecure");

        return true;
    }
    catch (const std::exception& e) {
        std::cout << "Error getting current screensaver settings: " << e.what() << std::endl;
        return false;
    }
}

std::string ScreensaverManager::ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey;
    std::string result;

    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
        DWORD dataSize = 0;
        DWORD dataType;

        // 获取数据大小
        if (RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &dataType, nullptr, &dataSize) == ERROR_SUCCESS) {
            if (dataType == REG_SZ || dataType == REG_EXPAND_SZ) {
                std::vector<char> buffer(dataSize);
                if (RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &dataType,
                                   reinterpret_cast<LPBYTE>(buffer.data()), &dataSize) == ERROR_SUCCESS) {
                    result = std::string(buffer.data());
                }
            }
        }

        RegCloseKey(hSubKey);
    }

    return result;
}

DWORD ScreensaverManager::ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey;
    DWORD result = 0;

    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
        DWORD dataSize = sizeof(DWORD);
        DWORD dataType;

        RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &dataType,
                        reinterpret_cast<LPBYTE>(&result), &dataSize);

        RegCloseKey(hSubKey);
    }

    return result;
}

bool ScreensaverManager::ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    DWORD value = ReadRegistryDWORD(hKey, subKey, valueName);
    return value != 0;
}

std::string ScreensaverManager::GetFileVersion(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = ConvertToWString(filePath);
    DWORD verHandle = 0;
    DWORD verSize = GetFileVersionInfoSize(wFilePath.c_str(), &verHandle);

    if (verSize == 0) {
        return "";
    }

    std::vector<BYTE> verData(verSize);
    if (!GetFileVersionInfo(wFilePath.c_str(), verHandle, verSize, verData.data())) {
        return "";
    }

    VS_FIXEDFILEINFO* fileInfo = nullptr;
    UINT size = 0;
    if (!VerQueryValue(verData.data(), L"\\", (LPVOID*)&fileInfo, &size)) {
        return "";
    }

    if (size == 0) {
        return "";
    }

    // 格式化版本号
    std::ostringstream version;
    version << HIWORD(fileInfo->dwFileVersionMS) << "."
            << LOWORD(fileInfo->dwFileVersionMS) << "."
            << HIWORD(fileInfo->dwFileVersionLS) << "."
            << LOWORD(fileInfo->dwFileVersionLS);

    return version.str();
}

std::string ScreensaverManager::GetFileManufacturer(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = ConvertToWString(filePath);
    DWORD verHandle = 0;
    DWORD verSize = GetFileVersionInfoSize(wFilePath.c_str(), &verHandle);

    if (verSize == 0) {
        return "";
    }

    std::vector<BYTE> verData(verSize);
    if (!GetFileVersionInfo(wFilePath.c_str(), verHandle, verSize, verData.data())) {
        return "";
    }

    // 获取语言和代码页信息
    LPVOID lpTranslate = nullptr;
    UINT cbTranslate = 0;
    if (!VerQueryValue(verData.data(), L"\\VarFileInfo\\Translation", &lpTranslate, &cbTranslate)) {
        return "";
    }

    // 构建查询字符串
    DWORD* pdwTranslate = (DWORD*)lpTranslate;
    std::wostringstream query;
    query << L"\\StringFileInfo\\" << std::hex << std::setw(4) << std::setfill(L'0')
          << LOWORD(pdwTranslate[0]) << std::setw(4) << HIWORD(pdwTranslate[0])
          << L"\\CompanyName";

    LPVOID lpBuffer = nullptr;
    UINT dwBytes = 0;
    if (VerQueryValue(verData.data(), query.str().c_str(), &lpBuffer, &dwBytes)) {
        return ConvertToString((LPCWSTR)lpBuffer);
    }

    return "";
}

std::string ScreensaverManager::GetFileDescription(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = ConvertToWString(filePath);
    DWORD verHandle = 0;
    DWORD verSize = GetFileVersionInfoSize(wFilePath.c_str(), &verHandle);

    if (verSize == 0) {
        return "";
    }

    std::vector<BYTE> verData(verSize);
    if (!GetFileVersionInfo(wFilePath.c_str(), verHandle, verSize, verData.data())) {
        return "";
    }

    // 获取语言和代码页信息
    LPVOID lpTranslate = nullptr;
    UINT cbTranslate = 0;
    if (!VerQueryValue(verData.data(), L"\\VarFileInfo\\Translation", &lpTranslate, &cbTranslate)) {
        return "";
    }

    // 构建查询字符串
    DWORD* pdwTranslate = (DWORD*)lpTranslate;
    std::wostringstream query;
    query << L"\\StringFileInfo\\" << std::hex << std::setw(4) << std::setfill(L'0')
          << LOWORD(pdwTranslate[0]) << std::setw(4) << HIWORD(pdwTranslate[0])
          << L"\\FileDescription";

    LPVOID lpBuffer = nullptr;
    UINT dwBytes = 0;
    if (VerQueryValue(verData.data(), query.str().c_str(), &lpBuffer, &dwBytes)) {
        return ConvertToString((LPCWSTR)lpBuffer);
    }

    return "";
}

std::string ScreensaverManager::GetFileSize(const std::string& filePath) {
    if (filePath.empty()) return "0 KB";

    try {
        if (!std::filesystem::exists(filePath)) {
            return "0 KB";
        }

        auto fileSize = std::filesystem::file_size(filePath);
        double sizeInKB = fileSize / 1024.0;

        std::ostringstream result;
        if (sizeInKB < 1024) {
            result << std::fixed << std::setprecision(1) << sizeInKB << " KB";
        } else {
            result << std::fixed << std::setprecision(1) << (sizeInKB / 1024.0) << " MB";
        }

        return result.str();
    }
    catch (const std::exception&) {
        return "0 KB";
    }
}

std::string ScreensaverManager::GetFileCreationTime(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = ConvertToWString(filePath);
    WIN32_FILE_ATTRIBUTE_DATA fileAttr;

    if (!GetFileAttributesEx(wFilePath.c_str(), GetFileExInfoStandard, &fileAttr)) {
        return "";
    }

    SYSTEMTIME sysTime;
    if (!FileTimeToSystemTime(&fileAttr.ftCreationTime, &sysTime)) {
        return "";
    }

    std::ostringstream result;
    result << std::setfill('0') << std::setw(4) << sysTime.wYear << "-"
           << std::setw(2) << sysTime.wMonth << "-"
           << std::setw(2) << sysTime.wDay << " "
           << std::setw(2) << sysTime.wHour << ":"
           << std::setw(2) << sysTime.wMinute << ":"
           << std::setw(2) << sysTime.wSecond;

    return result.str();
}

std::string ScreensaverManager::GetFileModificationTime(const std::string& filePath) {
    if (filePath.empty()) return "";

    std::wstring wFilePath = ConvertToWString(filePath);
    WIN32_FILE_ATTRIBUTE_DATA fileAttr;

    if (!GetFileAttributesEx(wFilePath.c_str(), GetFileExInfoStandard, &fileAttr)) {
        return "";
    }

    SYSTEMTIME sysTime;
    if (!FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &sysTime)) {
        return "";
    }

    std::ostringstream result;
    result << std::setfill('0') << std::setw(4) << sysTime.wYear << "-"
           << std::setw(2) << sysTime.wMonth << "-"
           << std::setw(2) << sysTime.wDay << " "
           << std::setw(2) << sysTime.wHour << ":"
           << std::setw(2) << sysTime.wMinute << ":"
           << std::setw(2) << sysTime.wSecond;

    return result.str();
}

bool ScreensaverManager::IsFileSigned(const std::string& filePath) {
    if (filePath.empty()) return false;

    std::wstring wFilePath = ConvertToWString(filePath);

    WINTRUST_FILE_INFO fileData;
    memset(&fileData, 0, sizeof(fileData));
    fileData.cbStruct = sizeof(WINTRUST_FILE_INFO);
    fileData.pcwszFilePath = wFilePath.c_str();
    fileData.hFile = NULL;
    fileData.pgKnownSubject = NULL;

    GUID WVTPolicyGUID = WINTRUST_ACTION_GENERIC_VERIFY_V2;
    WINTRUST_DATA winTrustData;

    memset(&winTrustData, 0, sizeof(winTrustData));
    winTrustData.cbStruct = sizeof(winTrustData);
    winTrustData.pPolicyCallbackData = NULL;
    winTrustData.pSIPClientData = NULL;
    winTrustData.dwUIChoice = WTD_UI_NONE;
    winTrustData.fdwRevocationChecks = WTD_REVOKE_NONE;
    winTrustData.dwUnionChoice = WTD_CHOICE_FILE;
    winTrustData.dwStateAction = WTD_STATEACTION_VERIFY;
    winTrustData.hWVTStateData = NULL;
    winTrustData.pwszURLReference = NULL;
    winTrustData.dwUIContext = 0;
    winTrustData.pFile = &fileData;

    LONG lStatus = WinVerifyTrust(NULL, &WVTPolicyGUID, &winTrustData);

    // 清理
    winTrustData.dwStateAction = WTD_STATEACTION_CLOSE;
    WinVerifyTrust(NULL, &WVTPolicyGUID, &winTrustData);

    return (lStatus == ERROR_SUCCESS);
}

std::string ScreensaverManager::GetFileSignatureInfo(const std::string& filePath) {
    if (filePath.empty()) return "";

    if (IsFileSigned(filePath)) {
        return "Microsoft Windows";  // 简化实现
    }

    return "Not Signed";
}

std::string ScreensaverManager::ClassifyScreensaver(const std::string& filePath, const std::string& manufacturer) {
    if (IsSystemScreensaver(filePath)) {
        return "System";
    }

    // 检查制造商
    std::string lowerManufacturer = manufacturer;
    std::transform(lowerManufacturer.begin(), lowerManufacturer.end(), lowerManufacturer.begin(), ::tolower);

    if (lowerManufacturer.find("microsoft") != std::string::npos) {
        return "System";
    }

    return "Third-party";
}

bool ScreensaverManager::IsSystemScreensaver(const std::string& filePath) {
    std::string lowerPath = filePath;
    std::transform(lowerPath.begin(), lowerPath.end(), lowerPath.begin(), ::tolower);

    // 检查是否在系统目录中
    return (lowerPath.find("\\windows\\system32\\") != std::string::npos ||
            lowerPath.find("\\windows\\syswow64\\") != std::string::npos);
}

std::string ScreensaverManager::ExpandEnvironmentPath(const std::string& path) {
    if (path.empty()) return path;

    std::wstring wPath = ConvertToWString(path);
    WCHAR expandedPath[MAX_PATH];

    DWORD result = ExpandEnvironmentStrings(wPath.c_str(), expandedPath, MAX_PATH);
    if (result > 0 && result <= MAX_PATH) {
        return ConvertToString(expandedPath);
    }

    return path;
}

std::string ScreensaverManager::NormalizePath(const std::string& path) {
    try {
        std::filesystem::path fsPath(path);
        return fsPath.lexically_normal().string();
    }
    catch (const std::exception&) {
        return path;
    }
}

bool ScreensaverManager::FileExists(const std::string& filePath) {
    try {
        return std::filesystem::exists(filePath);
    }
    catch (const std::exception&) {
        return false;
    }
}

std::string ScreensaverManager::ParseScreensaverName(const std::string& filePath) {
    try {
        std::filesystem::path fsPath(filePath);
        return fsPath.stem().string(); // 不包含扩展名的文件名
    }
    catch (const std::exception&) {
        return "Unknown";
    }
}

std::string ScreensaverManager::GetScreensaverDisplayName(const std::string& filePath) {
    // 首先尝试从文件版本信息获取
    std::string description = GetFileDescription(filePath);
    if (!description.empty()) {
        return description;
    }

    // 如果没有描述，使用文件名
    return ParseScreensaverName(filePath);
}

bool ScreensaverManager::HasConfigurationDialog(const std::string& filePath) {
    try {
        // 尝试执行配置命令来检查是否支持
        std::wstring wFilePath = ConvertToWString(filePath);
        std::wstring cmdLine = wFilePath + L" /c";

        STARTUPINFO si = { sizeof(si) };
        PROCESS_INFORMATION pi;

        // 创建进程但立即终止来测试支持性
        if (CreateProcess(NULL, const_cast<LPWSTR>(cmdLine.c_str()), NULL, NULL, FALSE,
                         CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
            TerminateProcess(pi.hProcess, 0);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return true;
        }

        return false;
    }
    catch (const std::exception&) {
        return false;
    }
}

std::vector<std::string> ScreensaverManager::GetSystemDirectories() {
    std::vector<std::string> directories;

    // 获取系统目录
    WCHAR systemDir[MAX_PATH];
    if (GetSystemDirectory(systemDir, MAX_PATH)) {
        directories.push_back(ConvertToString(systemDir));
    }

    // 获取Windows目录
    WCHAR windowsDir[MAX_PATH];
    if (GetWindowsDirectory(windowsDir, MAX_PATH)) {
        directories.push_back(ConvertToString(windowsDir));
    }

    return directories;
}

std::vector<std::string> ScreensaverManager::GetCommonScreensaverDirectories() {
    std::vector<std::string> directories;

    // 系统目录
    std::vector<std::string> systemDirs = GetSystemDirectories();
    directories.insert(directories.end(), systemDirs.begin(), systemDirs.end());

    // Program Files目录
    WCHAR programFiles[MAX_PATH];
    if (SHGetFolderPath(NULL, CSIDL_PROGRAM_FILES, NULL, SHGFP_TYPE_CURRENT, programFiles) == S_OK) {
        directories.push_back(ConvertToString(programFiles));
    }

    // Program Files (x86)目录
    if (SHGetFolderPath(NULL, CSIDL_PROGRAM_FILESX86, NULL, SHGFP_TYPE_CURRENT, programFiles) == S_OK) {
        directories.push_back(ConvertToString(programFiles));
    }

    // 用户目录下的常见位置
    WCHAR userProfile[MAX_PATH];
    if (SHGetFolderPath(NULL, CSIDL_PROFILE, NULL, SHGFP_TYPE_CURRENT, userProfile) == S_OK) {
        std::string userDir = ConvertToString(userProfile);
        directories.push_back(userDir + "\\AppData\\Local");
        directories.push_back(userDir + "\\AppData\\Roaming");
    }

    return directories;
}

std::string ScreensaverManager::GetLastErrorString() {
    DWORD errorCode = GetLastError();
    if (errorCode == 0) {
        return "";
    }

    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer, 0, NULL);

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);

    return message;
}

// 新增的屏保配置和功能方法实现
bool ScreensaverManager::GetScreensaverConfiguration(const std::string& filePath, ScreensaverData& data) {
    try {
        // 检查是否有配置对话框
        data.has_configuration = HasConfigurationDialog(filePath);

        // 检查是否支持预览
        data.can_preview = CanPreview(filePath);

        // 检查是否正在运行
        data.is_running = IsScreensaverRunning() && (GetCurrentRunningScreensaver() == filePath);

        // 获取架构信息
        data.architecture = GetScreensaverArchitecture(filePath);

        // 获取依赖项
        std::vector<std::string> deps = GetScreensaverDependencies(filePath);
        data.dependencies = "";
        for (size_t i = 0; i < deps.size(); ++i) {
            if (i > 0) data.dependencies += "; ";
            data.dependencies += deps[i];
        }

        // 获取性能信息
        DWORD memoryUsage = GetScreensaverMemoryUsage(filePath);
        double cpuUsage = GetScreensaverCpuUsage(filePath);
        data.performance_info = GetScreensaverPerformanceInfo(filePath);

        // 获取安全信息
        data.is_trusted = IsScreensaverTrusted(filePath);

        // 获取使用统计
        data.usage_count = GetScreensaverUsageCount(filePath);
        data.last_activation = GetLastScreensaverActivation();

        // 获取兼容性信息
        data.is_compatible = IsScreensaverCompatible(filePath);
        data.compatibility_info = GetScreensaverCompatibilityInfo(filePath);

        return true;
    }
    catch (const std::exception& e) {
        return false;
    }
}

std::string ScreensaverManager::GetScreensaverCommandLine(const std::string& filePath) {
    try {
        std::string result;

        // 基本命令行参数
        result += "\"" + filePath + "\"";

        // 检查支持的参数
        if (HasConfigurationDialog(filePath)) {
            result += " /c (Configuration)";
        }

        if (CanPreview(filePath)) {
            result += " /p <hwnd> (Preview)";
        }

        result += " /s (Screensaver mode)";

        return result;
    }
    catch (const std::exception&) {
        return "";
    }
}

std::string ScreensaverManager::GetScreensaverRegistryKey(const std::string& filePath) {
    try {
        std::filesystem::path fsPath(filePath);
        std::string fileName = fsPath.filename().string();

        // 构建注册表键路径
        std::string regKey = "HKEY_CURRENT_USER\\Control Panel\\Desktop\\";
        regKey += "ScreenSave_" + fileName;

        return regKey;
    }
    catch (const std::exception&) {
        return "";
    }
}

bool ScreensaverManager::CanPreview(const std::string& filePath) {
    try {
        // 尝试执行预览命令来检查是否支持
        std::wstring wFilePath = ConvertToWString(filePath);
        std::wstring cmdLine = wFilePath + L" /p 0";

        STARTUPINFO si = { sizeof(si) };
        PROCESS_INFORMATION pi;

        // 创建进程但立即终止来测试支持性
        if (CreateProcess(NULL, const_cast<LPWSTR>(cmdLine.c_str()), NULL, NULL, FALSE,
                         CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
            TerminateProcess(pi.hProcess, 0);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return true;
        }

        return false;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool ScreensaverManager::IsScreensaverRunning() {
    try {
        // 检查是否有屏保进程在运行
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return false;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = ConvertToString(pe32.szExeFile);
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find(".scr") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return false;
    }
    catch (const std::exception&) {
        return false;
    }
}

std::string ScreensaverManager::GetCurrentRunningScreensaver() {
    try {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return "";
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = ConvertToString(pe32.szExeFile);
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find(".scr") != std::string::npos) {
                    // 获取进程的完整路径
                    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pe32.th32ProcessID);
                    if (hProcess) {
                        WCHAR processPath[MAX_PATH];
                        if (GetModuleFileNameEx(hProcess, NULL, processPath, MAX_PATH)) {
                            CloseHandle(hProcess);
                            CloseHandle(hSnapshot);
                            return ConvertToString(processPath);
                        }
                        CloseHandle(hProcess);
                    }
                }
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return "";
    }
    catch (const std::exception&) {
        return "";
    }
}

// 屏保控制功能实现
bool ScreensaverManager::StartScreensaver(const std::string& filePath) {
    try {
        std::wstring wFilePath = ConvertToWString(filePath);
        std::wstring cmdLine = wFilePath + L" /s";

        STARTUPINFO si = { sizeof(si) };
        PROCESS_INFORMATION pi;

        if (CreateProcess(NULL, const_cast<LPWSTR>(cmdLine.c_str()), NULL, NULL, FALSE,
                         0, NULL, NULL, &si, &pi)) {
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return true;
        }

        return false;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool ScreensaverManager::StopScreensaver() {
    try {
        // 发送鼠标移动消息来停止屏保
        SetCursorPos(0, 0);
        mouse_event(MOUSEEVENTF_MOVE, 1, 1, 0, 0);
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool ScreensaverManager::PreviewScreensaver(const std::string& filePath, HWND parentWindow) {
    try {
        std::wstring wFilePath = ConvertToWString(filePath);
        std::wostringstream cmdLine;
        cmdLine << wFilePath << L" /p " << reinterpret_cast<UINT_PTR>(parentWindow);

        STARTUPINFO si = { sizeof(si) };
        PROCESS_INFORMATION pi;

        if (CreateProcess(NULL, const_cast<LPWSTR>(cmdLine.str().c_str()), NULL, NULL, FALSE,
                         0, NULL, NULL, &si, &pi)) {
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return true;
        }

        return false;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool ScreensaverManager::ConfigureScreensaver(const std::string& filePath, HWND parentWindow) {
    try {
        std::wstring wFilePath = ConvertToWString(filePath);
        std::wstring cmdLine = wFilePath + L" /c";

        STARTUPINFO si = { sizeof(si) };
        PROCESS_INFORMATION pi;

        if (CreateProcess(NULL, const_cast<LPWSTR>(cmdLine.c_str()), NULL, NULL, FALSE,
                         0, NULL, NULL, &si, &pi)) {
            // 等待配置对话框完成
            WaitForSingleObject(pi.hProcess, INFINITE);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return true;
        }

        return false;
    }
    catch (const std::exception&) {
        return false;
    }
}

// 安全和权限检查实现
bool ScreensaverManager::CheckScreensaverPermissions(const std::string& filePath) {
    try {
        std::wstring wFilePath = ConvertToWString(filePath);

        // 检查文件是否存在和可执行
        DWORD attributes = GetFileAttributes(wFilePath.c_str());
        if (attributes == INVALID_FILE_ATTRIBUTES) {
            return false;
        }

        // 检查执行权限
        HANDLE hFile = CreateFile(wFilePath.c_str(), GENERIC_EXECUTE, FILE_SHARE_READ,
                                 NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) {
            return false;
        }

        CloseHandle(hFile);
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

std::string ScreensaverManager::GetScreensaverSecurityInfo(const std::string& filePath) {
    try {
        std::ostringstream info;

        // 检查数字签名
        if (IsFileSigned(filePath)) {
            info << "Digitally signed by: " << GetFileSignatureInfo(filePath) << "; ";
        } else {
            info << "Not digitally signed; ";
        }

        // 检查权限
        if (CheckScreensaverPermissions(filePath)) {
            info << "Executable permissions: OK; ";
        } else {
            info << "Executable permissions: DENIED; ";
        }

        // 检查位置安全性
        if (IsSystemScreensaver(filePath)) {
            info << "Location: System directory (Trusted); ";
        } else {
            info << "Location: User directory (Verify manually); ";
        }

        return info.str();
    }
    catch (const std::exception&) {
        return "Security check failed";
    }
}

bool ScreensaverManager::IsScreensaverTrusted(const std::string& filePath) {
    try {
        // 检查是否为系统屏保
        if (IsSystemScreensaver(filePath)) {
            return true;
        }

        // 检查数字签名
        if (IsFileSigned(filePath)) {
            return true;
        }

        // 其他信任检查可以在这里添加
        return false;
    }
    catch (const std::exception&) {
        return false;
    }
}

// 性能分析实现
DWORD ScreensaverManager::GetScreensaverMemoryUsage(const std::string& filePath) {
    try {
        // 如果屏保正在运行，获取其内存使用
        std::string runningScreensaver = GetCurrentRunningScreensaver();
        if (runningScreensaver == filePath) {
            HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot == INVALID_HANDLE_VALUE) {
                return 0;
            }

            PROCESSENTRY32 pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32);

            if (Process32First(hSnapshot, &pe32)) {
                do {
                    std::string processName = ConvertToString(pe32.szExeFile);
                    std::filesystem::path fsPath(filePath);
                    std::string fileName = fsPath.filename().string();

                    if (processName == fileName) {
                        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pe32.th32ProcessID);
                        if (hProcess) {
                            PROCESS_MEMORY_COUNTERS pmc;
                            if (GetProcessMemoryInfo(hProcess, &pmc, sizeof(pmc))) {
                                CloseHandle(hProcess);
                                CloseHandle(hSnapshot);
                                return static_cast<DWORD>(pmc.WorkingSetSize / 1024); // 返回KB
                            }
                            CloseHandle(hProcess);
                        }
                    }
                } while (Process32Next(hSnapshot, &pe32));
            }

            CloseHandle(hSnapshot);
        }

        return 0;
    }
    catch (const std::exception&) {
        return 0;
    }
}

double ScreensaverManager::GetScreensaverCpuUsage(const std::string& filePath) {
    try {
        // 简化实现 - 实际应该监控一段时间来计算CPU使用率
        std::string runningScreensaver = GetCurrentRunningScreensaver();
        if (runningScreensaver == filePath) {
            // 这里应该实现CPU使用率监控
            // 由于复杂性，返回估计值
            return 5.0; // 假设5%的CPU使用率
        }

        return 0.0;
    }
    catch (const std::exception&) {
        return 0.0;
    }
}

std::string ScreensaverManager::GetScreensaverPerformanceInfo(const std::string& filePath) {
    try {
        std::ostringstream info;

        DWORD memUsage = GetScreensaverMemoryUsage(filePath);
        double cpuUsage = GetScreensaverCpuUsage(filePath);

        info << "Memory: " << memUsage << " KB; ";
        info << "CPU: " << std::fixed << std::setprecision(1) << cpuUsage << "%; ";

        // 获取文件大小作为性能参考
        std::string fileSize = GetFileSize(filePath);
        info << "File size: " << fileSize << "; ";

        // 检查是否正在运行
        if (IsScreensaverRunning() && GetCurrentRunningScreensaver() == filePath) {
            info << "Status: Running";
        } else {
            info << "Status: Not running";
        }

        return info.str();
    }
    catch (const std::exception&) {
        return "Performance info unavailable";
    }
}

// 历史和统计实现
std::vector<std::string> ScreensaverManager::GetScreensaverHistory() {
    std::vector<std::string> history;

    try {
        // 从注册表读取屏保历史
        // 这是一个简化实现，实际应该从系统日志或专门的历史记录中读取
        std::string currentScreensaver = ReadRegistryString(HKEY_CURRENT_USER,
            "Control Panel\\Desktop", "SCRNSAVE.EXE");

        if (!currentScreensaver.empty()) {
            history.push_back(currentScreensaver);
        }

        return history;
    }
    catch (const std::exception&) {
        return history;
    }
}

DWORD ScreensaverManager::GetScreensaverUsageCount(const std::string& filePath) {
    try {
        // 简化实现 - 实际应该从系统统计中读取
        // 这里返回一个基于文件修改时间的估计值
        std::filesystem::path fsPath(filePath);
        if (std::filesystem::exists(fsPath)) {
            auto lastWrite = std::filesystem::last_write_time(fsPath);
            auto now = std::filesystem::file_time_type::clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::hours>(now - lastWrite);

            // 假设每天使用一次，转换小时为天
            long days = duration.count() / 24;
            return static_cast<DWORD>((days > 1) ? days : 1);
        }

        return 0;
    }
    catch (const std::exception&) {
        return 0;
    }
}

std::string ScreensaverManager::GetLastScreensaverActivation() {
    try {
        // 简化实现 - 应该从系统事件日志中读取
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        struct tm timeinfo;
        if (localtime_s(&timeinfo, &time_t) == 0) {
            std::ostringstream oss;
            oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
            return oss.str();
        }

        return "Unknown";
    }
    catch (const std::exception&) {
        return "Unknown";
    }
}

// 高级屏保信息实现
std::vector<std::string> ScreensaverManager::GetScreensaverDependencies(const std::string& filePath) {
    std::vector<std::string> dependencies;

    try {
        std::wstring wFilePath = ConvertToWString(filePath);

        // 使用LoadLibrary来检查依赖项
        HMODULE hModule = LoadLibraryEx(wFilePath.c_str(), NULL, LOAD_LIBRARY_AS_DATAFILE);
        if (hModule) {
            // 这里应该解析PE文件的导入表来获取真正的依赖项
            // 简化实现，添加常见的系统依赖
            dependencies.push_back("kernel32.dll");
            dependencies.push_back("user32.dll");
            dependencies.push_back("gdi32.dll");

            FreeLibrary(hModule);
        }

        return dependencies;
    }
    catch (const std::exception&) {
        return dependencies;
    }
}

std::string ScreensaverManager::GetScreensaverArchitecture(const std::string& filePath) {
    try {
        std::wstring wFilePath = ConvertToWString(filePath);

        HANDLE hFile = CreateFile(wFilePath.c_str(), GENERIC_READ, FILE_SHARE_READ,
                                 NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) {
            return "Unknown";
        }

        IMAGE_DOS_HEADER dosHeader;
        DWORD bytesRead;

        if (!ReadFile(hFile, &dosHeader, sizeof(dosHeader), &bytesRead, NULL) ||
            bytesRead != sizeof(dosHeader) || dosHeader.e_magic != IMAGE_DOS_SIGNATURE) {
            CloseHandle(hFile);
            return "Unknown";
        }

        SetFilePointer(hFile, dosHeader.e_lfanew, NULL, FILE_BEGIN);

        IMAGE_NT_HEADERS ntHeaders;
        if (!ReadFile(hFile, &ntHeaders, sizeof(ntHeaders), &bytesRead, NULL) ||
            bytesRead != sizeof(ntHeaders) || ntHeaders.Signature != IMAGE_NT_SIGNATURE) {
            CloseHandle(hFile);
            return "Unknown";
        }

        CloseHandle(hFile);

        switch (ntHeaders.FileHeader.Machine) {
            case IMAGE_FILE_MACHINE_I386:
                return "x86 (32-bit)";
            case IMAGE_FILE_MACHINE_AMD64:
                return "x64 (64-bit)";
            case IMAGE_FILE_MACHINE_ARM:
                return "ARM";
#ifdef IMAGE_FILE_MACHINE_ARM64
            case IMAGE_FILE_MACHINE_ARM64:
                return "ARM64";
#endif
            default:
                return "Unknown architecture";
        }
    }
    catch (const std::exception&) {
        return "Unknown";
    }
}

bool ScreensaverManager::IsScreensaverCompatible(const std::string& filePath) {
    try {
        std::string architecture = GetScreensaverArchitecture(filePath);

        // 检查架构兼容性
        SYSTEM_INFO sysInfo;
        GetSystemInfo(&sysInfo);

        if (sysInfo.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_AMD64) {
            // 64位系统可以运行32位和64位屏保
            return (architecture.find("x86") != std::string::npos ||
                   architecture.find("x64") != std::string::npos);
        } else if (sysInfo.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_INTEL) {
            // 32位系统只能运行32位屏保
            return (architecture.find("x86") != std::string::npos);
        }

        return true; // 其他架构假设兼容
    }
    catch (const std::exception&) {
        return false;
    }
}

std::string ScreensaverManager::GetScreensaverCompatibilityInfo(const std::string& filePath) {
    try {
        std::ostringstream info;

        std::string architecture = GetScreensaverArchitecture(filePath);
        bool compatible = IsScreensaverCompatible(filePath);

        info << "Architecture: " << architecture << "; ";
        info << "Compatible: " << (compatible ? "Yes" : "No") << "; ";

        // 检查Windows版本兼容性
        // 使用IsWindows8OrGreater等API或简化处理
        // 由于版本检测API的复杂性，这里简化处理
        info << "Windows version: Modern; ";
        info << "OS compatibility: Good";

        return info.str();
    }
    catch (const std::exception&) {
        return "Compatibility check failed";
    }
}

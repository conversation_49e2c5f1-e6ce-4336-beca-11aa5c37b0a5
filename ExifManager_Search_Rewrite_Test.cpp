#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <chrono>

// 简单的进度回调函数
void searchRewriteProgressCallback(const std::string& message, int progress) {
    std::cout << "[Search Rewrite Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool searchRewriteQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 测试宽字符文件扫描性能
void testWideCharScan() {
    std::cout << "=== Testing Wide Character Scan Performance ===" << std::endl;
    
    ExifExtractor extractor;
    if (!extractor.Initialize()) {
        std::cout << "Failed to initialize EXIF extractor" << std::endl;
        return;
    }
    
    // 测试Screenshots目录
    std::wstring testDirectory = L"C:\\Users\\<USER>\\Pictures\\Screenshots";
    std::vector<std::wstring> wideImagePaths;
    
    std::cout << "Scanning directory: C:\\Users\\<USER>\\Pictures\\Screenshots" << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 使用新的宽字符扫描函数
    extractor.ScanDirectoryW(testDirectory, wideImagePaths);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    std::cout << "Wide character scan completed in " << duration.count() << " ms" << std::endl;
    std::cout << "Found " << wideImagePaths.size() << " image files" << std::endl;
    
    // 显示前10个文件路径
    std::cout << "\nFirst 10 files found:" << std::endl;
    for (size_t i = 0; i < wideImagePaths.size() && i < 10; i++) {
        // 转换为多字节字符串显示
        int pathLength = WideCharToMultiByte(CP_UTF8, 0, wideImagePaths[i].c_str(), -1, NULL, 0, NULL, NULL);
        if (pathLength > 0) {
            std::vector<char> pathBuffer(pathLength);
            int result = WideCharToMultiByte(CP_UTF8, 0, wideImagePaths[i].c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
            if (result > 0) {
                std::string narrowPath(&pathBuffer[0]);
                std::cout << "  " << (i + 1) << ". " << narrowPath << std::endl;
            }
        }
    }
    
    extractor.Cleanup();
}

// 比较新旧扫描方法
void compareSearchMethods() {
    std::cout << "\n=== Comparing Search Methods ===" << std::endl;
    
    ExifExtractor extractor;
    if (!extractor.Initialize()) {
        std::cout << "Failed to initialize EXIF extractor" << std::endl;
        return;
    }
    
    std::string testDirectory = "C:\\Users\\<USER>\\Pictures\\Screenshots";
    std::vector<std::string> imageExtensions = {".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff", ".webp"};
    
    // 测试旧方法
    std::cout << "Testing old search method..." << std::endl;
    auto startTime1 = std::chrono::high_resolution_clock::now();
    
    std::vector<std::string> oldResults;
    extractor.ScanDirectory(testDirectory, imageExtensions, oldResults);
    
    auto endTime1 = std::chrono::high_resolution_clock::now();
    auto duration1 = std::chrono::duration_cast<std::chrono::milliseconds>(endTime1 - startTime1);
    
    std::cout << "Old method: " << duration1.count() << " ms, found " << oldResults.size() << " files" << std::endl;
    
    // 测试新方法（通过宽字符）
    std::cout << "Testing new search method..." << std::endl;
    auto startTime2 = std::chrono::high_resolution_clock::now();
    
    std::vector<std::string> newResults;
    extractor.ScanDirectory(testDirectory, imageExtensions, newResults);
    
    auto endTime2 = std::chrono::high_resolution_clock::now();
    auto duration2 = std::chrono::duration_cast<std::chrono::milliseconds>(endTime2 - startTime2);
    
    std::cout << "New method: " << duration2.count() << " ms, found " << newResults.size() << " files" << std::endl;
    
    // 比较结果
    if (oldResults.size() == newResults.size()) {
        std::cout << "✓ Both methods found the same number of files" << std::endl;
    } else {
        std::cout << "⚠ Different number of files found!" << std::endl;
    }
    
    // 检查中文文件处理
    int chineseFilesOld = 0, chineseFilesNew = 0;
    for (const auto& path : oldResults) {
        for (unsigned char c : path) {
            if (c >= 128) {
                chineseFilesOld++;
                break;
            }
        }
    }
    
    for (const auto& path : newResults) {
        for (unsigned char c : path) {
            if (c >= 128) {
                chineseFilesNew++;
                break;
            }
        }
    }
    
    std::cout << "Chinese files - Old: " << chineseFilesOld << ", New: " << chineseFilesNew << std::endl;
    
    extractor.Cleanup();
}

int main() {
    std::cout << "=== EXIF Manager Search Function Rewrite Test ===" << std::endl;
    
    // 测试宽字符扫描
    testWideCharScan();
    
    // 比较新旧方法
    compareSearchMethods();
    
    std::string taskId = "search_rewrite_test_001";
    
    std::cout << "\n=== Testing Full EXIF Analysis with New Search ===" << std::endl;
    std::cout << "This will use the rewritten search function..." << std::endl;
    
    try {
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            searchRewriteProgressCallback,
            taskId,
            searchRewriteQueryTaskControl
        );
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
        
        std::cout << "\n=== Search Rewrite Test Results ===" << std::endl;
        std::cout << "Total analysis time: " << duration.count() << " seconds" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 解析结果
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            if (jsonResult.find("status") != jsonResult.end()) {
                std::cout << "Status: " << jsonResult["status"] << std::endl;
            }
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            if (jsonResult.find("statistics") != jsonResult.end()) {
                auto stats = jsonResult["statistics"];
                if (stats.find("successful_extractions") != stats.end()) {
                    std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                }
            }
            
            // 检查路径质量
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                int pathIssues = 0;
                int chineseFiles = 0;
                
                for (const auto& image : imageAnalysis) {
                    if (image.find("file_path") != image.end()) {
                        std::string filePath = image["file_path"];
                        
                        // 检查路径问题
                        if (filePath.find("????????") != std::string::npos) {
                            pathIssues++;
                        }
                        
                        // 检查中文文件
                        for (unsigned char c : filePath) {
                            if (c >= 128) {
                                chineseFiles++;
                                break;
                            }
                        }
                    }
                }
                
                std::cout << "Path quality check:" << std::endl;
                std::cout << "  Files with path issues: " << pathIssues << std::endl;
                std::cout << "  Files with Chinese characters: " << chineseFiles << std::endl;
                
                if (pathIssues == 0) {
                    std::cout << "✓ No path encoding issues found!" << std::endl;
                } else {
                    std::cout << "⚠ Found path encoding issues" << std::endl;
                }
            }
            
            // 检查文件保存
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "✓ Results saved to: " << outputFile << std::endl;
                }
            }
            
            std::cout << "\n✓ Search rewrite test completed successfully!" << std::endl;
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

﻿#include "../include/ServiceManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <algorithm>
#include <cctype>

#pragma comment(lib, "advapi32.lib")

ServiceManager::ServiceManager() : m_scManager(nullptr), m_initialized(false) {
}

ServiceManager::~ServiceManager() {
    Cleanup();
}

bool ServiceManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 打开服务控制管理器
    m_scManager = OpenSCManager(nullptr, nullptr, SC_MANAGER_ENUMERATE_SERVICE | SC_MANAGER_CONNECT);
    if (m_scManager == nullptr) {
        std::cout << "Failed to open Service Control Manager: " << GetLastError() << std::endl;
        return false;
    }

    m_initialized = true;
    return true;
}

void ServiceManager::Cleanup() {
    if (m_scManager) {
        CloseServiceHandle(m_scManager);
        m_scManager = nullptr;
    }
    m_initialized = false;
}

std::vector<ServiceData> ServiceManager::GetAllServices() {
    std::vector<ServiceData> allServices;

    if (!m_initialized) {
        return allServices;
    }

    // 第一次调用获取所需缓冲区大小
    DWORD bytesNeeded = 0;
    DWORD servicesReturned = 0;
    DWORD resumeHandle = 0;

    EnumServicesStatusEx(m_scManager, SC_ENUM_PROCESS_INFO, SERVICE_WIN32,
                        SERVICE_STATE_ALL, nullptr, 0, &bytesNeeded,
                        &servicesReturned, &resumeHandle, nullptr);

    if (GetLastError() != ERROR_MORE_DATA) {
        std::cout << "Failed to get services buffer size: " << GetLastError() << std::endl;
        return allServices;
    }

    // 分配缓冲区并获取服务列表
    std::vector<BYTE> buffer(bytesNeeded);
    LPENUM_SERVICE_STATUS_PROCESS services = reinterpret_cast<LPENUM_SERVICE_STATUS_PROCESS>(buffer.data());

    if (!EnumServicesStatusEx(m_scManager, SC_ENUM_PROCESS_INFO, SERVICE_WIN32,
                             SERVICE_STATE_ALL, buffer.data(), bytesNeeded,
                             &bytesNeeded, &servicesReturned, &resumeHandle, nullptr)) {
        std::cout << "Failed to enumerate services: " << GetLastError() << std::endl;
        return allServices;
    }

    // 遍历服务列表
    for (DWORD i = 0; i < servicesReturned; i++) {
        ServiceData serviceData;

        // 基本信息
        serviceData.service_name = ConvertToString(services[i].lpServiceName);
        serviceData.display_name = ConvertToString(services[i].lpDisplayName);
        serviceData.status = GetServiceStatusString(services[i].ServiceStatusProcess.dwCurrentState);
        serviceData.process_id = services[i].ServiceStatusProcess.dwProcessId;

        // 获取详细信息
        ServiceData detailedInfo = GetServiceDetails(serviceData.service_name);
        if (!detailedInfo.service_name.empty()) {
            serviceData.description = detailedInfo.description;
            serviceData.startup_type = detailedInfo.startup_type;
            serviceData.service_type = detailedInfo.service_type;
            serviceData.account = detailedInfo.account;
            serviceData.binary_path = detailedInfo.binary_path;
            serviceData.dependencies = detailedInfo.dependencies;
            serviceData.can_stop = detailedInfo.can_stop;
            serviceData.can_pause = detailedInfo.can_pause;
        }

        allServices.push_back(serviceData);
    }

    return allServices;
}

nlohmann::json ServiceManager::GetServicesInfoAsJson() {
    nlohmann::json result;

    // 获取所有服务
    std::vector<ServiceData> services = GetAllServices();
    result["services"] = services;

    // 统计信息
    int runningCount = 0;
    int stoppedCount = 0;
    int pausedCount = 0;

    for (const auto& service : services) {
        if (service.status == "Running") {
            runningCount++;
        } else if (service.status == "Stopped") {
            stoppedCount++;
        } else if (service.status == "Paused") {
            pausedCount++;
        }
    }

    // 添加元数据
    result["metadata"] = {
        {"total_services", services.size()},
        {"running_services", runningCount},
        {"stopped_services", stoppedCount},
        {"paused_services", pausedCount},
        {"scan_time", std::time(nullptr)},
        {"version", "1.0"}
    };

    return result;
}

bool ServiceManager::SaveServicesInfoToFile(const std::string& filename) {
    try {
        // 获取服务信息的JSON数据
        nlohmann::json serviceInfo = GetServicesInfoAsJson();

        // 打开文件进行写入
        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << "Failed to open file for writing: " << filename << std::endl;
            return false;
        }

        // 将JSON数据写入文件（格式化输出，缩进为4个空格）
        file << serviceInfo.dump(4);
        file.close();

        std::cout << "Services information saved to: " << filename << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cout << "Error saving services information to file: " << e.what() << std::endl;
        return false;
    }
}

// 封装的服务信息获取接口实现
std::string ServiceManager::Init_ServiceInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback("Initializing Service Manager...", 10);
        }

        // 创建服务管理器实例
        ServiceManager serviceManager;
        if (!serviceManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize Service Manager. Administrator privileges may be required."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback("Enumerating Windows services...", 50);
        }

        // 获取服务信息
        nlohmann::json serviceInfo = serviceManager.GetServicesInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：保存到文件
        if (progressCallback) {
            progressCallback("Saving services information to file...", 90);
        }

        // 保存服务信息到JSON文件
        std::string filename = "services_data.json";
        bool saveSuccess = serviceManager.SaveServicesInfoToFile(filename);

        // 报告进度：完成
        if (progressCallback) {
            if (saveSuccess) {
                progressCallback("Service enumeration and file save completed successfully", 100);
            } else {
                progressCallback("Service enumeration completed, but file save failed", 100);
            }
        }

        // 添加任务状态信息
        serviceInfo["status"] = "success";
        serviceInfo["task_id"] = taskId;
        serviceInfo["message"] = "Windows services information retrieved successfully";

        // 添加文件保存状态信息
        serviceInfo["file_save"] = {
            {"filename", filename},
            {"save_success", saveSuccess},
            {"save_message", saveSuccess ? "Services data saved to file successfully" : "Failed to save services data to file"}
        };

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            serviceInfo["request_params"] = params;
        }

        return serviceInfo.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during service enumeration", -1);
        }

        return errorResult.dump();
    }
}

// 辅助函数实现
std::wstring ServiceManager::ConvertToWString(const std::string& str) {
    if (str.empty()) return L"";

    int len = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
    if (len == 0) return L"";

    std::wstring result(len - 1, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &result[0], len);
    return result;
}

std::string ServiceManager::ConvertToString(const std::wstring& wstr) {
    if (wstr.empty()) return "";

    int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len == 0) return "";

    std::string result(len - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], len, nullptr, nullptr);
    return result;
}

std::string ServiceManager::GetServiceStatusString(DWORD status) {
    switch (status) {
        case SERVICE_STOPPED: return "Stopped";
        case SERVICE_START_PENDING: return "Start Pending";
        case SERVICE_STOP_PENDING: return "Stop Pending";
        case SERVICE_RUNNING: return "Running";
        case SERVICE_CONTINUE_PENDING: return "Continue Pending";
        case SERVICE_PAUSE_PENDING: return "Pause Pending";
        case SERVICE_PAUSED: return "Paused";
        default: return "Unknown";
    }
}

std::string ServiceManager::GetStartupTypeString(DWORD startType) {
    switch (startType) {
        case SERVICE_AUTO_START: return "Automatic";
        case SERVICE_BOOT_START: return "Boot";
        case SERVICE_DEMAND_START: return "Manual";
        case SERVICE_DISABLED: return "Disabled";
        case SERVICE_SYSTEM_START: return "System";
        default: return "Unknown";
    }
}

std::string ServiceManager::GetServiceTypeString(DWORD serviceType) {
    std::string result;

    if (serviceType & SERVICE_WIN32_OWN_PROCESS) {
        result += "Win32 Own Process";
    }
    if (serviceType & SERVICE_WIN32_SHARE_PROCESS) {
        if (!result.empty()) result += ", ";
        result += "Win32 Share Process";
    }
    if (serviceType & SERVICE_KERNEL_DRIVER) {
        if (!result.empty()) result += ", ";
        result += "Kernel Driver";
    }
    if (serviceType & SERVICE_FILE_SYSTEM_DRIVER) {
        if (!result.empty()) result += ", ";
        result += "File System Driver";
    }
    if (serviceType & SERVICE_INTERACTIVE_PROCESS) {
        if (!result.empty()) result += ", ";
        result += "Interactive";
    }

    return result.empty() ? "Unknown" : result;
}

ServiceData ServiceManager::GetServiceDetails(const std::string& serviceName) {
    ServiceData serviceData;
    serviceData.service_name = serviceName;

    // 打开服务句柄
    std::wstring wServiceName = ConvertToWString(serviceName);
    SC_HANDLE serviceHandle = OpenService(m_scManager, wServiceName.c_str(),
                                         SERVICE_QUERY_CONFIG | SERVICE_QUERY_STATUS);

    if (serviceHandle == nullptr) {
        return serviceData;
    }

    // 获取服务配置信息
    GetServiceConfig(serviceHandle, serviceData);

    // 获取服务描述
    serviceData.description = GetServiceDescription(serviceHandle);

    CloseServiceHandle(serviceHandle);
    return serviceData;
}

std::string ServiceManager::GetServiceDescription(SC_HANDLE serviceHandle) {
    DWORD bytesNeeded = 0;

    // 第一次调用获取所需缓冲区大小
    QueryServiceConfig2(serviceHandle, SERVICE_CONFIG_DESCRIPTION, nullptr, 0, &bytesNeeded);

    if (GetLastError() != ERROR_INSUFFICIENT_BUFFER) {
        return "";
    }

    // 分配缓冲区并获取描述
    std::vector<BYTE> buffer(bytesNeeded);
    LPSERVICE_DESCRIPTION serviceDesc = reinterpret_cast<LPSERVICE_DESCRIPTION>(buffer.data());

    if (QueryServiceConfig2(serviceHandle, SERVICE_CONFIG_DESCRIPTION,
                           buffer.data(), bytesNeeded, &bytesNeeded)) {
        if (serviceDesc->lpDescription) {
            return ConvertToString(serviceDesc->lpDescription);
        }
    }

    return "";
}

bool ServiceManager::GetServiceConfig(SC_HANDLE serviceHandle, ServiceData& serviceData) {
    DWORD bytesNeeded = 0;

    // 第一次调用获取所需缓冲区大小
    QueryServiceConfig(serviceHandle, nullptr, 0, &bytesNeeded);

    if (GetLastError() != ERROR_INSUFFICIENT_BUFFER) {
        return false;
    }

    // 分配缓冲区并获取配置
    std::vector<BYTE> buffer(bytesNeeded);
    LPQUERY_SERVICE_CONFIG serviceConfig = reinterpret_cast<LPQUERY_SERVICE_CONFIG>(buffer.data());

    if (QueryServiceConfig(serviceHandle, serviceConfig, bytesNeeded, &bytesNeeded)) {
        serviceData.startup_type = GetStartupTypeString(serviceConfig->dwStartType);
        serviceData.service_type = GetServiceTypeString(serviceConfig->dwServiceType);

        if (serviceConfig->lpServiceStartName) {
            serviceData.account = ConvertToString(serviceConfig->lpServiceStartName);
        }

        if (serviceConfig->lpBinaryPathName) {
            std::string rawPath = ConvertToString(serviceConfig->lpBinaryPathName);
            serviceData.binary_path = FormatBinaryPath(rawPath);
        }

        // 获取依赖服务
        if (serviceConfig->lpDependencies) {
            std::wstring deps = serviceConfig->lpDependencies;
            serviceData.dependencies = ConvertToString(deps);
        }

        return true;
    }

    return false;
}

// 路径格式化处理函数实现
std::string ServiceManager::FormatBinaryPath(const std::string& rawPath) {
    if (rawPath.empty()) {
        return rawPath;
    }

    // 提取可执行文件路径（去除引号和参数）
    std::string executablePath = ExtractExecutablePath(rawPath);

    // 标准化路径格式
    return NormalizePath(executablePath);
}

std::string ServiceManager::ExtractExecutablePath(const std::string& rawPath) {
    if (rawPath.empty()) {
        return rawPath;
    }

    std::string path = rawPath;

    // 去除首尾空格
    size_t start = path.find_first_not_of(" \t");
    if (start == std::string::npos) {
        return "";
    }
    size_t end = path.find_last_not_of(" \t");
    path = path.substr(start, end - start + 1);

    // 处理引号包围的路径
    if (path.length() >= 2 && path[0] == '"') {
        size_t endQuote = path.find('"', 1);
        if (endQuote != std::string::npos) {
            return path.substr(1, endQuote - 1);
        }
    }

    // 处理没有引号的路径，查找第一个空格作为参数分隔符
    size_t spacePos = path.find(' ');
    if (spacePos != std::string::npos) {
        // 检查空格前是否是有效的可执行文件路径
        std::string potentialPath = path.substr(0, spacePos);
        if (potentialPath.length() > 4) {
            std::string extension = potentialPath.substr(potentialPath.length() - 4);
            std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
            if (extension == ".exe" || extension == ".dll") {
                return potentialPath;
            }
        }
    }

    return path;
}

std::string ServiceManager::ExtractArguments(const std::string& rawPath) {
    if (rawPath.empty()) {
        return "";
    }

    std::string path = rawPath;

    // 去除首尾空格
    size_t start = path.find_first_not_of(" \t");
    if (start == std::string::npos) {
        return "";
    }
    size_t end = path.find_last_not_of(" \t");
    path = path.substr(start, end - start + 1);

    // 处理引号包围的路径
    if (path.length() >= 2 && path[0] == '"') {
        size_t endQuote = path.find('"', 1);
        if (endQuote != std::string::npos && endQuote + 1 < path.length()) {
            std::string args = path.substr(endQuote + 1);
            // 去除参数前的空格
            size_t argStart = args.find_first_not_of(" \t");
            if (argStart != std::string::npos) {
                return args.substr(argStart);
            }
        }
        return "";
    }

    // 处理没有引号的路径
    size_t spacePos = path.find(' ');
    if (spacePos != std::string::npos) {
        std::string potentialPath = path.substr(0, spacePos);
        if (potentialPath.length() > 4) {
            std::string extension = potentialPath.substr(potentialPath.length() - 4);
            std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
            if (extension == ".exe" || extension == ".dll") {
                std::string args = path.substr(spacePos + 1);
                // 去除参数前的空格
                size_t argStart = args.find_first_not_of(" \t");
                if (argStart != std::string::npos) {
                    return args.substr(argStart);
                }
            }
        }
    }

    return "";
}

std::string ServiceManager::NormalizePath(const std::string& path) {
    if (path.empty()) {
        return path;
    }

    std::string normalized = path;

    // 转换为标准的反斜杠分隔符
    std::replace(normalized.begin(), normalized.end(), '/', '\\');

    // 移除多余的反斜杠
    std::string result;
    bool lastWasBackslash = false;
    for (char c : normalized) {
        if (c == '\\') {
            if (!lastWasBackslash) {
                result += c;
                lastWasBackslash = true;
            }
        } else {
            result += c;
            lastWasBackslash = false;
        }
    }

    // 标准化大小写（Windows路径不区分大小写，统一转为小写）
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);

    return result;
}

﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// 账户锁定策略数据结构
struct AccountLockoutPolicyData {
    // 审核策略设置 - 您指定的10个字段
    bool audit_system_events;           // 审核系统事件
    bool audit_logon_events;            // 审核登录事件
    bool audit_object_access;           // 审核对象访问
    bool audit_privilege_use;           // 审核特权使用
    bool audit_process_tracking;        // 审核进程跟踪
    bool audit_policy_change;           // 审核策略更改
    bool audit_account_management;      // 审核账户管理
    bool audit_account_logon_events;    // 审核账户登录事件
    bool audit_directory_service_access; // 审核目录服务访问
    bool audit_account_management_detailed; // 审核账目管理
    
    // 锁定策略基本信息
    int lockout_threshold;              // 账户锁定阈值
    int lockout_duration_minutes;       // 账户锁定时间(分钟)
    int lockout_observation_window_minutes; // 重置账户锁定计数器(分钟)
    
    // 策略来源信息
    std::string policy_source;          // 策略来源 (Local/Domain/GPO)
    std::string last_modified;          // 最后修改时间
    std::string domain_name;            // 域名
    std::string domain_controller;      // 域控制器
    
    // 扫描信息
    std::time_t scan_time;              // 扫描时间
    std::string scan_duration;          // 扫描耗时
    
    // 审核策略详细状态
    std::string audit_policy_status;    // 审核策略总体状态
    std::vector<std::string> enabled_audit_categories; // 已启用的审核类别
    std::vector<std::string> disabled_audit_categories; // 已禁用的审核类别
    
    // 锁定统计信息
    int currently_locked_accounts;      // 当前锁定的账户数
    std::vector<std::string> locked_account_list; // 锁定账户列表
    
    AccountLockoutPolicyData() : audit_system_events(false), audit_logon_events(false),
                                audit_object_access(false), audit_privilege_use(false),
                                audit_process_tracking(false), audit_policy_change(false),
                                audit_account_management(false), audit_account_logon_events(false),
                                audit_directory_service_access(false), audit_account_management_detailed(false),
                                lockout_threshold(0), lockout_duration_minutes(0),
                                lockout_observation_window_minutes(0), scan_time(0),
                                currently_locked_accounts(0) {}
};

// 账户锁定策略统计信息
struct AccountLockoutStatistics {
    int total_audit_categories;        // 总审核类别数
    int enabled_audit_categories_count; // 已启用审核类别数
    int disabled_audit_categories_count; // 已禁用审核类别数
    double audit_coverage_percentage;   // 审核覆盖率
    std::string security_level;         // 安全级别评估
    std::time_t scan_time;              // 扫描时间
    std::string scan_duration;          // 扫描耗时
    
    AccountLockoutStatistics() : total_audit_categories(0), enabled_audit_categories_count(0),
                                disabled_audit_categories_count(0), audit_coverage_percentage(0.0),
                                scan_time(0) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(AccountLockoutPolicyData,
    audit_system_events, audit_logon_events, audit_object_access,
    audit_privilege_use, audit_process_tracking, audit_policy_change,
    audit_account_management, audit_account_logon_events, audit_directory_service_access,
    audit_account_management_detailed, lockout_threshold, lockout_duration_minutes,
    lockout_observation_window_minutes, policy_source, last_modified,
    domain_name, domain_controller, scan_time, scan_duration,
    audit_policy_status, enabled_audit_categories, disabled_audit_categories,
    currently_locked_accounts, locked_account_list)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(AccountLockoutStatistics,
    total_audit_categories, enabled_audit_categories_count, disabled_audit_categories_count,
    audit_coverage_percentage, security_level, scan_time, scan_duration)

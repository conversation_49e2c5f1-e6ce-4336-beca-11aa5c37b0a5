﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// Windows服务数据结构
struct ServiceData {
    std::string service_name;           // 服务名称
    std::string display_name;           // 显示名称
    std::string description;            // 服务描述
    std::string status;                 // 服务状态 (Running, Stopped, Paused等)
    std::string startup_type;           // 启动类型 (Automatic, Manual, Disabled等)
    std::string service_type;           // 服务类型 (Win32, Driver等)
    std::string account;                // 运行账户
    std::string binary_path;            // 可执行文件路径
    DWORD process_id;                   // 进程ID (如果正在运行)
    std::string dependencies;           // 依赖服务
    bool can_stop;                      // 是否可以停止
    bool can_pause;                     // 是否可以暂停
    std::string start_time;             // 启动时间

    ServiceData() : process_id(0), can_stop(false), can_pause(false) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ServiceData,
    service_name, display_name, description, status,
    startup_type, service_type, account, binary_path,
    process_id, dependencies, can_stop, can_pause, start_time)

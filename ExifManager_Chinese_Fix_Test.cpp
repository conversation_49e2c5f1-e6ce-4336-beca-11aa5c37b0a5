#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <windows.h>

// 简单的进度回调函数
void chineseFixProgressCallback(const std::string& message, int progress) {
    std::cout << "[Chinese Fix Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool chineseFixQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 测试UTF-8字符串清理功能
void testUtf8Cleaning() {
    std::cout << "=== Testing UTF-8 String Cleaning ===" << std::endl;
    
    // 这些测试字符串需要在源码中直接定义，因为我们无法在这里调用内部函数
    std::vector<std::string> testStrings = {
        "Normal ASCII text",
        "Path with spaces",
        "C:\\Users\\<USER>\\Pictures\\Screenshots",
        // 注意：中文字符串需要在实际运行时测试
    };
    
    for (const auto& testStr : testStrings) {
        std::cout << "Original: " << testStr << std::endl;
        // 实际的清理测试需要通过EXIF分析来验证
    }
}

// 检查字符串中是否包含问号（表示字符被替换）
int countQuestionMarks(const std::string& str) {
    int count = 0;
    for (char c : str) {
        if (c == '?') count++;
    }
    return count;
}

int main() {
    std::cout << "=== EXIF Manager Chinese Character Fix Test ===" << std::endl;
    
    // 首先测试UTF-8清理功能
    testUtf8Cleaning();
    
    std::string taskId = "chinese_fix_test_001";
    
    std::cout << "\n=== Starting EXIF Analysis with Chinese Character Fix ===" << std::endl;
    std::cout << "This test will verify that Chinese characters are preserved..." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            chineseFixProgressCallback,
            taskId,
            chineseFixQueryTaskControl
        );
        
        std::cout << "\n=== Chinese Character Fix Test Results ===" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 解析结果检查中文字符处理
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            std::cout << "\n=== Analysis Summary ===" << std::endl;
            if (jsonResult.find("status") != jsonResult.end()) {
                std::cout << "Status: " << jsonResult["status"] << std::endl;
            }
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            if (jsonResult.find("statistics") != jsonResult.end()) {
                auto stats = jsonResult["statistics"];
                if (stats.find("successful_extractions") != stats.end()) {
                    std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                }
            }
            
            // 检查中文字符处理情况
            std::cout << "\n=== Chinese Character Analysis ===" << std::endl;
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                int totalFiles = imageAnalysis.size();
                int filesWithQuestionMarks = 0;
                int chinesePathFiles = 0;
                int successfulChineseFiles = 0;
                
                for (const auto& image : imageAnalysis) {
                    if (image.find("file_path") != image.end()) {
                        std::string filePath = image["file_path"];
                        
                        // 检查是否包含问号（表示字符被替换）
                        int questionMarks = countQuestionMarks(filePath);
                        if (questionMarks > 0) {
                            filesWithQuestionMarks++;
                            std::cout << "File with question marks: " << filePath << " (" << questionMarks << " marks)" << std::endl;
                        }
                        
                        // 检查是否是中文路径（包含非ASCII字符）
                        bool hasNonAscii = false;
                        for (unsigned char c : filePath) {
                            if (c >= 128) {
                                hasNonAscii = true;
                                break;
                            }
                        }
                        
                        if (hasNonAscii) {
                            chinesePathFiles++;
                            if (image.find("success") != image.end() && image["success"].get<bool>()) {
                                successfulChineseFiles++;
                                std::cout << "✓ Successfully processed Chinese path: " << filePath.substr(0, 80) << "..." << std::endl;
                            }
                        }
                    }
                }
                
                std::cout << "\nChinese Character Processing Summary:" << std::endl;
                std::cout << "  Total files: " << totalFiles << std::endl;
                std::cout << "  Files with question marks: " << filesWithQuestionMarks << std::endl;
                std::cout << "  Files with Chinese characters: " << chinesePathFiles << std::endl;
                std::cout << "  Successfully processed Chinese files: " << successfulChineseFiles << std::endl;
                
                if (filesWithQuestionMarks == 0) {
                    std::cout << "✓ No question mark replacements found - Chinese characters preserved!" << std::endl;
                } else {
                    std::cout << "⚠ Found " << filesWithQuestionMarks << " files with character replacements" << std::endl;
                }
                
                if (chinesePathFiles > 0) {
                    double successRate = (double)successfulChineseFiles / chinesePathFiles * 100.0;
                    std::cout << "Chinese file success rate: " << std::fixed << std::setprecision(1) << successRate << "%" << std::endl;
                }
            }
            
            // 检查文件保存
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "\n✓ Results with Chinese characters saved to: " << outputFile << std::endl;
                } else {
                    std::cout << "\n⚠ Failed to save results to: " << outputFile << std::endl;
                }
            }
            
            std::cout << "\n✓ Chinese character fix test completed!" << std::endl;
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
            std::cout << "First 300 chars of result:" << std::endl;
            std::cout << result.substr(0, 300) << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

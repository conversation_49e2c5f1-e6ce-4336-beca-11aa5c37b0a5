﻿#include "AccountLockoutPolicyManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <set>
#include <algorithm>
#include <chrono>
#include <regex>
#include <wbemidl.h>
#include <comdef.h>
#include <sddl.h>
#include <userenv.h>
#include <dsgetdc.h>
#include <dsrole.h>
#include <winnt.h>

#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "oleaut32.lib")
#pragma comment(lib, "wbemuuid.lib")
#pragma comment(lib, "userenv.lib")
#pragma comment(lib, "netapi32.lib")

// 定义缺少的常量
#ifndef STATUS_SUCCESS
#define STATUS_SUCCESS ((NTSTATUS)0x00000000L)
#endif

#ifndef TIMEQ_FOREVER
#define TIMEQ_FOREVER ((DWORD)0xFFFFFFFF)
#endif

// 审核策略注册表路径
const std::string AccountLockoutPolicyManager::AUDIT_POLICY_REG_PATH = "SYSTEM\\CurrentControlSet\\Control\\Lsa\\Audit";
const std::string AccountLockoutPolicyManager::LSA_POLICY_REG_PATH = "SYSTEM\\CurrentControlSet\\Control\\Lsa";

AccountLockoutPolicyManager::AccountLockoutPolicyManager() : m_initialized(false), m_lsaHandle(nullptr) {
}

AccountLockoutPolicyManager::~AccountLockoutPolicyManager() {
    Cleanup();
}

bool AccountLockoutPolicyManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_startTime = std::chrono::system_clock::now();
    
    // 初始化COM
    HRESULT hr = CoInitializeEx(0, COINIT_MULTITHREADED);
    if (FAILED(hr)) {
        std::cout << u8"初始化COM失败: " << hr << std::endl;
        return false;
    }

    // 初始化LSA
    if (!InitializeLSA()) {
        std::cout << u8"初始化LSA失败" << std::endl;
        CoUninitialize();
        return false;
    }

    // 初始化WMI
    if (!InitializeWMI()) {
        std::cout << u8"初始化WMI失败" << std::endl;
        CleanupLSA();
        CoUninitialize();
        return false;
    }

    m_initialized = true;
    return true;
}

void AccountLockoutPolicyManager::Cleanup() {
    if (m_initialized) {
        CleanupWMI();
        CleanupLSA();
        CoUninitialize();
        m_initialized = false;
    }
}

AccountLockoutPolicyData AccountLockoutPolicyManager::GetAccountLockoutPolicy() {
    AccountLockoutPolicyData policy;

    if (!m_initialized) {
        return policy;
    }

    try {
        // 检查是否在域环境中
        if (IsComputerInDomain()) {
            policy.policy_source = "Domain";
            policy.domain_name = GetDomainName();
            policy.domain_controller = GetDomainController();
        } else {
            policy.policy_source = "Local";
        }

        // 获取基本锁定策略
        GetBasicLockoutPolicy(policy);
        
        // 获取审核策略设置 - 核心功能
        GetAuditPolicySettings(policy);
        
        // 获取当前锁定账户信息
        policy.locked_account_list = GetCurrentlyLockedAccounts();
        policy.currently_locked_accounts = static_cast<int>(policy.locked_account_list.size());
        
        // 获取域信息
        GetDomainInfo(policy);
        
        // 设置最后修改时间
        policy.last_modified = GetCurrentTimestamp();
        policy.scan_time = std::time(nullptr);

    } catch (const std::exception& e) {
        std::cout << u8"获取账户锁定策略时发生错误: " << e.what() << std::endl;
    }

    return policy;
}

AccountLockoutStatistics AccountLockoutPolicyManager::GetAccountLockoutStatistics() {
    AccountLockoutStatistics stats;
    
    AccountLockoutPolicyData policy = GetAccountLockoutPolicy();
    
    stats.scan_time = std::time(nullptr);

    // 计算扫描耗时
    auto endTime = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_startTime);
    stats.scan_duration = std::to_string(duration.count()) + " ms";

    // 统计审核类别
    stats.total_audit_categories = 10; // 您指定的10个审核字段
    
    // 计算已启用的审核类别数
    int enabledCount = 0;
    if (policy.audit_system_events) enabledCount++;
    if (policy.audit_logon_events) enabledCount++;
    if (policy.audit_object_access) enabledCount++;
    if (policy.audit_privilege_use) enabledCount++;
    if (policy.audit_process_tracking) enabledCount++;
    if (policy.audit_policy_change) enabledCount++;
    if (policy.audit_account_management) enabledCount++;
    if (policy.audit_account_logon_events) enabledCount++;
    if (policy.audit_directory_service_access) enabledCount++;
    if (policy.audit_account_management_detailed) enabledCount++;
    
    stats.enabled_audit_categories_count = enabledCount;
    stats.disabled_audit_categories_count = stats.total_audit_categories - enabledCount;
    
    // 计算审核覆盖率
    stats.audit_coverage_percentage = CalculateAuditCoverage(policy);
    
    // 评估安全级别
    stats.security_level = EvaluateSecurityLevel(policy);

    // 注意：已移除安全建议字段

    return stats;
}

nlohmann::json AccountLockoutPolicyManager::GetAccountLockoutPolicyInfoAsJson() {
    nlohmann::json result;

    try {
        AccountLockoutPolicyData policy = GetAccountLockoutPolicy();
        AccountLockoutStatistics stats = GetAccountLockoutStatistics();

        // 构建JSON结果
        result["metadata"] = {
            {"tool_name", u8"Windows账户锁定策略扫描器"},
            {"version", "1.0.0"},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration},
            {"policy_source", policy.policy_source}
        };

        // 审核策略信息 - 您指定的10个字段
        result["audit_policies"] = {
            {"audit_system_events", policy.audit_system_events},
            {"audit_logon_events", policy.audit_logon_events},
            {"audit_object_access", policy.audit_object_access},
            {"audit_privilege_use", policy.audit_privilege_use},
            {"audit_process_tracking", policy.audit_process_tracking},
            {"audit_policy_change", policy.audit_policy_change},
            {"audit_account_management", policy.audit_account_management},
            {"audit_account_logon_events", policy.audit_account_logon_events},
            {"audit_directory_service_access", policy.audit_directory_service_access},
            {"audit_account_management_detailed", policy.audit_account_management_detailed}
        };

        // 锁定策略基本信息
        result["lockout_policy"] = {
            {"lockout_threshold", policy.lockout_threshold},
            {"lockout_duration_minutes", policy.lockout_duration_minutes},
            {"lockout_observation_window_minutes", policy.lockout_observation_window_minutes}
        };

        // 当前锁定账户信息
        result["locked_accounts"] = {
            {"currently_locked_count", policy.currently_locked_accounts},
            {"locked_account_list", policy.locked_account_list}
        };

        // 统计信息（不包含security_recommendations字段）
        result["statistics"] = {
            {"total_audit_categories", stats.total_audit_categories},
            {"enabled_audit_categories_count", stats.enabled_audit_categories_count},
            {"disabled_audit_categories_count", stats.disabled_audit_categories_count},
            {"audit_coverage_percentage", stats.audit_coverage_percentage},
            {"security_level", stats.security_level},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration}
        };

        // 域信息
        if (!policy.domain_name.empty()) {
            result["domain_info"] = {
                {"domain_name", policy.domain_name},
                {"domain_controller", policy.domain_controller}
            };
        }

        return result;
    }
    catch (const std::exception& e) {
        nlohmann::json error_result;
        error_result["error"] = u8"获取账户锁定策略信息失败";
        error_result["details"] = e.what();
        return error_result;
    }
}

bool AccountLockoutPolicyManager::SaveAccountLockoutPolicyInfoToFile(const std::string& filename) {
    try {
        nlohmann::json policyInfo = GetAccountLockoutPolicyInfoAsJson();

        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << u8"无法打开文件进行写入: " << filename << std::endl;
            return false;
        }

        file << policyInfo.dump(4); // 4 spaces indentation
        file.close();

        std::cout << u8"账户锁定策略信息已保存到: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cout << u8"保存账户锁定策略信息时发生错误: " << e.what() << std::endl;
        return false;
    }
}

// 封装的账户锁定策略信息获取接口实现
std::string AccountLockoutPolicyManager::Init_AccountLockoutPolicyInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化账户锁定策略管理器...", 10);
        }

        // 创建账户锁定策略管理器实例
        AccountLockoutPolicyManager lockoutManager;
        if (!lockoutManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化账户锁定策略管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描账户锁定策略和审核设置...", 50);
        }

        // 获取账户锁定策略信息
        nlohmann::json lockoutPolicyInfo = lockoutManager.GetAccountLockoutPolicyInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"账户锁定策略扫描完成", 100);
        }

        // 添加任务状态信息
        lockoutPolicyInfo["status"] = "success";
        lockoutPolicyInfo["task_id"] = taskId;
        lockoutPolicyInfo["message"] = u8"账户锁定策略信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            lockoutPolicyInfo["request_params"] = params;
        }

        return lockoutPolicyInfo.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"账户锁定策略扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}

// 辅助函数实现
std::string AccountLockoutPolicyManager::ConvertToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::string AccountLockoutPolicyManager::ConvertToString(LPCWSTR wstr) {
    if (wstr == nullptr) return std::string();

    int len = wcslen(wstr);
    if (len == 0) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr, len, NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr, len, &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::wstring AccountLockoutPolicyManager::ConvertToWString(const std::string& str) {
    if (str.empty()) return std::wstring();

    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

std::string AccountLockoutPolicyManager::ConvertLsaUnicodeStringToString(const LSA_UNICODE_STRING& lsaString) {
    if (lsaString.Buffer == nullptr || lsaString.Length == 0) {
        return "";
    }

    std::wstring wstr(lsaString.Buffer, lsaString.Length / sizeof(WCHAR));
    return ConvertToString(wstr);
}

// LSA策略操作实现
bool AccountLockoutPolicyManager::InitializeLSA() {
    LSA_OBJECT_ATTRIBUTES objectAttributes;
    ZeroMemory(&objectAttributes, sizeof(objectAttributes));

    NTSTATUS status = LsaOpenPolicy(nullptr, &objectAttributes, POLICY_ALL_ACCESS, &m_lsaHandle);
    if (status != STATUS_SUCCESS) {
        std::cout << u8"打开LSA策略失败: " << GetNTStatusString(status) << std::endl;
        return false;
    }

    return true;
}

void AccountLockoutPolicyManager::CleanupLSA() {
    if (m_lsaHandle != nullptr) {
        LsaClose(m_lsaHandle);
        m_lsaHandle = nullptr;
    }
}

bool AccountLockoutPolicyManager::GetLSAPolicy(POLICY_INFORMATION_CLASS infoClass, PVOID* buffer) {
    if (m_lsaHandle == nullptr) {
        return false;
    }

    NTSTATUS status = LsaQueryInformationPolicy(m_lsaHandle, infoClass, buffer);
    return (status == STATUS_SUCCESS);
}

// 获取基本锁定策略
bool AccountLockoutPolicyManager::GetBasicLockoutPolicy(AccountLockoutPolicyData& policy) {
    try {
        // 使用NetUserModalsGet获取账户锁定策略
        USER_MODALS_INFO_3* pUserModals3 = nullptr;
        NET_API_STATUS status = NetUserModalsGet(nullptr, 3, (LPBYTE*)&pUserModals3);

        if (status == NERR_Success && pUserModals3 != nullptr) {
            policy.lockout_threshold = pUserModals3->usrmod3_lockout_threshold;

            // 处理锁定持续时间（特殊值处理）
            if (pUserModals3->usrmod3_lockout_duration == TIMEQ_FOREVER || pUserModals3->usrmod3_lockout_duration == 0) {
                policy.lockout_duration_minutes = 0; // 永久锁定或无锁定
            } else {
                policy.lockout_duration_minutes = pUserModals3->usrmod3_lockout_duration / 60;
            }

            // 处理锁定观察窗口
            if (pUserModals3->usrmod3_lockout_observation_window == TIMEQ_FOREVER || pUserModals3->usrmod3_lockout_observation_window == 0) {
                policy.lockout_observation_window_minutes = 0;
            } else {
                policy.lockout_observation_window_minutes = pUserModals3->usrmod3_lockout_observation_window / 60;
            }

            NetApiBufferFree(pUserModals3);
            return true;
        }

    } catch (const std::exception& e) {
        std::cout << u8"获取基本锁定策略时发生错误: " << e.what() << std::endl;
    }

    return false;
}

// 获取审核策略设置 - 核心功能实现
bool AccountLockoutPolicyManager::GetAuditPolicySettings(AccountLockoutPolicyData& policy) {
    try {
        // 方法1: 通过注册表获取审核策略
        HKEY hKey;
        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, AUDIT_POLICY_REG_PATH.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {

            // 审核系统事件
            policy.audit_system_events = ReadRegistryBool(hKey, "", "AuditSystemEvents");

            // 审核登录事件
            policy.audit_logon_events = ReadRegistryBool(hKey, "", "AuditLogonEvents");

            // 审核对象访问
            policy.audit_object_access = ReadRegistryBool(hKey, "", "AuditObjectAccess");

            // 审核特权使用
            policy.audit_privilege_use = ReadRegistryBool(hKey, "", "AuditPrivilegeUse");

            // 审核进程跟踪
            policy.audit_process_tracking = ReadRegistryBool(hKey, "", "AuditProcessTracking");

            // 审核策略更改
            policy.audit_policy_change = ReadRegistryBool(hKey, "", "AuditPolicyChange");

            // 审核账户管理
            policy.audit_account_management = ReadRegistryBool(hKey, "", "AuditAccountManage");

            // 审核账户登录事件
            policy.audit_account_logon_events = ReadRegistryBool(hKey, "", "AuditAccountLogon");

            // 审核目录服务访问
            policy.audit_directory_service_access = ReadRegistryBool(hKey, "", "AuditDSAccess");

            // 审核账目管理（详细）
            policy.audit_account_management_detailed = ReadRegistryBool(hKey, "", "AuditAccountManageDetailed");

            RegCloseKey(hKey);
        }

        // 方法2: 通过LSA策略获取（备用方法）
        if (m_lsaHandle != nullptr) {
            PPOLICY_AUDIT_EVENTS_INFO pAuditInfo = nullptr;
            if (GetLSAPolicy(PolicyAuditEventsInformation, (PVOID*)&pAuditInfo)) {
                if (pAuditInfo != nullptr && pAuditInfo->AuditingMode) {
                    // LSA审核策略处理逻辑
                    // 这里可以进一步解析LSA返回的审核信息
                    LsaFreeMemory(pAuditInfo);
                }
            }
        }

        // 构建审核策略状态摘要
        std::vector<std::string> enabledCategories;
        std::vector<std::string> disabledCategories;

        if (policy.audit_system_events) enabledCategories.push_back(u8"系统事件"); else disabledCategories.push_back(u8"系统事件");
        if (policy.audit_logon_events) enabledCategories.push_back(u8"登录事件"); else disabledCategories.push_back(u8"登录事件");
        if (policy.audit_object_access) enabledCategories.push_back(u8"对象访问"); else disabledCategories.push_back(u8"对象访问");
        if (policy.audit_privilege_use) enabledCategories.push_back(u8"特权使用"); else disabledCategories.push_back(u8"特权使用");
        if (policy.audit_process_tracking) enabledCategories.push_back(u8"进程跟踪"); else disabledCategories.push_back(u8"进程跟踪");
        if (policy.audit_policy_change) enabledCategories.push_back(u8"策略更改"); else disabledCategories.push_back(u8"策略更改");
        if (policy.audit_account_management) enabledCategories.push_back(u8"账户管理"); else disabledCategories.push_back(u8"账户管理");
        if (policy.audit_account_logon_events) enabledCategories.push_back(u8"账户登录事件"); else disabledCategories.push_back(u8"账户登录事件");
        if (policy.audit_directory_service_access) enabledCategories.push_back(u8"目录服务访问"); else disabledCategories.push_back(u8"目录服务访问");
        if (policy.audit_account_management_detailed) enabledCategories.push_back(u8"账目管理"); else disabledCategories.push_back(u8"账目管理");

        policy.enabled_audit_categories = enabledCategories;
        policy.disabled_audit_categories = disabledCategories;

        if (enabledCategories.size() > disabledCategories.size()) {
            policy.audit_policy_status = u8"大部分审核类别已启用";
        } else if (enabledCategories.size() == 0) {
            policy.audit_policy_status = u8"所有审核类别均已禁用";
        } else {
            policy.audit_policy_status = u8"部分审核类别已启用";
        }

        return true;

    } catch (const std::exception& e) {
        std::cout << u8"获取审核策略设置时发生错误: " << e.what() << std::endl;
    }

    return false;
}

// 获取当前锁定的账户列表
std::vector<std::string> AccountLockoutPolicyManager::GetCurrentlyLockedAccounts() {
    std::vector<std::string> lockedAccounts;

    try {
        PNET_DISPLAY_USER pUsers = nullptr;
        DWORD entriesRead = 0;
        DWORD totalEntries = 0;
        DWORD resumeHandle = 0;

        NET_API_STATUS status = NetQueryDisplayInformation(
            nullptr, 1, 0, 1000, MAX_PREFERRED_LENGTH,
            &entriesRead, (PVOID*)&pUsers);

        if (status == NERR_Success || status == ERROR_MORE_DATA) {
            for (DWORD i = 0; i < entriesRead; i++) {
                std::string username = ConvertToString(pUsers[i].usri1_name);

                // 检查用户是否被锁定
                std::wstring wUsername = ConvertToWString(username);
                PUSER_INFO_3 pUserInfo = nullptr;

                NET_API_STATUS userStatus = NetUserGetInfo(nullptr, wUsername.c_str(), 3, (LPBYTE*)&pUserInfo);

                if (userStatus == NERR_Success && pUserInfo != nullptr) {
                    if (pUserInfo->usri3_flags & UF_LOCKOUT) {
                        lockedAccounts.push_back(username);
                    }
                    NetApiBufferFree(pUserInfo);
                }
            }
            NetApiBufferFree(pUsers);
        }

    } catch (const std::exception& e) {
        std::cout << u8"获取锁定账户列表时发生错误: " << e.what() << std::endl;
    }

    return lockedAccounts;
}

int AccountLockoutPolicyManager::CountLockedAccounts() {
    return static_cast<int>(GetCurrentlyLockedAccounts().size());
}

// 域信息获取
bool AccountLockoutPolicyManager::GetDomainInfo(AccountLockoutPolicyData& policy) {
    try {
        if (IsComputerInDomain()) {
            policy.domain_name = GetDomainName();
            policy.domain_controller = GetDomainController();
            return true;
        }
    } catch (const std::exception& e) {
        std::cout << u8"获取域信息时发生错误: " << e.what() << std::endl;
    }
    return false;
}

std::string AccountLockoutPolicyManager::GetDomainName() {
    try {
        PDOMAIN_CONTROLLER_INFOA pDomainInfo = nullptr;
        DWORD result = DsGetDcNameA(nullptr, nullptr, nullptr, nullptr, 0, &pDomainInfo);

        if (result == ERROR_SUCCESS && pDomainInfo != nullptr) {
            std::string domainName = pDomainInfo->DomainName;
            NetApiBufferFree(pDomainInfo);
            return domainName;
        }
    } catch (const std::exception& e) {
        std::cout << u8"获取域名时发生错误: " << e.what() << std::endl;
    }
    return "";
}

std::string AccountLockoutPolicyManager::GetDomainController() {
    try {
        PDOMAIN_CONTROLLER_INFOA pDomainInfo = nullptr;
        DWORD result = DsGetDcNameA(nullptr, nullptr, nullptr, nullptr, 0, &pDomainInfo);

        if (result == ERROR_SUCCESS && pDomainInfo != nullptr) {
            std::string dcName = pDomainInfo->DomainControllerName;
            NetApiBufferFree(pDomainInfo);
            return dcName;
        }
    } catch (const std::exception& e) {
        std::cout << u8"获取域控制器时发生错误: " << e.what() << std::endl;
    }
    return "";
}

bool AccountLockoutPolicyManager::IsComputerInDomain() {
    try {
        PDSROLE_PRIMARY_DOMAIN_INFO_BASIC pDomainInfo = nullptr;
        DWORD result = DsRoleGetPrimaryDomainInformation(nullptr, DsRolePrimaryDomainInfoBasic, (PBYTE*)&pDomainInfo);

        if (result == ERROR_SUCCESS && pDomainInfo != nullptr) {
            bool inDomain = (pDomainInfo->MachineRole == DsRole_RoleMemberWorkstation ||
                           pDomainInfo->MachineRole == DsRole_RoleMemberServer);
            DsRoleFreeMemory(pDomainInfo);
            return inDomain;
        }
    } catch (const std::exception& e) {
        std::cout << u8"检查域成员身份时发生错误: " << e.what() << std::endl;
    }
    return false;
}

// 安全分析功能
std::string AccountLockoutPolicyManager::EvaluateSecurityLevel(const AccountLockoutPolicyData& policy) {
    int securityScore = 0;

    // 基于启用的审核类别计算安全分数
    if (policy.audit_system_events) securityScore += 10;
    if (policy.audit_logon_events) securityScore += 15;
    if (policy.audit_object_access) securityScore += 10;
    if (policy.audit_privilege_use) securityScore += 15;
    if (policy.audit_process_tracking) securityScore += 5;
    if (policy.audit_policy_change) securityScore += 20;
    if (policy.audit_account_management) securityScore += 15;
    if (policy.audit_account_logon_events) securityScore += 10;
    if (policy.audit_directory_service_access) securityScore += 5;
    if (policy.audit_account_management_detailed) securityScore += 5;

    // 基于锁定策略计算分数
    if (policy.lockout_threshold > 0 && policy.lockout_threshold <= 5) securityScore += 10;
    if (policy.lockout_duration_minutes > 0) securityScore += 5;
    if (policy.lockout_observation_window_minutes > 0) securityScore += 5;

    if (securityScore >= 80) return u8"高";
    else if (securityScore >= 50) return u8"中";
    else return u8"低";
}

double AccountLockoutPolicyManager::CalculateAuditCoverage(const AccountLockoutPolicyData& policy) {
    int enabledCount = 0;
    int totalCount = 10; // 您指定的10个审核字段

    if (policy.audit_system_events) enabledCount++;
    if (policy.audit_logon_events) enabledCount++;
    if (policy.audit_object_access) enabledCount++;
    if (policy.audit_privilege_use) enabledCount++;
    if (policy.audit_process_tracking) enabledCount++;
    if (policy.audit_policy_change) enabledCount++;
    if (policy.audit_account_management) enabledCount++;
    if (policy.audit_account_logon_events) enabledCount++;
    if (policy.audit_directory_service_access) enabledCount++;
    if (policy.audit_account_management_detailed) enabledCount++;

    return (static_cast<double>(enabledCount) / totalCount) * 100.0;
}

// GenerateSecurityRecommendations函数已移除

// 时间转换
std::string AccountLockoutPolicyManager::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    struct tm timeinfo;
    if (localtime_s(&timeinfo, &time_t) == 0) {
        std::ostringstream oss;
        oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }
    return "Unknown";
}

// 注册表操作
std::string AccountLockoutPolicyManager::ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return "";
        }
        needClose = true;
    }

    char buffer[1024];
    DWORD bufferSize = sizeof(buffer);
    DWORD type;

    LONG result = RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &type, (LPBYTE)buffer, &bufferSize);

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    if (result == ERROR_SUCCESS && type == REG_SZ) {
        return std::string(buffer);
    }

    return "";
}

DWORD AccountLockoutPolicyManager::ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey = hKey;
    bool needClose = false;

    if (!subKey.empty()) {
        if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
            return 0;
        }
        needClose = true;
    }

    DWORD value = 0;
    DWORD valueSize = sizeof(value);
    DWORD type;

    LONG result = RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &type, (LPBYTE)&value, &valueSize);

    if (needClose) {
        RegCloseKey(hSubKey);
    }

    if (result == ERROR_SUCCESS && type == REG_DWORD) {
        return value;
    }

    return 0;
}

bool AccountLockoutPolicyManager::ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    return ReadRegistryDWORD(hKey, subKey, valueName) != 0;
}

// 错误处理
std::string AccountLockoutPolicyManager::GetLastErrorString() {
    DWORD error = GetLastError();
    LPSTR messageBuffer = nullptr;

    FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                   nullptr, error, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                   (LPSTR)&messageBuffer, 0, nullptr);

    std::string message = messageBuffer ? messageBuffer : "Unknown error";
    LocalFree(messageBuffer);

    return message;
}

std::string AccountLockoutPolicyManager::GetNTStatusString(NTSTATUS status) {
    return "NTSTATUS: 0x" + std::to_string(status);
}

void AccountLockoutPolicyManager::LogError(const std::string& error) {
    std::cout << u8"[错误] " << error << std::endl;
}

// WMI相关（简化实现）
bool AccountLockoutPolicyManager::InitializeWMI() {
    // 简化的WMI初始化
    return true;
}

void AccountLockoutPolicyManager::CleanupWMI() {
    // WMI清理
}

std::string AccountLockoutPolicyManager::ExecuteWMIQuery(const std::string& query) {
    // 简化的WMI查询实现
    return "";
}

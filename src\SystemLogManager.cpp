﻿#define _CRT_SECURE_NO_WARNINGS
#include "../include/SystemLogManager.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <chrono>
#include <fstream>

// 静态常量定义
const char* SystemLogManager::SYSTEM_LOG_NAME = "System";
const char* SystemLogManager::SECURITY_LOG_NAME = "Security";
const char* SystemLogManager::APPLICATION_LOG_NAME = "Application";

const std::vector<std::string> SystemLogManager::LOGIN_EVENT_IDS = {
    "4624", "4625", "4634", "4647", "4648", "4672", "4720", "4722", "4723", "4724"
};

const std::vector<std::string> SystemLogManager::LOGOUT_EVENT_IDS = {
    "4634", "4647"
};

const std::vector<std::string> SystemLogManager::POWER_EVENT_IDS = {
    "6005", "6006", "6008", "6009", "6013", "1074", "1076", "42", "107", "109"
};

const std::vector<std::string> SystemLogManager::SYSTEM_EVENT_IDS = {
    "6005", "6006", "6008", "6009", "6013", "7034", "7035", "7036", "7040"
};

// 构造函数
SystemLogManager::SystemLogManager() : m_cancelled(false) {
}

// 析构函数
SystemLogManager::~SystemLogManager() {
    m_cancelled = true;
}

// 统一接口 - 系统日志
std::string SystemLogManager::Init_SystemLogInfoMsg(
    const std::string& params,
    std::function<void(const std::string&, int)> progressCallback,
    const std::string& taskId,
    std::function<bool(const std::string&)> queryTaskControl
) {
    SystemLogManager manager;
    
    try {
        if (progressCallback) {
            progressCallback("Starting system log analysis...", 0);
        }
        
        // 解析查询参数
        LogQueryParams queryParams = SystemLogUtils::ParseQueryParams(params);
        queryParams.log_type = LogType::System;
        
        if (progressCallback) {
            progressCallback("Reading system event logs...", 20);
        }
        
        // 检查任务是否被取消
        if (queryTaskControl && queryTaskControl(taskId)) {
            nlohmann::json result;
            result["status"] = "cancelled";
            result["message"] = "Task cancelled";
            return result.dump(4);
        }
        
        // 获取系统日志
        std::vector<LogEntry> logs = manager.GetSystemLogs(queryParams);
        
        if (progressCallback) {
            progressCallback("Analyzing log data...", 60);
        }
        
        // 获取统计信息
        LogStatistics stats = manager.GetLogStatistics(LogType::System);
        
        if (progressCallback) {
            progressCallback("Generating results...", 80);
        }
        
        // 构建JSON结果
        nlohmann::json result;
        result["status"] = "success";
        result["message"] = "System log analysis completed";
        result["log_type"] = "system";
        result["task_id"] = taskId;
        
        // 日志数据
        nlohmann::json logArray = nlohmann::json::array();
        for (const auto& log : logs) {
            logArray.push_back(log.toJson());
        }
        result["logs"] = logArray;
        
        // 统计信息
        result["statistics"] = stats.toJson();
        
        if (progressCallback) {
            progressCallback("System log analysis completed", 100);
        }
        
        // 保存结果到JSON文件
        try {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::tm tm_info;
            localtime_s(&tm_info, &time_t);
            
            std::ostringstream filename;
            filename << "system_logs_" 
                     << std::put_time(&tm_info, "%Y%m%d_%H%M%S") 
                     << ".json";
            
            std::ofstream outFile(filename.str());
            if (outFile.is_open()) {
                outFile << result.dump(4);
                outFile.close();
                result["output_file"] = filename.str();
            }
        } catch (...) {
            // 忽略文件保存错误
        }
        
        return result.dump(4);
        
    } catch (const std::exception& e) {
        nlohmann::json errorResult;
        errorResult["status"] = "error";
        errorResult["message"] = "Failed to get system logs";

        // 安全处理异常信息，避免编码问题
        try {
            std::string errorMsg = e.what();
            bool hasNonAscii = false;
            for (char c : errorMsg) {
                if ((unsigned char)c > 127) {
                    hasNonAscii = true;
                    break;
                }
            }
            errorResult["error_details"] = hasNonAscii ? "Encoding error, cannot display details" : errorMsg;
        } catch (...) {
            errorResult["error_details"] = "Unknown error";
        }

        errorResult["task_id"] = taskId;
        return errorResult.dump(4);
    }
}

// 统一接口 - 登录日志
std::string SystemLogManager::Init_LoginLogInfoMsg(
    const std::string& params,
    std::function<void(const std::string&, int)> progressCallback,
    const std::string& taskId,
    std::function<bool(const std::string&)> queryTaskControl
) {
    SystemLogManager manager;
    
    try {
        if (progressCallback) {
            progressCallback("Starting login log analysis...", 0);
        }
        
        // 解析参数
        int maxEntries = 1000;
        int daysBack = 30;
        
        if (!params.empty()) {
            try {
                nlohmann::json paramJson = nlohmann::json::parse(params);
                if (paramJson.contains("max_entries")) {
                    maxEntries = paramJson["max_entries"];
                }
                if (paramJson.contains("days_back")) {
                    daysBack = paramJson["days_back"];
                }
            } catch (...) {
                // 使用默认参数
            }
        }
        
        if (progressCallback) {
            progressCallback("Reading security event logs...", 20);
        }
        
        // 检查任务取消
        if (queryTaskControl && queryTaskControl(taskId)) {
            nlohmann::json result;
            result["status"] = "cancelled";
            result["message"] = "Task cancelled";
            return result.dump(4);
        }
        
        // 获取登录日志
        std::vector<LoginLogEntry> logs = manager.GetLoginLogs(maxEntries, daysBack);
        
        if (progressCallback) {
            progressCallback("Analyzing login data...", 60);
        }
        
        // 获取统计信息
        LogStatistics stats = manager.GetLoginLogStatistics();
        
        if (progressCallback) {
            progressCallback("Generating results...", 80);
        }
        
        // 构建JSON结果
        nlohmann::json result;
        result["status"] = "success";
        result["message"] = "Login log analysis completed";
        result["log_type"] = "login";
        result["task_id"] = taskId;
        
        // 登录日志数据 - 使用统一的"logs"字段名
        nlohmann::json logArray = nlohmann::json::array();
        for (const auto& log : logs) {
            logArray.push_back(log.toJson());
        }
        result["logs"] = logArray;
        
        // 统计信息
        result["statistics"] = stats.toJson();
        
        // 登录模式分析
        result["login_patterns"] = SystemLogUtils::AnalyzeLoginPatterns(logs);
        
        // 失败登录分析
        auto failedLogins = SystemLogUtils::FindFailedLogins(logs);
        nlohmann::json failedArray = nlohmann::json::array();
        for (const auto& failed : failedLogins) {
            failedArray.push_back(failed.toJson());
        }
        result["failed_logins"] = failedArray;
        
        if (progressCallback) {
            progressCallback("Login log analysis completed", 100);
        }
        
        // 保存结果到JSON文件
        try {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::tm tm_info;
            localtime_s(&tm_info, &time_t);
            
            std::ostringstream filename;
            filename << "login_logs_" 
                     << std::put_time(&tm_info, "%Y%m%d_%H%M%S") 
                     << ".json";
            
            std::ofstream outFile(filename.str());
            if (outFile.is_open()) {
                outFile << result.dump(4);
                outFile.close();
                result["output_file"] = filename.str();
            }
        } catch (...) {
            // 忽略文件保存错误
        }
        
        return result.dump(4);
        
    } catch (const std::exception& e) {
        nlohmann::json errorResult;
        errorResult["status"] = "error";
        errorResult["message"] = "Failed to get login logs";

        // 安全处理异常信息，避免编码问题
        try {
            std::string errorMsg = e.what();
            bool hasNonAscii = false;
            for (char c : errorMsg) {
                if ((unsigned char)c > 127) {
                    hasNonAscii = true;
                    break;
                }
            }
            errorResult["error_details"] = hasNonAscii ? "Encoding error, cannot display details" : errorMsg;
        } catch (...) {
            errorResult["error_details"] = "Unknown error";
        }

        errorResult["task_id"] = taskId;
        return errorResult.dump(4);
    }
}

// 统一接口 - 安全日志
std::string SystemLogManager::Init_SecurityLogInfoMsg(
    const std::string& params,
    std::function<void(const std::string&, int)> progressCallback,
    const std::string& taskId,
    std::function<bool(const std::string&)> queryTaskControl
) {
    SystemLogManager manager;

    try {
        if (progressCallback) {
            progressCallback("Starting security log analysis...", 0);
        }

        // 解析查询参数
        LogQueryParams queryParams = SystemLogUtils::ParseQueryParams(params);
        queryParams.log_type = LogType::Security;

        if (progressCallback) {
            progressCallback("Reading security event logs...", 20);
        }

        // 检查任务取消
        if (queryTaskControl && queryTaskControl(taskId)) {
            nlohmann::json result;
            result["status"] = "cancelled";
            result["message"] = "Task cancelled";
            return result.dump(4);
        }

        // 获取安全日志
        std::vector<LogEntry> logs = manager.GetSecurityLogs(queryParams);

        if (progressCallback) {
            progressCallback("Analyzing security data...", 60);
        }

        // 获取统计信息
        LogStatistics stats = manager.GetLogStatistics(LogType::Security);

        if (progressCallback) {
            progressCallback("Generating results...", 80);
        }

        // 构建JSON结果
        nlohmann::json result;
        result["status"] = "success";
        result["message"] = "Security log analysis completed";
        result["log_type"] = "security";
        result["task_id"] = taskId;

        // 安全日志数据 - 使用统一的"logs"字段名
        nlohmann::json logArray = nlohmann::json::array();
        for (const auto& log : logs) {
            logArray.push_back(log.toJson());
        }
        result["logs"] = logArray;

        // 统计信息
        result["statistics"] = stats.toJson();

        // 可疑活动分析
        auto suspiciousLogs = SystemLogUtils::FindSuspiciousActivities(logs);
        nlohmann::json suspiciousArray = nlohmann::json::array();
        for (const auto& suspicious : suspiciousLogs) {
            suspiciousArray.push_back(suspicious.toJson());
        }
        result["suspicious_activities"] = suspiciousArray;

        if (progressCallback) {
            progressCallback("Security log analysis completed", 100);
        }

        // 保存结果到JSON文件
        try {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::tm tm_info;
            localtime_s(&tm_info, &time_t);

            std::ostringstream filename;
            filename << "security_logs_"
                     << std::put_time(&tm_info, "%Y%m%d_%H%M%S")
                     << ".json";

            std::ofstream outFile(filename.str());
            if (outFile.is_open()) {
                outFile << result.dump(4);
                outFile.close();
                result["output_file"] = filename.str();
            }
        } catch (...) {
            // 忽略文件保存错误
        }

        return result.dump(4);

    } catch (const std::exception& e) {
        nlohmann::json errorResult;
        errorResult["status"] = "error";
        errorResult["message"] = "Failed to get security logs";

        // 安全处理异常信息，避免编码问题
        try {
            std::string errorMsg = e.what();
            bool hasNonAscii = false;
            for (char c : errorMsg) {
                if ((unsigned char)c > 127) {
                    hasNonAscii = true;
                    break;
                }
            }
            errorResult["error_details"] = hasNonAscii ? "Encoding error, cannot display details" : errorMsg;
        } catch (...) {
            errorResult["error_details"] = "Unknown error";
        }

        errorResult["task_id"] = taskId;
        return errorResult.dump(4);
    }
}

// 统一接口 - 开关机日志
std::string SystemLogManager::Init_PowerLogInfoMsg(
    const std::string& params,
    std::function<void(const std::string&, int)> progressCallback,
    const std::string& taskId,
    std::function<bool(const std::string&)> queryTaskControl
) {
    SystemLogManager manager;

    try {
        if (progressCallback) {
            progressCallback("Starting power log analysis...", 0);
        }

        // 解析参数
        int maxEntries = 1000;
        int daysBack = 30;

        if (!params.empty()) {
            try {
                nlohmann::json paramJson = nlohmann::json::parse(params);
                if (paramJson.contains("max_entries")) {
                    maxEntries = paramJson["max_entries"];
                }
                if (paramJson.contains("days_back")) {
                    daysBack = paramJson["days_back"];
                }
            } catch (...) {
                // 使用默认参数
            }
        }

        if (progressCallback) {
            progressCallback("Reading system event logs...", 20);
        }

        // 检查任务取消
        if (queryTaskControl && queryTaskControl(taskId)) {
            nlohmann::json result;
            result["status"] = "cancelled";
            result["message"] = "Task cancelled";
            return result.dump(4);
        }

        // 获取开关机日志
        std::vector<PowerLogEntry> logs = manager.GetPowerLogs(maxEntries, daysBack);

        if (progressCallback) {
            progressCallback("Analyzing power data...", 60);
        }

        // 获取统计信息
        LogStatistics stats = manager.GetPowerLogStatistics();

        if (progressCallback) {
            progressCallback("Generating results...", 80);
        }

        // 构建JSON结果
        nlohmann::json result;
        result["status"] = "success";
        result["message"] = "Power log analysis completed";
        result["log_type"] = "power";
        result["task_id"] = taskId;

        // 开关机日志数据 - 使用统一的"logs"字段名
        nlohmann::json logArray = nlohmann::json::array();
        for (const auto& log : logs) {
            logArray.push_back(log.toJson());
        }
        result["logs"] = logArray;

        // 统计信息
        result["statistics"] = stats.toJson();

        // 开关机模式分析
        result["power_patterns"] = SystemLogUtils::AnalyzePowerPatterns(logs);

        if (progressCallback) {
            progressCallback("Power log analysis completed", 100);
        }

        // 保存结果到JSON文件
        try {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::tm tm_info;
            localtime_s(&tm_info, &time_t);

            std::ostringstream filename;
            filename << "power_logs_"
                     << std::put_time(&tm_info, "%Y%m%d_%H%M%S")
                     << ".json";

            std::ofstream outFile(filename.str());
            if (outFile.is_open()) {
                outFile << result.dump(4);
                outFile.close();
                result["output_file"] = filename.str();
            }
        } catch (...) {
            // 忽略文件保存错误
        }

        return result.dump(4);

    } catch (const std::exception& e) {
        nlohmann::json errorResult;
        errorResult["status"] = "error";
        errorResult["message"] = "Failed to get power logs";

        // 安全处理异常信息，避免编码问题
        try {
            std::string errorMsg = e.what();
            // 检查是否包含非ASCII字符
            bool hasNonAscii = false;
            for (char c : errorMsg) {
                if ((unsigned char)c > 127) {
                    hasNonAscii = true;
                    break;
                }
            }

            if (hasNonAscii) {
                errorResult["error_details"] = "Encoding error, cannot display details";
            } else {
                errorResult["error_details"] = errorMsg;
            }
        } catch (...) {
            errorResult["error_details"] = "Unknown error";
        }

        errorResult["task_id"] = taskId;
        return errorResult.dump(4);
    }
}

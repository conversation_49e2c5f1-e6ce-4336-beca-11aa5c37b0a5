#pragma once

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include <windows.h>
#include <objbase.h>
#include <shlobj.h>
#include <shobjidl.h>
#include <propkey.h>
#include <propsys.h>
#include <string>
#include <vector>
#include <functional>
#include "nlohmann/json.hpp"

// EXIF元数据信息结构体
struct ExifInfo
{
    std::string manufacturer;    // 照相机制造商
    std::string model;          // 照相机型号
    std::string dateTime;       // 拍摄日期时间
    std::string orientation;    // 图像方向
    std::string exposureTime;   // 曝光时间
    std::string fNumber;        // 光圈值
    std::string isoSpeed;       // ISO感光度
    std::string focalLength;    // 焦距
    std::string flash;          // 闪光灯
    std::string width;          // 图像宽度
    std::string height;         // 图像高度

    // GPS位置信息
    std::string gpsLatitude;    // GPS纬度
    std::string gpsLongitude;   // GPS经度
    std::string gpsAltitude;    // GPS海拔高度
    std::string gpsLatitudeRef; // 纬度参考(N/S)
    std::string gpsLongitudeRef;// 经度参考(E/W)
    std::string gpsAltitudeRef; // 海拔参考(海平面上/下)
    std::string gpsDateTime;    // GPS时间戳
    std::string gpsMapDatum;    // GPS地图基准
    
    // 清空所有信息
    void Clear()
    {
        manufacturer.clear();
        model.clear();
        dateTime.clear();
        orientation.clear();
        exposureTime.clear();
        fNumber.clear();
        isoSpeed.clear();
        focalLength.clear();
        flash.clear();
        width.clear();
        height.clear();

        // 清空GPS信息
        gpsLatitude.clear();
        gpsLongitude.clear();
        gpsAltitude.clear();
        gpsLatitudeRef.clear();
        gpsLongitudeRef.clear();
        gpsAltitudeRef.clear();
        gpsDateTime.clear();
        gpsMapDatum.clear();
    }
};

// EXIF元数据提取器类
class ExifExtractor
{
public:
    ExifExtractor();
    ~ExifExtractor();

    // 静态接口方法 - 符合项目统一接口规范
    static std::string Init_ExifExtractorMsg(
        const std::string& params,
        std::function<void(const std::string&, int)> progressCallback,
        const std::string& taskId,
        std::function<bool(const std::string&)> queryTaskControl
    );

    // 初始化COM组件
    bool Initialize();

    // 清理COM组件
    void Cleanup();

    // 从图片文件提取EXIF信息
    bool ExtractExifInfo(const std::string& filePath, ExifInfo& exifInfo);

    // 获取最后的错误信息
    std::string GetLastError() const { return m_lastError; }

private:
    bool m_initialized;
    std::string m_lastError;
    
    // 辅助函数：从PROPVARIANT获取字符串值
    std::string GetStringFromPropVariant(const PROPVARIANT& pv);
    
    // 辅助函数：从PROPVARIANT获取数值字符串
    std::string GetNumberStringFromPropVariant(const PROPVARIANT& pv);
    
    // 辅助函数：从PROPVARIANT获取分数字符串（如光圈值）
    std::string GetRationalStringFromPropVariant(const PROPVARIANT& pv);
    
    // 辅助函数：设置错误信息
    void SetError(const std::string& error);
    
    // 辅助函数：将宽字符串转换为多字节字符串
    std::string WideStringToString(const std::wstring& wstr);

    // GPS相关辅助函数
    std::string ConvertGpsCoordinate(const PROPVARIANT& pv);
    std::string ConvertGpsAltitude(const PROPVARIANT& pv);
    std::string FormatGpsCoordinate(double degrees, double minutes, double seconds);
    double ConvertDmsToDecimal(double degrees, double minutes, double seconds);
    std::string GetGpsDecimalCoordinate(const std::string& dmsCoord, const std::string& ref);
    bool HasGpsInfo(const ExifInfo& exifInfo);
};

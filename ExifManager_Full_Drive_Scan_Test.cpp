#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <chrono>
#include <set>

// 进度回调函数
void fullDriveScanProgressCallback(const std::string& message, int progress) {
    std::cout << "[Full Drive Scan " << progress << "%] " << message << std::endl;
}

// 任务控制回调函数
bool fullDriveScanQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 分析搜索结果的目录分布
void analyzeDirectoryDistribution(const std::vector<std::string>& paths) {
    std::cout << "\n=== Directory Distribution Analysis ===" << std::endl;
    
    std::map<std::string, int> directoryCount;
    std::set<std::string> uniqueDirectories;
    
    for (const auto& path : paths) {
        // 提取目录路径（去掉文件名）
        size_t lastSlash = path.find_last_of("\\");
        if (lastSlash != std::string::npos) {
            std::string directory = path.substr(0, lastSlash);
            directoryCount[directory]++;
            uniqueDirectories.insert(directory);
        }
    }
    
    std::cout << "Total unique directories with images: " << uniqueDirectories.size() << std::endl;
    
    // 显示文件数量最多的前10个目录
    std::vector<std::pair<std::string, int>> sortedDirs(directoryCount.begin(), directoryCount.end());
    std::sort(sortedDirs.begin(), sortedDirs.end(), 
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    std::cout << "\nTop 10 directories by file count:" << std::endl;
    for (size_t i = 0; i < sortedDirs.size() && i < 10; i++) {
        std::cout << "  " << (i + 1) << ". " << sortedDirs[i].first 
                  << " (" << sortedDirs[i].second << " files)" << std::endl;
    }
    
    // 检查是否找到了各种类型的目录
    std::vector<std::string> targetDirectories = {
        "Screenshots", "Desktop", "Pictures", "Downloads", "Documents", "Camera Roll"
    };
    
    std::cout << "\nTarget directory detection:" << std::endl;
    for (const auto& target : targetDirectories) {
        bool found = false;
        int fileCount = 0;
        
        for (const auto& dir : uniqueDirectories) {
            if (dir.find(target) != std::string::npos) {
                found = true;
                if (directoryCount.find(dir) != directoryCount.end()) {
                    fileCount += directoryCount[dir];
                }
            }
        }
        
        if (found) {
            std::cout << "  ✓ " << target << " directories found (" << fileCount << " files)" << std::endl;
        } else {
            std::cout << "  ⚠ " << target << " directories not found" << std::endl;
        }
    }
}

// 检查搜索深度和覆盖范围
void analyzeSearchCoverage(const std::vector<std::string>& paths) {
    std::cout << "\n=== Search Coverage Analysis ===" << std::endl;
    
    std::map<std::string, int> driveCount;
    std::map<int, int> depthCount;
    int maxDepth = 0;
    
    for (const auto& path : paths) {
        // 分析驱动器分布
        if (path.length() >= 3 && path[1] == ':') {
            std::string drive = path.substr(0, 3);
            driveCount[drive]++;
        }
        
        // 分析目录深度
        int depth = 0;
        for (char c : path) {
            if (c == '\\') depth++;
        }
        depthCount[depth]++;
        maxDepth = std::max(maxDepth, depth);
    }
    
    std::cout << "Drive coverage:" << std::endl;
    for (const auto& pair : driveCount) {
        std::cout << "  " << pair.first << " → " << pair.second << " files" << std::endl;
    }
    
    std::cout << "\nDepth distribution:" << std::endl;
    for (const auto& pair : depthCount) {
        std::cout << "  Depth " << pair.first << ": " << pair.second << " files" << std::endl;
    }
    
    std::cout << "Maximum depth reached: " << maxDepth << std::endl;
    
    if (maxDepth >= 8) {
        std::cout << "✓ Deep recursive search is working excellently!" << std::endl;
    } else if (maxDepth >= 5) {
        std::cout << "✓ Good recursive search depth achieved!" << std::endl;
    } else {
        std::cout << "ℹ Search depth is relatively shallow" << std::endl;
    }
}

// 直接测试ScanDirectoryW方法
void testDirectScanDirectoryW() {
    std::cout << "\n=== Direct ScanDirectoryW Test ===" << std::endl;
    
    ExifExtractor extractor;
    if (!extractor.Initialize()) {
        std::cout << "Failed to initialize EXIF extractor" << std::endl;
        return;
    }
    
    // 测试C盘根目录扫描
    std::wstring rootPath = L"C:\\";
    std::vector<std::wstring> wideResults;
    
    std::cout << "Testing direct scan of C:\\ root directory..." << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    extractor.ScanDirectoryW(rootPath, wideResults);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
    
    std::cout << "Direct C:\\ scan completed in " << duration.count() << " seconds" << std::endl;
    std::cout << "Total files found: " << wideResults.size() << std::endl;
    
    if (wideResults.size() > 0) {
        std::cout << "✓ Full drive scan is working!" << std::endl;
        
        // 显示前5个文件路径作为示例
        std::cout << "Sample files found:" << std::endl;
        for (size_t i = 0; i < wideResults.size() && i < 5; i++) {
            // 转换为UTF-8显示
            int pathLength = WideCharToMultiByte(CP_UTF8, 0, wideResults[i].c_str(), -1, NULL, 0, NULL, NULL);
            if (pathLength > 0) {
                std::vector<char> pathBuffer(pathLength);
                int result = WideCharToMultiByte(CP_UTF8, 0, wideResults[i].c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
                if (result > 0) {
                    std::string utf8Path(&pathBuffer[0]);
                    std::cout << "  " << (i + 1) << ". " << utf8Path << std::endl;
                }
            }
        }
        
        // 检查是否找到Screenshots目录
        int screenshotsCount = 0;
        for (const auto& path : wideResults) {
            if (path.find(L"Screenshots") != std::wstring::npos || 
                path.find(L"screenshots") != std::wstring::npos) {
                screenshotsCount++;
            }
        }
        
        if (screenshotsCount > 0) {
            std::cout << "✓ Found " << screenshotsCount << " files in Screenshots directories!" << std::endl;
        } else {
            std::cout << "⚠ No Screenshots files found" << std::endl;
        }
        
    } else {
        std::cout << "⚠ No files found - there might be an issue with the scan" << std::endl;
    }
    
    extractor.Cleanup();
}

int main() {
    std::cout << "=== EXIF Manager Full Drive Scan Test ===" << std::endl;
    std::cout << "This test will perform a complete C:\\ drive scan for images..." << std::endl;
    std::cout << "WARNING: This may take several minutes depending on your drive size!" << std::endl;
    std::cout << "\nPress Enter to continue or Ctrl+C to cancel..." << std::endl;
    std::cin.get();
    
    // 直接测试ScanDirectoryW方法
    testDirectScanDirectoryW();
    
    std::string taskId = "full_drive_scan_test_001";
    
    std::cout << "\n=== Testing Full EXIF Analysis with Complete Drive Scan ===" << std::endl;
    
    try {
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // 调用完整的EXIF分析
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            fullDriveScanProgressCallback,
            taskId,
            fullDriveScanQueryTaskControl
        );
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::minutes>(endTime - startTime);
        
        std::cout << "\n=== Full Drive Scan Test Results ===" << std::endl;
        std::cout << "Total analysis time: " << duration.count() << " minutes" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 解析结果分析全盘扫描效果
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            if (jsonResult.find("status") != jsonResult.end()) {
                std::cout << "Status: " << jsonResult["status"] << std::endl;
            }
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                int totalImages = jsonResult["total_images_found"];
                std::cout << "Total images found: " << totalImages << std::endl;
                
                if (totalImages > 100) {
                    std::cout << "✓ Excellent! Full drive scan found many images!" << std::endl;
                } else if (totalImages > 10) {
                    std::cout << "✓ Good! Drive scan found a reasonable number of images!" << std::endl;
                } else {
                    std::cout << "ℹ Limited images found - this might be expected" << std::endl;
                }
            }
            
            // 分析搜索结果
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                std::vector<std::string> allPaths;
                
                for (const auto& image : imageAnalysis) {
                    if (image.find("file_path") != image.end()) {
                        allPaths.push_back(image["file_path"]);
                    }
                }
                
                std::cout << "\nCollected " << allPaths.size() << " file paths for analysis" << std::endl;
                
                // 分析目录分布
                analyzeDirectoryDistribution(allPaths);
                
                // 分析搜索覆盖范围
                analyzeSearchCoverage(allPaths);
            }
            
            // 检查文件保存
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "\n✓ Results saved to: " << outputFile << std::endl;
                }
            }
            
            std::cout << "\n✓ Full drive scan test completed successfully!" << std::endl;
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <windows.h>

// 简单的进度回调函数
void chinesePathProgressCallback(const std::string& message, int progress) {
    std::cout << "[Chinese Path Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool chinesePathQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 测试中文路径处理
void testChinesePathHandling() {
    std::cout << "=== Testing Chinese Path Handling ===" << std::endl;
    
    // 测试一些包含中文的路径
    std::vector<std::string> testPaths = {
        "C:\\Users\\<USER>\\Pictures\\Screenshots\\屏幕截图 2025-03-26 204527.png",
        "C:\\Users\\<USER>\\Desktop\\测试图片.jpg",
        "C:\\Users\\<USER>\\Documents\\我的照片\\photo.png"
    };
    
    for (const auto& path : testPaths) {
        std::cout << "\nTesting path: " << path << std::endl;
        
        // 检查文件是否存在
        DWORD attributes = GetFileAttributesA(path.c_str());
        if (attributes != INVALID_FILE_ATTRIBUTES) {
            std::cout << "  File exists (using ANSI API)" << std::endl;
        } else {
            std::cout << "  File not found (using ANSI API)" << std::endl;
        }
        
        // 尝试宽字符转换
        int widePathLength = MultiByteToWideChar(CP_ACP, 0, path.c_str(), -1, NULL, 0);
        if (widePathLength > 0) {
            std::vector<wchar_t> widePath(widePathLength);
            MultiByteToWideChar(CP_ACP, 0, path.c_str(), -1, &widePath[0], widePathLength);
            
            DWORD wideAttributes = GetFileAttributesW(&widePath[0]);
            if (wideAttributes != INVALID_FILE_ATTRIBUTES) {
                std::cout << "  File exists (using Wide API)" << std::endl;
            } else {
                std::cout << "  File not found (using Wide API)" << std::endl;
            }
        } else {
            std::cout << "  Failed to convert to wide characters" << std::endl;
        }
    }
}

int main() {
    std::cout << "=== EXIF Manager Chinese Path Test ===" << std::endl;
    
    // 首先测试中文路径处理
    testChinesePathHandling();
    
    std::string taskId = "chinese_path_test_001";
    
    std::cout << "\n=== Starting EXIF Analysis with Chinese Path Support ===" << std::endl;
    std::cout << "This test will scan for images with Chinese filenames..." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            chinesePathProgressCallback,
            taskId,
            chinesePathQueryTaskControl
        );
        
        std::cout << "\n=== Chinese Path Test Results ===" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 检查结果是否包含错误信息
        if (result.find("\"status\":\"error\"") != std::string::npos) {
            std::cout << "Error detected in result:" << std::endl;
            std::cout << result << std::endl;
        } else {
            // 尝试解析JSON以查找中文路径相关的问题
            try {
                nlohmann::json jsonResult = nlohmann::json::parse(result);
                std::cout << "JSON parsing successful!" << std::endl;
                
                if (jsonResult.find("total_images_found") != jsonResult.end()) {
                    std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
                }
                
                if (jsonResult.find("statistics") != jsonResult.end()) {
                    auto stats = jsonResult["statistics"];
                    if (stats.find("successful_extractions") != stats.end()) {
                        std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                    }
                }
                
                // 查找失败的图片文件
                if (jsonResult.find("image_analysis") != jsonResult.end()) {
                    auto imageAnalysis = jsonResult["image_analysis"];
                    int failedCount = 0;
                    int chinesePathCount = 0;
                    
                    for (const auto& image : imageAnalysis) {
                        if (image.find("success") != image.end() && !image["success"].get<bool>()) {
                            failedCount++;
                            if (image.find("file_path") != image.end()) {
                                std::string filePath = image["file_path"];
                                // 检查是否包含中文字符（被替换为?）
                                if (filePath.find("??") != std::string::npos) {
                                    chinesePathCount++;
                                    std::cout << "Chinese path issue detected: " << filePath << std::endl;
                                    if (image.find("error") != image.end()) {
                                        std::cout << "  Error: " << image["error"] << std::endl;
                                    }
                                }
                            }
                        }
                    }
                    
                    std::cout << "Failed extractions: " << failedCount << std::endl;
                    std::cout << "Chinese path issues: " << chinesePathCount << std::endl;
                }
                
                std::cout << "\nChinese path test completed successfully!" << std::endl;
                
            } catch (const nlohmann::json::exception& e) {
                std::cout << "JSON parse error: " << e.what() << std::endl;
                std::cout << "First 500 chars: " << result.substr(0, 500) << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

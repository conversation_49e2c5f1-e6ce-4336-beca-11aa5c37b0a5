# ExifManager - 设备图片EXIF信息分析模块

## 功能概述

ExifManager是一个用于设备级图片EXIF信息分析的C++模块，能够：
1. **快速扫描设备上的所有图片文件**
2. **批量提取EXIF元数据信息**
3. **生成统计分析报告**

支持从JPEG、PNG、TIFF、BMP、GIF等格式的图片文件中提取详细的元数据信息。

## 主要功能

### 基本信息提取
- **照相机制造商** (manufacturer): 如 "vivo", "Canon", "Nikon" 等
- **照相机型号** (model): 如 "V2302A", "EOS 5D Mark IV" 等
- **拍摄日期** (date_time): ISO格式的日期时间
- **图像尺寸** (width/height): 图片的像素尺寸

### 拍摄参数
- **曝光时间** (exposure_time): 如 "1/125"
- **光圈值** (f_number): 如 "f/2.8"
- **ISO感光度** (iso_speed): 如 "100", "800"
- **焦距** (focal_length): 如 "50.0mm"

### GPS位置信息
- **纬度** (latitude): 十进制度数格式
- **经度** (longitude): 十进制度数格式
- **GPS信息标志** (has_gps_info): 是否包含GPS信息

## 接口规范

### 主要接口
```cpp
std::string ExifManager::Init_ExifInfoMsg(
    const std::string& params,           // 空字符串表示扫描整个设备
    void(*progressCallback)(const std::string&, int),  // 进度回调
    const std::string& taskId,           // 任务ID
    QueryTaskControlCallback queryTaskControlCb  // 任务控制回调
);
```

### 返回格式
```json
{
    "status": "success",
    "message": "EXIF analysis completed",
    "task_id": "device_scan_001",
    "total_images_found": 1247,
    "output_file": "EXIF_Analysis_20250125_143022.json",
    "file_saved": true,
    "statistics": {
        "total_images": 1247,
        "successful_extractions": 1156,
        "extraction_success_rate": 92.7,
        "top_manufacturers": {
            "vivo": 456,
            "Apple": 234,
            "Samsung": 189,
            "Canon": 67
        },
        "top_models": {
            "V2302A": 456,
            "iPhone 13": 234,
            "Galaxy S21": 189,
            "EOS 5D": 67
        }
    },
    "image_analysis": [
        {
            "file_path": "C:\\Users\\<USER>\\Pictures\\photo1.jpg",
            "success": true,
            "manufacturer": "vivo",
            "model": "V2302A",
            "date_time": "2024-12-29 14:32:00",
            "width": "1080",
            "height": "1440"
        },
        {
            "file_path": "C:\\Users\\<USER>\\Desktop\\image2.png",
            "success": true,
            "manufacturer": "Apple",
            "model": "iPhone 13",
            "date_time": "2024-12-28 10:15:30",
            "width": "1920",
            "height": "1080"
        }
    ]
}
```

### JSON文件保存
- **自动保存**：分析结果自动保存为JSON文件
- **时间戳命名**：文件名格式为`EXIF_Analysis_YYYYMMDD_HHMMSS.json`
- **保存状态**：返回结果中包含文件保存状态信息
- **错误处理**：即使保存失败也会返回分析结果

## 使用示例

### 设备级EXIF分析
```cpp
#include "include/ExifManager.h"

void progressCallback(const std::string& message, int progress) {
    std::cout << "[" << progress << "%] " << message << std::endl;
}

bool taskControl(const std::string& taskId) {
    return false; // 不取消任务
}

int main() {
    std::string taskId = "device_exif_scan_001";

    // 设备级EXIF分析 - 扫描所有驱动器的图片文件
    std::string result = ExifManager::Init_ExifInfoMsg(
        "",              // 空参数表示设备级扫描
        progressCallback,
        taskId,
        taskControl
    );

    std::cout << result << std::endl;
    return 0;
}
```

### 在Tool.cpp主程序中的调用
```cpp
// 选择菜单项14：Device-wide EXIF metadata analysis
case 14: {
    std::cout << "=== Starting Device-wide EXIF Metadata Analysis ===" << std::endl;
    std::cout << "This will scan all drives for image files and extract EXIF data" << std::endl;
    std::cout << "Please wait, this may take several minutes..." << std::endl;

    result = ExifManager::Init_ExifInfoMsg("", progressCallback, taskId, queryTaskControl);

    std::cout << "=== Device EXIF Analysis Results ===" << std::endl;
    std::cout << result << std::endl;
    break;
}
```

## 技术实现

### 依赖项
- **GDI+** (Windows XP兼容的图像处理)
- **Windows API** (文件系统扫描，无需std::filesystem)
- **gdiplus.lib库**
- **nlohmann/json库**

### 支持的图片格式
- JPEG (.jpg, .jpeg)
- TIFF (.tif, .tiff)
- PNG (.png)
- BMP (.bmp)
- GIF (.gif)
- 其他GDI+支持的格式

### 兼容性
- **Windows XP及以上版本**
  - 使用GDI+ API进行EXIF提取
  - 使用Windows API进行文件系统扫描
  - 支持中文文件名和路径
  - 无需std::filesystem (C++17特性)
- **Visual Studio 2015及以上版本**
- **C++14标准**

### 中文路径支持（已全面重写）
- **宽字符优先**：重写搜索函数，全程使用宽字符API处理文件路径
- **简洁高效**：参考标准实现，代码更简洁，性能更优
- **UTF-8编码链**：建立一致的UTF-8编码处理链，从文件扫描到EXIF提取
- **智能编码转换**：优先使用UTF-8，失败时自动回退到ANSI编码
- **中文字符保留**：改进的字符串清理函数保留有效的UTF-8中文字符
- **路径完整性**：确保中文文件路径在整个处理过程中保持完整
- **转义修复**：避免JSON双重转义，确保路径格式正确
- **兼容性保证**：保持原有接口不变，内部使用新的宽字符实现

## 错误处理

模块包含完整的错误处理机制：
- COM组件初始化失败
- 文件路径无效
- 属性存储访问失败
- 任务取消处理

## 进度报告

提供详细的进度回调：
- 0%: 开始EXIF数据提取
- 30%: 正在提取EXIF信息
- 80%: 正在生成结果
- 100%: EXIF信息提取完成

## 注意事项

1. 确保图片文件存在且可访问
2. 某些EXIF字段可能为空（取决于拍摄设备和设置）
3. GPS信息需要设备支持且用户已启用位置服务
4. 处理大文件时可能需要较长时间

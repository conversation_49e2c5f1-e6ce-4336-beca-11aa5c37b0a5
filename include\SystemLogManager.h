﻿#pragma once

#include "SystemLogData.h"
#include <functional>
#include <memory>
#include <thread>
#include <atomic>

class SystemLogManager {
public:
    SystemLogManager();
    ~SystemLogManager();

    // 统一接口方法 - 符合项目接口规范
    static std::string Init_SystemLogInfoMsg(
        const std::string& params,
        std::function<void(const std::string&, int)> progressCallback,
        const std::string& taskId,
        std::function<bool(const std::string&)> queryTaskControl
    );

    static std::string Init_LoginLogInfoMsg(
        const std::string& params,
        std::function<void(const std::string&, int)> progressCallback,
        const std::string& taskId,
        std::function<bool(const std::string&)> queryTaskControl
    );

    static std::string Init_SecurityLogInfoMsg(
        const std::string& params,
        std::function<void(const std::string&, int)> progressCallback,
        const std::string& taskId,
        std::function<bool(const std::string&)> queryTaskControl
    );

    static std::string Init_PowerLogInfoMsg(
        const std::string& params,
        std::function<void(const std::string&, int)> progressCallback,
        const std::string& taskId,
        std::function<bool(const std::string&)> queryTaskControl
    );

    // 核心日志获取方法
    std::vector<LogEntry> GetSystemLogs(const LogQueryParams& params);
    std::vector<LogEntry> GetSecurityLogs(const LogQueryParams& params);
    std::vector<LogEntry> GetApplicationLogs(const LogQueryParams& params);
    
    // 专门的日志类型获取方法
    std::vector<LoginLogEntry> GetLoginLogs(int maxEntries = 1000, int daysBack = 30);
    std::vector<PowerLogEntry> GetPowerLogs(int maxEntries = 1000, int daysBack = 30);
    
    // 日志统计方法
    LogStatistics GetLogStatistics(LogType logType);
    LogStatistics GetLoginLogStatistics();
    LogStatistics GetPowerLogStatistics();

    // 日志查询和过滤
    std::vector<LogEntry> QueryLogs(const LogQueryParams& params);
    std::vector<LogEntry> FilterLogsByEventId(const std::vector<LogEntry>& logs, const std::vector<std::string>& eventIds);
    std::vector<LogEntry> FilterLogsByTimeRange(const std::vector<LogEntry>& logs, const std::string& startTime, const std::string& endTime);
    std::vector<LogEntry> FilterLogsByUser(const std::vector<LogEntry>& logs, const std::string& userName);

    // 工具方法
    std::string GetLogLevelString(WORD eventType);
    std::string GetLogTypeString(LogType logType);
    std::string FormatEventTime(const SYSTEMTIME& systemTime);
    std::string GetComputerName();
    
    // 登录类型解析
    std::string GetLoginTypeString(int loginType);
    std::string GetLogonProcessDescription(const std::string& process);
    
    // 开关机事件解析
    std::string GetPowerActionString(const std::string& eventId);
    std::string GetShutdownReasonString(DWORD reasonCode);

private:
    // Windows事件日志相关
    HANDLE OpenEventLog(const std::string& logName);
    void CloseEventLog(HANDLE hEventLog);
    
    // 事件记录读取和解析
    std::vector<LogEntry> ReadEventLogEntries(HANDLE hEventLog, const LogQueryParams& params);
    LogEntry ParseEventLogRecord(EVENTLOGRECORD* pRecord, const std::string& logName = "System");
    
    // 登录日志特殊解析
    LoginLogEntry ParseLoginLogRecord(EVENTLOGRECORD* pRecord);
    PowerLogEntry ParsePowerLogRecord(EVENTLOGRECORD* pRecord);
    
    // 字符串处理工具
    std::string WideStringToString(const std::wstring& wstr);
    std::wstring StringToWideString(const std::string& str);
    std::string SafeStringConvert(const char* pStr, size_t maxLen);
    std::string GetStringFromRecord(EVENTLOGRECORD* pRecord, DWORD offset);
    std::vector<std::string> GetStringsFromRecord(EVENTLOGRECORD* pRecord);
    
    // 时间处理
    std::string SystemTimeToString(const SYSTEMTIME& st);
    SYSTEMTIME FileTimeToSystemTime(const FILETIME& ft);
    bool IsTimeInRange(const std::string& timeStr, const std::string& startTime, const std::string& endTime);
    
    // 错误处理
    std::string GetLastErrorString();
    void LogError(const std::string& operation, DWORD errorCode);
    
    // 进度报告
    void ReportProgress(std::function<void(const std::string&, int)> callback, 
                       const std::string& message, int progress);
    
    // 任务控制
    bool CheckTaskCancellation(std::function<bool(const std::string&)> queryControl, 
                              const std::string& taskId);

    // 成员变量
    std::atomic<bool> m_cancelled;
    std::string m_lastError;
    LogStatistics m_currentStats;
    
    // 常量定义
    static const int DEFAULT_BUFFER_SIZE = 64 * 1024;  // 64KB缓冲区
    static const int MAX_RECORD_SIZE = 32 * 1024;      // 32KB最大记录大小
    
    // 事件ID常量
    static const std::vector<std::string> LOGIN_EVENT_IDS;
    static const std::vector<std::string> LOGOUT_EVENT_IDS;
    static const std::vector<std::string> POWER_EVENT_IDS;
    static const std::vector<std::string> SYSTEM_EVENT_IDS;
    
    // 日志名称常量
    static const char* SYSTEM_LOG_NAME;
    static const char* SECURITY_LOG_NAME;
    static const char* APPLICATION_LOG_NAME;
};

// 工具函数命名空间
namespace SystemLogUtils {
    // 解析参数字符串为查询参数
    LogQueryParams ParseQueryParams(const std::string& params);
    
    // 生成日志报告
    std::string GenerateLogReport(const std::vector<LogEntry>& logs, const LogStatistics& stats);
    std::string GenerateLoginReport(const std::vector<LoginLogEntry>& logs, const LogStatistics& stats);
    std::string GeneratePowerReport(const std::vector<PowerLogEntry>& logs, const LogStatistics& stats);
    
    // 导出功能
    bool ExportLogsToJson(const std::vector<LogEntry>& logs, const std::string& filename);
    bool ExportLoginLogsToJson(const std::vector<LoginLogEntry>& logs, const std::string& filename);
    bool ExportPowerLogsToJson(const std::vector<PowerLogEntry>& logs, const std::string& filename);
    
    // 日志分析
    std::map<std::string, int> AnalyzeLogSources(const std::vector<LogEntry>& logs);
    std::map<std::string, int> AnalyzeLoginPatterns(const std::vector<LoginLogEntry>& logs);
    std::map<std::string, int> AnalyzePowerPatterns(const std::vector<PowerLogEntry>& logs);
    
    // 安全分析
    std::vector<LogEntry> FindSuspiciousActivities(const std::vector<LogEntry>& logs);
    std::vector<LoginLogEntry> FindFailedLogins(const std::vector<LoginLogEntry>& logs);
    std::vector<LogEntry> FindSystemErrors(const std::vector<LogEntry>& logs);
}

#include "include/ExifManager.h"
#include <iostream>
#include <string>

// 简单的进度回调函数
void utf8ProgressCallback(const std::string& message, int progress) {
    std::cout << "[UTF8 Test Progress " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool utf8QueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

int main() {
    std::cout << "=== EXIF Manager UTF-8 Encoding Test ===" << std::endl;
    
    std::string taskId = "utf8_test_001";
    
    std::cout << "Testing EXIF Manager with UTF-8 encoding fixes..." << std::endl;
    std::cout << "This test will scan for images and handle invalid UTF-8 characters safely." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            utf8ProgressCallback,
            taskId,
            utf8QueryTaskControl
        );
        
        std::cout << "\n=== UTF-8 Safe JSON Result ===" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 检查结果是否包含错误信息
        if (result.find("\"status\":\"error\"") != std::string::npos) {
            std::cout << "Error detected in result:" << std::endl;
            std::cout << result << std::endl;
        } else {
            std::cout << "Success! First 1000 characters of result:" << std::endl;
            std::cout << result.substr(0, 1000) << std::endl;
            
            // 尝试解析JSON以验证有效性
            try {
                nlohmann::json jsonResult = nlohmann::json::parse(result);
                std::cout << "\n=== JSON Validation Success ===" << std::endl;
                std::cout << "Status: " << jsonResult["status"] << std::endl;
                std::cout << "Message: " << jsonResult["message"] << std::endl;
                
                if (jsonResult.find("total_images_found") != jsonResult.end()) {
                    std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
                }
                
                if (jsonResult.find("statistics") != jsonResult.end()) {
                    auto stats = jsonResult["statistics"];
                    if (stats.find("total_images") != stats.end()) {
                        std::cout << "Statistics - Total images: " << stats["total_images"] << std::endl;
                    }
                    if (stats.find("successful_extractions") != stats.end()) {
                        std::cout << "Statistics - Successful extractions: " << stats["successful_extractions"] << std::endl;
                    }
                    if (stats.find("top_manufacturers") != stats.end()) {
                        std::cout << "Top manufacturers found: " << stats["top_manufacturers"].size() << std::endl;
                        for (auto it = stats["top_manufacturers"].begin(); it != stats["top_manufacturers"].end(); ++it) {
                            std::cout << "  " << it.key() << ": " << it.value() << " images" << std::endl;
                        }
                    }
                }
                
                std::cout << "\nUTF-8 encoding test PASSED!" << std::endl;
                
            } catch (const nlohmann::json::exception& e) {
                std::cout << "\n=== JSON Validation Failed ===" << std::endl;
                std::cout << "JSON parse error: " << e.what() << std::endl;
                std::cout << "Error ID: " << e.id << std::endl;
                std::cout << "This indicates UTF-8 encoding issues still exist." << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cout << "\n=== General Error ===" << std::endl;
        std::cout << "Error: " << e.what() << std::endl;
    }
    
    std::cout << "\nUTF-8 test completed. Press any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

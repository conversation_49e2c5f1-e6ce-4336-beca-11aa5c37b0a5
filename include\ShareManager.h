﻿#pragma once
#include "ShareData.h"
#include <vector>
#include <string>
#include <functional>
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <lm.h>
#include <nlohmann/json.hpp>

// 任务控制回调函数类型定义
typedef std::function<bool(const std::string&)> QueryTaskControlCallback;

class ShareManager {
public:
    ShareManager();
    ~ShareManager();

    // 初始化共享管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有网络共享
    std::vector<ShareData> GetAllShares();

    // 获取完整的共享信息并返回JSON格式
    nlohmann::json GetSharesInfoAsJson();

    // 保存共享信息到JSON文件
    bool SaveSharesInfoToFile(const std::string& filename);

    // 封装的共享信息获取接口
    static std::string Init_ShareInfoMsg(
        const std::string& params,
        void(*progressCallback)(const std::string&, int),
        const std::string& taskId,
        QueryTaskControlCallback queryTaskControlCb
    );

private:
    bool m_initialized;

    // 辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::wstring ConvertToWString(const std::string& str);

    // 获取共享类型字符串
    std::string GetShareTypeString(DWORD shareType);

    // 获取共享详细信息
    ShareData GetShareDetails(const std::wstring& shareName);

    // 检查是否为隐藏共享
    bool IsHiddenShare(const std::wstring& shareName);

    // 获取共享权限信息
    std::string GetSharePermissions(const std::wstring& shareName);
};

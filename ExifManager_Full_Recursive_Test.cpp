#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <chrono>
#include <set>

// 进度回调函数
void fullRecursiveProgressCallback(const std::string& message, int progress) {
    std::cout << "[Full Recursive Test " << progress << "%] " << message << std::endl;
}

// 任务控制回调函数
bool fullRecursiveQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 直接测试新的完全递归搜索方法
void testFullRecursiveSearch() {
    std::cout << "=== Testing Full Recursive Search Method ===" << std::endl;
    
    ExifExtractor extractor;
    if (!extractor.Initialize()) {
        std::cout << "Failed to initialize EXIF extractor" << std::endl;
        return;
    }
    
    // 测试不同的目录
    std::vector<std::wstring> testDirectories = {
        L"C:\\Users\\<USER>\\Pictures",
        L"C:\\Users\\<USER>\\Desktop",
        L"C:\\Users\\<USER>\\Downloads"
    };
    
    for (const auto& testDir : testDirectories) {
        std::cout << "\n--- Testing Directory ---" << std::endl;
        
        // 转换为UTF-8显示
        int pathLength = WideCharToMultiByte(CP_UTF8, 0, testDir.c_str(), -1, NULL, 0, NULL, NULL);
        if (pathLength > 0) {
            std::vector<char> pathBuffer(pathLength);
            int result = WideCharToMultiByte(CP_UTF8, 0, testDir.c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
            if (result > 0) {
                std::string utf8Path(&pathBuffer[0]);
                std::cout << "Directory: " << utf8Path << std::endl;
            }
        }
        
        std::vector<std::wstring> wideResults;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // 使用新的完全递归搜索方法
        extractor.ScanDirectoryW(testDir, wideResults);
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        std::cout << "Scan time: " << duration.count() << " ms" << std::endl;
        std::cout << "Files found: " << wideResults.size() << std::endl;
        
        // 分析子目录分布
        std::set<std::wstring> subDirectories;
        for (const auto& path : wideResults) {
            size_t pos = path.find(L"\\", testDir.length() + 1);
            if (pos != std::wstring::npos) {
                std::wstring subDir = path.substr(0, pos);
                subDirectories.insert(subDir);
            }
        }
        
        std::cout << "Subdirectories with images: " << subDirectories.size() << std::endl;
        
        // 显示前5个子目录
        int count = 0;
        for (const auto& subDir : subDirectories) {
            if (count >= 5) break;
            
            // 转换为UTF-8显示
            int subPathLength = WideCharToMultiByte(CP_UTF8, 0, subDir.c_str(), -1, NULL, 0, NULL, NULL);
            if (subPathLength > 0) {
                std::vector<char> subPathBuffer(subPathLength);
                int result = WideCharToMultiByte(CP_UTF8, 0, subDir.c_str(), -1, &subPathBuffer[0], subPathLength, NULL, NULL);
                if (result > 0) {
                    std::string utf8SubPath(&subPathBuffer[0]);
                    std::cout << "  " << (count + 1) << ". " << utf8SubPath << std::endl;
                }
            }
            count++;
        }
        
        // 检查是否找到Screenshots目录
        bool foundScreenshots = false;
        for (const auto& path : wideResults) {
            if (path.find(L"Screenshots") != std::wstring::npos || 
                path.find(L"screenshots") != std::wstring::npos) {
                foundScreenshots = true;
                break;
            }
        }
        
        if (foundScreenshots) {
            std::cout << "✓ Found files in Screenshots directory!" << std::endl;
        } else {
            std::cout << "⚠ No files found in Screenshots directory" << std::endl;
        }
    }
    
    extractor.Cleanup();
}

// 分析搜索结果的深度分布
void analyzeSearchDepth(const std::vector<std::string>& paths) {
    std::cout << "\n=== Search Depth Analysis ===" << std::endl;
    
    std::map<int, int> depthCount;
    int maxDepth = 0;
    
    for (const auto& path : paths) {
        int depth = 0;
        for (char c : path) {
            if (c == '\\') depth++;
        }
        depthCount[depth]++;
        maxDepth = std::max(maxDepth, depth);
    }
    
    std::cout << "Depth distribution:" << std::endl;
    for (const auto& pair : depthCount) {
        std::cout << "  Depth " << pair.first << ": " << pair.second << " files" << std::endl;
    }
    
    std::cout << "Maximum depth reached: " << maxDepth << std::endl;
    
    if (maxDepth >= 6) {
        std::cout << "✓ Deep recursive search is working!" << std::endl;
    } else {
        std::cout << "ℹ Search depth is relatively shallow" << std::endl;
    }
}

int main() {
    std::cout << "=== EXIF Manager Full Recursive Search Test ===" << std::endl;
    
    // 直接测试新的递归搜索方法
    testFullRecursiveSearch();
    
    std::string taskId = "full_recursive_test_001";
    
    std::cout << "\n=== Testing Full EXIF Analysis with Complete Recursion ===" << std::endl;
    std::cout << "This will test the unlimited depth recursive search..." << std::endl;
    
    try {
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // 调用完整的EXIF分析
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            fullRecursiveProgressCallback,
            taskId,
            fullRecursiveQueryTaskControl
        );
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
        
        std::cout << "\n=== Full Recursive Test Results ===" << std::endl;
        std::cout << "Total analysis time: " << duration.count() << " seconds" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 解析结果分析递归效果
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            if (jsonResult.find("status") != jsonResult.end()) {
                std::cout << "Status: " << jsonResult["status"] << std::endl;
            }
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            if (jsonResult.find("statistics") != jsonResult.end()) {
                auto stats = jsonResult["statistics"];
                if (stats.find("successful_extractions") != stats.end()) {
                    std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                }
            }
            
            // 分析搜索深度和范围
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                std::vector<std::string> allPaths;
                int screenshotsCount = 0;
                int deepPathsCount = 0;
                
                for (const auto& image : imageAnalysis) {
                    if (image.find("file_path") != image.end()) {
                        std::string filePath = image["file_path"];
                        allPaths.push_back(filePath);
                        
                        // 检查Screenshots文件
                        if (filePath.find("Screenshots") != std::string::npos || 
                            filePath.find("screenshots") != std::string::npos) {
                            screenshotsCount++;
                        }
                        
                        // 检查深层路径（超过5层）
                        int depth = 0;
                        for (char c : filePath) {
                            if (c == '\\') depth++;
                        }
                        if (depth > 5) {
                            deepPathsCount++;
                        }
                    }
                }
                
                std::cout << "\nRecursive Search Analysis:" << std::endl;
                std::cout << "  Screenshots files found: " << screenshotsCount << std::endl;
                std::cout << "  Deep paths (>5 levels): " << deepPathsCount << std::endl;
                
                if (screenshotsCount > 0) {
                    std::cout << "✓ Successfully found Screenshots directory files!" << std::endl;
                } else {
                    std::cout << "⚠ No Screenshots files found" << std::endl;
                }
                
                if (deepPathsCount > 0) {
                    std::cout << "✓ Deep recursive search is working!" << std::endl;
                } else {
                    std::cout << "ℹ No very deep paths found" << std::endl;
                }
                
                // 分析搜索深度分布
                analyzeSearchDepth(allPaths);
            }
            
            // 检查文件保存
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "\n✓ Results saved to: " << outputFile << std::endl;
                }
            }
            
            std::cout << "\n✓ Full recursive search test completed!" << std::endl;
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

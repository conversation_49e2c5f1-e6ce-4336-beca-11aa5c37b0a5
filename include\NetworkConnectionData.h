﻿#pragma once
#include <string>
#include <windows.h>
#include <nlohmann/json.hpp>

// 统一格式的网络连接数据结构
struct NetworkConnectionData {
    std::string protocol;           // 协议类型 (TCP/UDP)
    std::string local_address;      // 本地地址
    DWORD local_port;              // 本地端口
    std::string remote_address;     // 远程地址
    DWORD remote_port;             // 远程端口
    std::string state;             // 连接状态字符串
    DWORD owning_pid;              // 拥有进程ID
    std::string process_name;       // 进程名称
    std::string process_path;       // 进程完整路径
    std::string process_icon;       // 进程图标Base64编码

    // 构造函数
    NetworkConnectionData() {
        local_port = 0;
        remote_port = 0;
        owning_pid = 0;
    }
};

// 统一格式的网络连接统计信息
struct NetworkConnectionStats {
    int total_connections;          // 总连接数
    int tcp_connections;            // TCP连接数
    int udp_connections;            // UDP连接数
    int established_connections;    // 已建立连接数
    int listening_connections;      // 监听连接数
    int unique_processes;           // 唯一进程数

    // 构造函数
    NetworkConnectionStats() {
        total_connections = 0;
        tcp_connections = 0;
        udp_connections = 0;
        established_connections = 0;
        listening_connections = 0;
        unique_processes = 0;
    }
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(NetworkConnectionData,
    protocol, local_address, local_port, remote_address, remote_port,
    state, owning_pid, process_name, process_path, process_icon)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(NetworkConnectionStats,
    total_connections, tcp_connections, udp_connections,
    established_connections, listening_connections, unique_processes)

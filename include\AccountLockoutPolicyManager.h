﻿#pragma once
#include "AccountLockoutPolicyData.h"
#include <vector>
#include <string>
#include <functional>
#include <windows.h>
#include <lm.h>
#include <ntsecapi.h>
#include <nlohmann/json.hpp>

#pragma comment(lib, "netapi32.lib")
#pragma comment(lib, "advapi32.lib")

// 任务控制回调函数类型定义
typedef std::function<bool(const std::string&)> QueryTaskControlCallback;

class AccountLockoutPolicyManager {
public:
    AccountLockoutPolicyManager();
    ~AccountLockoutPolicyManager();

    // 初始化账户锁定策略管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取账户锁定策略信息
    AccountLockoutPolicyData GetAccountLockoutPolicy();

    // 获取账户锁定策略统计信息
    AccountLockoutStatistics GetAccountLockoutStatistics();

    // 获取完整的账户锁定策略信息并返回JSON格式
    nlohmann::json GetAccountLockoutPolicyInfoAsJson();

    // 保存账户锁定策略信息到JSON文件
    bool SaveAccountLockoutPolicyInfoToFile(const std::string& filename);

    // 封装的账户锁定策略信息获取接口
    static std::string Init_AccountLockoutPolicyInfoMsg(
        const std::string& params,
        void(*progressCallback)(const std::string&, int),
        const std::string& taskId,
        QueryTaskControlCallback queryTaskControlCb
    );

private:
    bool m_initialized;
    std::chrono::system_clock::time_point m_startTime;
    LSA_HANDLE m_lsaHandle;

    // 辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::string ConvertToString(LPCWSTR wstr);
    std::wstring ConvertToWString(const std::string& str);
    std::string ConvertLsaUnicodeStringToString(const LSA_UNICODE_STRING& lsaString);

    // LSA策略操作
    bool InitializeLSA();
    void CleanupLSA();
    bool GetLSAPolicy(POLICY_INFORMATION_CLASS infoClass, PVOID* buffer);

    // 审核策略获取 - 核心功能
    bool GetAuditPolicySettings(AccountLockoutPolicyData& policy);
    bool GetSystemEventsAudit(AccountLockoutPolicyData& policy);
    bool GetLogonEventsAudit(AccountLockoutPolicyData& policy);
    bool GetObjectAccessAudit(AccountLockoutPolicyData& policy);
    bool GetPrivilegeUseAudit(AccountLockoutPolicyData& policy);
    bool GetProcessTrackingAudit(AccountLockoutPolicyData& policy);
    bool GetPolicyChangeAudit(AccountLockoutPolicyData& policy);
    bool GetAccountManagementAudit(AccountLockoutPolicyData& policy);
    bool GetAccountLogonEventsAudit(AccountLockoutPolicyData& policy);
    bool GetDirectoryServiceAccessAudit(AccountLockoutPolicyData& policy);
    bool GetDetailedAccountManagementAudit(AccountLockoutPolicyData& policy);

    // 锁定策略获取
    bool GetBasicLockoutPolicy(AccountLockoutPolicyData& policy);
    bool GetLockoutPolicyFromRegistry(AccountLockoutPolicyData& policy);
    bool GetLockoutPolicyFromNetAPI(AccountLockoutPolicyData& policy);

    // 锁定账户统计
    std::vector<std::string> GetCurrentlyLockedAccounts();
    int CountLockedAccounts();

    // 域信息获取
    bool GetDomainInfo(AccountLockoutPolicyData& policy);
    std::string GetDomainName();
    std::string GetDomainController();
    bool IsComputerInDomain();

    // 安全分析
    std::vector<std::string> AnalyzeAuditPolicySecurity(const AccountLockoutPolicyData& policy);
    std::string EvaluateSecurityLevel(const AccountLockoutPolicyData& policy);
    double CalculateAuditCoverage(const AccountLockoutPolicyData& policy);

    // 时间转换
    std::string GetCurrentTimestamp();

    // 注册表操作
    std::string ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName);
    DWORD ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName);
    bool ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName);

    // 错误处理
    std::string GetLastErrorString();
    std::string GetNTStatusString(NTSTATUS status);
    void LogError(const std::string& error);

    // WMI相关
    bool InitializeWMI();
    void CleanupWMI();
    std::string ExecuteWMIQuery(const std::string& query);

    // 审核策略注册表路径常量
    static const std::string AUDIT_POLICY_REG_PATH;
    static const std::string LSA_POLICY_REG_PATH;
};

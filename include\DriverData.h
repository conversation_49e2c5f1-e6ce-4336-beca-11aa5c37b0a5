﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// 驱动程序类型枚举
enum class DriverType {
    KERNEL_DRIVER,          // 内核驱动
    FILE_SYSTEM_DRIVER,     // 文件系统驱动
    NETWORK_DRIVER,         // 网络驱动
    DISPLAY_DRIVER,         // 显示驱动
    AUDIO_DRIVER,           // 音频驱动
    USB_DRIVER,             // USB驱动
    STORAGE_DRIVER,         // 存储驱动
    SYSTEM_DRIVER,          // 系统驱动
    THIRD_PARTY_DRIVER,     // 第三方驱动
    UNKNOWN_DRIVER          // 未知类型
};

// 驱动程序状态枚举
enum class DriverStatus {
    RUNNING,                // 运行中
    STOPPED,                // 已停止
    PAUSED,                 // 已暂停
    START_PENDING,          // 启动中
    STOP_PENDING,           // 停止中
    UNKNOWN                 // 未知状态
};

// Windows驱动程序数据结构（重新设计，更加实用和高效）
struct DriverData {
    std::string name;                   // 驱动程序名称
    std::string display_name;           // 显示名称
    std::string status;                 // 驱动程序状态
    std::string startup_type;           // 启动类型
    std::string driver_category;        // 驱动程序分类
    std::string binary_path;            // 驱动程序文件路径
    std::string load_address;           // 加载地址

    DriverData() {}
};

// 驱动程序统计信息
struct DriverStatistics {
    int total_drivers;                  // 总驱动数量
    int running_drivers;                // 运行中的驱动
    int stopped_drivers;                // 已停止的驱动
    int signed_drivers;                 // 已签名的驱动
    int unsigned_drivers;               // 未签名的驱动
    int system_drivers;                 // 系统驱动
    int third_party_drivers;            // 第三方驱动
    std::string scan_time;              // 扫描时间
    std::string scan_duration;          // 扫描耗时
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(DriverData,
    name, display_name, status, startup_type,
    driver_category, binary_path, load_address)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(DriverStatistics,
    total_drivers, running_drivers, stopped_drivers,
    signed_drivers, unsigned_drivers, system_drivers,
    third_party_drivers, scan_time, scan_duration)

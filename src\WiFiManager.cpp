﻿
#include "../include/WiFiManager.h"
#include <iostream>
#include <sstream>
#include <locale>
#include <memory>
#include <ctime>
#include <chrono>
#include <iomanip>
#include <vector>
#include <map>
#include <set>
#include <locale>
#include <codecvt>

// 条件链接不同的库
#ifndef WINDOWS_XP_SUPPORT
    #pragma comment(lib, "wlanapi.lib")
#endif
#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "advapi32.lib")

// WZC API 函数指针类型定义（用于动态加载）
typedef DWORD (WINAPI *PFN_WZCEnumInterfaces)(LPWSTR pSrvAddr, void* pIntfs);
typedef DWORD (WINAPI *PFN_WZCQueryInterface)(LPWSTR pSrvAddr, DWORD dwInFlags, void* pIntf, LPDWORD pdwOutFlags);
typedef DWORD (WINAPI *PFN_WZCDeleteIntfObj)(void* pIntf);

// 全局函数指针
static PFN_WZCEnumInterfaces g_pfnWZCEnumInterfaces = nullptr;
static PFN_WZCQueryInterface g_pfnWZCQueryInterface = nullptr;
static PFN_WZCDeleteIntfObj g_pfnWZCDeleteIntfObj = nullptr;

WiFiManager::WiFiManager() : m_initialized(false), m_useWlanApi(false),
                             m_clientHandle(nullptr), m_negotiatedVersion(0),
                             m_hWzcModule(nullptr), m_pIntfsTable(nullptr) {
}

WiFiManager::~WiFiManager() {
    Cleanup();
}

bool WiFiManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 初始化COM
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
    if (FAILED(hr)) {
        std::cout << "Failed to initialize COM: " << hr << std::endl;
        return false;
    }

    // 根据当前操作系统选择合适的API
    if (InitializeForCurrentOS()) {
        m_initialized = true;
        return true;
    }

    CoUninitialize();
    return false;
}

void WiFiManager::Cleanup() {
    if (m_useWlanApi) {
#ifndef WINDOWS_XP_SUPPORT
        CleanupWlanApi();
#endif
    } else {
#ifdef WINDOWS_XP_SUPPORT
        CleanupWzcApi();
#endif
    }

    if (m_initialized) {
        CoUninitialize();
        m_initialized = false;
    }
}

// 静态方法：检测API可用性
bool WiFiManager::IsWlanApiAvailable() {
    HMODULE hWlanApi = LoadLibraryA("wlanapi.dll");
    if (hWlanApi) {
        FreeLibrary(hWlanApi);
        return true;
    }
    return false;
}

bool WiFiManager::IsWindowsXP() {
    OSVERSIONINFO osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFO));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);

    if (GetVersionEx(&osvi)) {
        // Windows XP: Major=5, Minor=1
        // Windows 2003: Major=5, Minor=2
        return (osvi.dwMajorVersion == 5);
    }
    return false;
}

// 根据当前操作系统初始化合适的API
bool WiFiManager::InitializeForCurrentOS() {
    // 首先尝试WLAN API（Vista+）
    if (IsWlanApiAvailable() && !IsWindowsXP()) {
        std::cout << "Using WLAN API (Vista+)" << std::endl;
        m_useWlanApi = true;
#ifndef WINDOWS_XP_SUPPORT
        return InitializeWlanApi();
#else
        return false;
#endif
    }

    // 回退到WZC API（XP）
    std::cout << "Using WZC API (Windows XP)" << std::endl;
    m_useWlanApi = false;
#ifdef WINDOWS_XP_SUPPORT
    return InitializeWzcApi();
#else
    // 如果编译时没有XP支持，尝试注册表方法
    std::cout << "WZC API not available, using registry fallback" << std::endl;
    return true; // 使用注册表方法作为最后的回退
#endif
}

#ifndef WINDOWS_XP_SUPPORT
// WLAN API 初始化（Vista+）
bool WiFiManager::InitializeWlanApi() {
    DWORD result = WlanOpenHandle(2, nullptr, &m_negotiatedVersion, &m_clientHandle);
    if (result != ERROR_SUCCESS) {
        std::cout << "Failed to open WLAN handle: " << result << std::endl;
        return false;
    }
    return true;
}

void WiFiManager::CleanupWlanApi() {
    if (m_clientHandle) {
        WlanCloseHandle(m_clientHandle, nullptr);
        m_clientHandle = nullptr;
    }
}
#endif

#ifdef WINDOWS_XP_SUPPORT
// WZC API 初始化（XP）
bool WiFiManager::InitializeWzcApi() {
    // 动态加载wzcsapi.dll
    m_hWzcModule = LoadLibraryA("wzcsapi.dll");
    if (!m_hWzcModule) {
        std::cout << "Failed to load wzcsapi.dll" << std::endl;
        return false;
    }

    // 获取函数指针
    g_pfnWZCEnumInterfaces = (PFN_WZCEnumInterfaces)GetProcAddress(m_hWzcModule, "WZCEnumInterfaces");
    g_pfnWZCQueryInterface = (PFN_WZCQueryInterface)GetProcAddress(m_hWzcModule, "WZCQueryInterface");
    g_pfnWZCDeleteIntfObj = (PFN_WZCDeleteIntfObj)GetProcAddress(m_hWzcModule, "WZCDeleteIntfObj");

    if (!g_pfnWZCEnumInterfaces || !g_pfnWZCQueryInterface || !g_pfnWZCDeleteIntfObj) {
        std::cout << "Failed to get WZC function pointers" << std::endl;
        FreeLibrary(m_hWzcModule);
        m_hWzcModule = nullptr;
        return false;
    }

    return true;
}

void WiFiManager::CleanupWzcApi() {
    if (m_pIntfsTable) {
        // 清理接口表（简化处理，避免类型转换问题）
        LocalFree(m_pIntfsTable);
        m_pIntfsTable = nullptr;
    }

    if (m_hWzcModule) {
        FreeLibrary(m_hWzcModule);
        m_hWzcModule = nullptr;
    }

    // 清理函数指针
    g_pfnWZCEnumInterfaces = nullptr;
    g_pfnWZCQueryInterface = nullptr;
    g_pfnWZCDeleteIntfObj = nullptr;
}
#endif

std::vector<WiFiData> WiFiManager::GetAllWiFiProfiles() {
    if (!m_initialized) {
        return std::vector<WiFiData>();
    }

    return GetProfilesForCurrentOS();
}

// 根据当前操作系统获取WiFi配置文件
std::vector<WiFiData> WiFiManager::GetProfilesForCurrentOS() {
    // 无论使用哪种API，都使用合并的数据以获得最完整的结果
    return GetCombinedWiFiProfiles();
}

#ifndef WINDOWS_XP_SUPPORT
// WLAN API 获取配置文件（Vista+）
std::vector<WiFiData> WiFiManager::GetWlanApiProfiles() {
    std::vector<WiFiData> allProfiles;

    // 获取WiFi接口
    PWLAN_INTERFACE_INFO_LIST pInterfaceList = nullptr;
    DWORD result = WlanEnumInterfaces(m_clientHandle, nullptr, &pInterfaceList);

    if (result != ERROR_SUCCESS) {
        std::cout << "Failed to enumerate WiFi interfaces: " << result << std::endl;
        return allProfiles;
    }

    // 遍历每个接口
    for (DWORD i = 0; i < pInterfaceList->dwNumberOfItems; i++) {
        WLAN_INTERFACE_INFO& interfaceInfo = pInterfaceList->InterfaceInfo[i];

        // 获取配置文件列表
        PWLAN_PROFILE_INFO_LIST pProfileList = nullptr;
        DWORD profileResult = WlanGetProfileList(m_clientHandle, &interfaceInfo.InterfaceGuid, nullptr, &pProfileList);

        if (profileResult == ERROR_SUCCESS && pProfileList) {
            for (DWORD j = 0; j < pProfileList->dwNumberOfItems; j++) {
                WLAN_PROFILE_INFO& profileInfo = pProfileList->ProfileInfo[j];

                WiFiData wifiData;
                std::string rawProfileName = ConvertToString(profileInfo.strProfileName);
                wifiData.ssid = SafeStringClean(rawProfileName);
                wifiData.description = SafeStringClean(rawProfileName); // WLAN API中使用profile name作为description

                // 尝试获取密码
                LPWSTR pProfileXml = nullptr;
                DWORD flags = WLAN_PROFILE_GET_PLAINTEXT_KEY;
                DWORD grantedAccess = 0;

                DWORD xmlResult = WlanGetProfile(m_clientHandle, &interfaceInfo.InterfaceGuid,
                                               profileInfo.strProfileName, nullptr, &pProfileXml,
                                               &flags, &grantedAccess);

                if (xmlResult == ERROR_SUCCESS && pProfileXml) {
                    std::string rawPassword = ParsePasswordFromXML(pProfileXml);
                    wifiData.password = SafeStringClean(rawPassword);
                    WlanFreeMemory(pProfileXml);
                }

                // 获取WiFi连接历史时间（从注册表）
                std::string rawLastConnected = GetWiFiLastConnectedTime(wifiData.ssid);
                wifiData.last_connected = SafeStringClean(rawLastConnected);

                allProfiles.push_back(wifiData);
            }
            WlanFreeMemory(pProfileList);
        }
    }

    if (pInterfaceList) {
        WlanFreeMemory(pInterfaceList);
    }

    return allProfiles;
}
#endif

#ifdef WINDOWS_XP_SUPPORT
// WZC API 获取配置文件（XP）
std::vector<WiFiData> WiFiManager::GetWzcApiProfiles() {
    std::vector<WiFiData> allProfiles;

    if (!g_pfnWZCEnumInterfaces) {
        std::cout << "WZC API not initialized" << std::endl;
        return allProfiles;
    }

    // 简化的WZC API实现 - 由于类型定义复杂，使用注册表方法作为回退
    std::cout << "WZC API implementation simplified - using registry fallback" << std::endl;

    // 直接使用注册表方法获取WiFi信息
    return GetCombinedWiFiProfiles();
}


#endif

nlohmann::json WiFiManager::GetWiFiInfoAsJson() {
    nlohmann::json result;

    // 获取WiFi配置文件（根据当前API）
    std::vector<WiFiData> profiles = GetAllWiFiProfiles();
    result["profiles"] = profiles;

    // 统计信息
    int savedConfigs = 0;
    int unsavedConfigs = 0;
    for (const auto& profile : profiles) {
        if (profile.has_saved_config) {
            savedConfigs++;
        } else {
            unsavedConfigs++;
        }
    }

    // 添加元数据
    std::string apiType = m_useWlanApi ? "WLAN API (Vista+)" : "WZC API (XP) + Registry";
    result["metadata"] = {
        {"total_profiles", profiles.size()},
        {"saved_configs", savedConfigs},
        {"unsaved_configs", unsavedConfigs},
        {"scan_time", std::time(nullptr)},
        {"version", "3.0"},
        {"api_type", apiType},
        {"windows_xp_compatible", true},
        {"description", "Multi-API WiFi scanner with XP support"}
    };

    return result;
}

// 封装的WiFi信息获取接口实现
std::string WiFiManager::Init_WifiInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback("Initializing WiFi Manager...", 10);
        }

        // 创建WiFi管理器实例
        WiFiManager wifiManager;
        if (!wifiManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", "Failed to initialize WiFi Manager. Administrator privileges may be required."},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback("Scanning WiFi profiles...", 50);
        }

        // 获取WiFi信息
        nlohmann::json wifiInfo = wifiManager.GetWiFiInfoAsJson();

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback("WiFi scan completed successfully", 100);
        }

        // 添加任务状态信息
        wifiInfo["status"] = "success";
        wifiInfo["task_id"] = taskId;
        wifiInfo["message"] = "WiFi information retrieved successfully";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            wifiInfo["request_params"] = params;
        }

        return wifiInfo.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string("Exception occurred: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback("Error occurred during WiFi scan", -1);
        }

        return errorResult.dump();
    }
}

// 辅助函数实现
std::wstring WiFiManager::ConvertToWString(const std::string& str) {
    if (str.empty()) return L"";

    int len = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
    if (len == 0) return L"";

    std::wstring result(len - 1, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &result[0], len);
    return result;
}

std::string WiFiManager::ConvertToString(const std::wstring& wstr) {
    if (wstr.empty()) return "";

    try {
        int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
        if (len == 0) return "";

        std::string result(len - 1, 0);
        int convertResult = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], len, nullptr, nullptr);

        if (convertResult == 0) {
            // 转换失败，返回安全的ASCII版本
            std::string safeResult;
            for (wchar_t wc : wstr) {
                if (wc >= 32 && wc <= 126) {
                    safeResult += static_cast<char>(wc);
                } else {
                    safeResult += '?';  // 替换无法转换的字符
                }
            }
            return safeResult;
        }

        return SafeStringClean(result);
    } catch (...) {
        // 异常情况下返回空字符串
        return "";
    }
}

// 删除GetInterfaceStateString函数

std::string WiFiManager::ParsePasswordFromXML(const std::wstring& xmlContent) {
    // 简单的XML解析，查找<keyMaterial>标签中的密码
    size_t startPos = xmlContent.find(L"<keyMaterial>");
    if (startPos == std::wstring::npos) {
        return "";
    }

    startPos += 13; // 跳过<keyMaterial>标签
    size_t endPos = xmlContent.find(L"</keyMaterial>", startPos);
    if (endPos == std::wstring::npos) {
        return "";
    }

    std::wstring password = xmlContent.substr(startPos, endPos - startPos);
    return ConvertToString(password);
}

// 注册表操作实现（基于你的代码）
std::string WiFiManager::GetRegistryStringValue(HKEY hKey, const char* valueName) {
    DWORD dataSize = 0;
    DWORD dataType = 0;

    // 先查询数据大小
    LONG result = RegQueryValueExA(hKey, valueName, nullptr, &dataType, nullptr, &dataSize);
    if (result != ERROR_SUCCESS || (dataType != REG_SZ && dataType != REG_EXPAND_SZ)) {
        return "";
    }

    // 分配缓冲区并读取数据
    std::vector<char> buffer(dataSize);
    result = RegQueryValueExA(hKey, valueName, nullptr, &dataType,
                             reinterpret_cast<LPBYTE>(buffer.data()), &dataSize);

    if (result == ERROR_SUCCESS) {
        return std::string(buffer.data());
    }
    return "";
}

DWORD WiFiManager::GetRegistryDwordValue(HKEY hKey, const char* valueName) {
    DWORD value = 0;
    DWORD dataSize = sizeof(DWORD);
    DWORD dataType = 0;

    LONG result = RegQueryValueExA(hKey, valueName, nullptr, &dataType,
                                  reinterpret_cast<LPBYTE>(&value), &dataSize);

    if (result == ERROR_SUCCESS && dataType == REG_DWORD) {
        return value;
    }
    return 0;
}

std::string WiFiManager::GetRegistryBinaryValue(HKEY hKey, const char* valueName) {
    DWORD dataSize = 0;
    DWORD dataType = 0;

    // 先查询数据大小
    LONG result = RegQueryValueExA(hKey, valueName, nullptr, &dataType, nullptr, &dataSize);
    if (result != ERROR_SUCCESS || dataType != REG_BINARY) {
        return "";
    }

    // 分配缓冲区并读取数据
    std::vector<BYTE> buffer(dataSize);
    result = RegQueryValueExA(hKey, valueName, nullptr, &dataType,
                             buffer.data(), &dataSize);

    if (result == ERROR_SUCCESS) {
        std::string hexString;
        for (DWORD i = 0; i < dataSize; i++) {
            char hex[4];
            sprintf_s(hex, sizeof(hex), "%02X", buffer[i]);
            hexString += hex;
            if (i < dataSize - 1) {
                hexString += " ";
            }
        }
        return hexString;
    }
    return "";
}

std::string WiFiManager::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    struct tm timeinfo;
    if (localtime_s(&timeinfo, &time_t) == 0) {
        std::ostringstream oss;
        oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }
    return "Unknown";
}

// 安全的字符串清理函数，确保UTF-8兼容
std::string WiFiManager::SafeStringClean(const std::string& input) {
    if (input.empty()) {
        return "";
    }

    std::string result;
    result.reserve(input.length());

    for (size_t i = 0; i < input.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(input[i]);

        // 只保留可打印的ASCII字符和有效的UTF-8字符
        if (c >= 32 && c <= 126) {
            // 标准ASCII可打印字符
            result += c;
        } else if (c >= 128) {
            // 可能的UTF-8多字节字符，进行简单验证
            if (i + 1 < input.length()) {
                unsigned char next = static_cast<unsigned char>(input[i + 1]);
                if ((c & 0xE0) == 0xC0 && (next & 0xC0) == 0x80) {
                    // 有效的2字节UTF-8序列
                    result += c;
                    result += next;
                    i++; // 跳过下一个字节
                } else if (i + 2 < input.length()) {
                    unsigned char next2 = static_cast<unsigned char>(input[i + 2]);
                    if ((c & 0xF0) == 0xE0 && (next & 0xC0) == 0x80 && (next2 & 0xC0) == 0x80) {
                        // 有效的3字节UTF-8序列
                        result += c;
                        result += next;
                        result += next2;
                        i += 2; // 跳过接下来的两个字节
                    }
                }
            }
            // 如果不是有效的UTF-8序列，就跳过这个字符
        }
        // 跳过其他控制字符
    }

    return result;
}

// 获取WLAN API中所有WiFi配置文件的密码
std::map<std::string, std::string> WiFiManager::GetWlanApiPasswords() {
    std::map<std::string, std::string> passwordMap;

    if (!m_clientHandle) {
        std::cout << "Debug: WLAN client handle not initialized" << std::endl;
        return passwordMap;
    }

    PWLAN_INTERFACE_INFO_LIST pInterfaceList = nullptr;
    DWORD result = WlanEnumInterfaces(m_clientHandle, nullptr, &pInterfaceList);

    if (result != ERROR_SUCCESS || !pInterfaceList) {
        std::cout << "Debug: Failed to enumerate WLAN interfaces" << std::endl;
        return passwordMap;
    }

    std::cout << "Debug: Found " << pInterfaceList->dwNumberOfItems << " WLAN interfaces" << std::endl;

    for (DWORD i = 0; i < pInterfaceList->dwNumberOfItems; i++) {
        WLAN_INTERFACE_INFO& interfaceInfo = pInterfaceList->InterfaceInfo[i];

        // 获取配置文件列表
        PWLAN_PROFILE_INFO_LIST pProfileList = nullptr;
        DWORD profileResult = WlanGetProfileList(m_clientHandle, &interfaceInfo.InterfaceGuid, nullptr, &pProfileList);

        if (profileResult == ERROR_SUCCESS && pProfileList) {
            std::cout << "Debug: Found " << pProfileList->dwNumberOfItems << " WLAN profiles" << std::endl;

            for (DWORD j = 0; j < pProfileList->dwNumberOfItems; j++) {
                WLAN_PROFILE_INFO& profileInfo = pProfileList->ProfileInfo[j];
                std::string profileName = ConvertToString(profileInfo.strProfileName);

                // 尝试获取密码
                LPWSTR pProfileXml = nullptr;
                DWORD flags = WLAN_PROFILE_GET_PLAINTEXT_KEY;
                DWORD grantedAccess = 0;

                DWORD xmlResult = WlanGetProfile(m_clientHandle, &interfaceInfo.InterfaceGuid,
                                               profileInfo.strProfileName, nullptr, &pProfileXml,
                                               &flags, &grantedAccess);

                if (xmlResult == ERROR_SUCCESS && pProfileXml) {
                    std::string rawPassword = ParsePasswordFromXML(pProfileXml);
                    std::string cleanPassword = SafeStringClean(rawPassword);
                    if (!cleanPassword.empty()) {
                        passwordMap[SafeStringClean(profileName)] = cleanPassword;
                        std::cout << "Debug: Found password for WLAN profile: " << SafeStringClean(profileName) << std::endl;
                    } else {
                        std::cout << "Debug: No password found for WLAN profile: " << SafeStringClean(profileName) << std::endl;
                    }
                    WlanFreeMemory(pProfileXml);
                } else {
                    std::cout << "Debug: Failed to get XML for WLAN profile: " << SafeStringClean(profileName) << std::endl;
                }
            }
            WlanFreeMemory(pProfileList);
        }
    }

    if (pInterfaceList) {
        WlanFreeMemory(pInterfaceList);
    }

    std::cout << "Debug: Total WLAN profiles with passwords: " << passwordMap.size() << std::endl;
    return passwordMap;
}

// 合并注册表和WLAN API数据
std::vector<WiFiData> WiFiManager::GetCombinedWiFiProfiles() {
    std::vector<WiFiData> combinedProfiles;
    std::set<std::string> processedProfiles;

    std::cout << "Debug: ========== Starting Combined WiFi Profile Scan ==========" << std::endl;

    // 1. 获取WLAN API中的密码映射
    std::map<std::string, std::string> wlanPasswords = GetWlanApiPasswords();

    // 2. 从注册表获取所有网络历史记录
    const char* keyPath = "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\NetworkList\\Profiles";
    HKEY hKey;
    LONG result = ERROR_FILE_NOT_FOUND;

    #ifdef _WIN64
        result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, keyPath, 0, KEY_READ, &hKey);
    #else
        result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, keyPath, 0, KEY_READ | KEY_WOW64_64KEY, &hKey);
        if (result != ERROR_SUCCESS) {
            result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, keyPath, 0, KEY_READ, &hKey);
        }
    #endif

    if (result != ERROR_SUCCESS) {
        std::cout << "Debug: Failed to open registry, using WLAN API data only" << std::endl;
        // 如果注册表访问失败，只返回WLAN API数据
        for (const auto& pair : wlanPasswords) {
            WiFiData wifiData;
            wifiData.ssid = pair.first;
            wifiData.password = pair.second;
            wifiData.description = pair.first; // 使用profile name作为description
            wifiData.has_saved_config = true;
            wifiData.last_connected = GetWiFiLastConnectedTime(pair.first);
            combinedProfiles.push_back(wifiData);
        }
        return combinedProfiles;
    }

    std::cout << "Debug: Registry opened successfully, scanning network profiles..." << std::endl;

    // 3. 枚举注册表中的所有网络配置文件
    char subKeyName[256];
    DWORD nameSize;
    DWORD index = 0;
    int totalRegistryProfiles = 0;
    int wifiProfiles = 0;

    while (true) {
        nameSize = sizeof(subKeyName);
        LONG enumResult = RegEnumKeyExA(hKey, index, subKeyName, &nameSize, nullptr, nullptr, nullptr, nullptr);

        if (enumResult == ERROR_NO_MORE_ITEMS) {
            break;
        } else if (enumResult == ERROR_SUCCESS) {
            totalRegistryProfiles++;

            std::string fullPath = "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\NetworkList\\Profiles\\";
            fullPath += subKeyName;

            HKEY hSubKey;
            LONG subResult = ERROR_FILE_NOT_FOUND;

            #ifdef _WIN64
                subResult = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey);
            #else
                subResult = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ | KEY_WOW64_64KEY, &hSubKey);
                if (subResult != ERROR_SUCCESS) {
                    subResult = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey);
                }
            #endif

            if (subResult == ERROR_SUCCESS) {
                // 读取配置文件信息
                std::string rawProfileName = GetRegistryStringValue(hSubKey, "ProfileName");
                std::string rawDescription = GetRegistryStringValue(hSubKey, "Description");
                std::string profileName = SafeStringClean(rawProfileName);
                std::string description = SafeStringClean(rawDescription);
                DWORD nameType = GetRegistryDwordValue(hSubKey, "NameType");

                std::cout << "Debug: Registry profile: '" << profileName << "' Description: '" << description << "' NameType: " << nameType;
                // 添加类型说明
                switch (nameType) {
                    case 6: std::cout << " (Ethernet)"; break;
                    case 23: std::cout << " (WiFi)"; break;
                    case 71: std::cout << " (Mobile Broadband)"; break;
                    default: std::cout << " (Other)"; break;
                }
                std::cout << std::endl;

                // 处理无线网络 (NameType = 23)、移动宽带 (NameType = 71) 和其他可能的WiFi类型
                // 扩展支持更多网络类型，因为某些WiFi可能被识别为其他类型
                if ((nameType == 23 || nameType == 71 || nameType == 6) && !profileName.empty()) {
                    std::cout << "Debug: Found network profile: " << profileName << " (NameType: " << nameType << ")" << std::endl;
                    wifiProfiles++;

                    // 检查是否已经处理过这个配置文件
                    if (processedProfiles.find(profileName) == processedProfiles.end()) {
                        WiFiData wifiData;
                        wifiData.ssid = SafeStringClean(profileName);
                        wifiData.description = SafeStringClean(description.empty() ? profileName : description);

                        // 获取最后连接时间
                        std::string rawLastConnected = GetWiFiLastConnectedTime(profileName);
                        wifiData.last_connected = SafeStringClean(rawLastConnected);

                        // 检查是否有WLAN API配置文件和密码
                        auto passwordIt = wlanPasswords.find(profileName);
                        if (passwordIt != wlanPasswords.end()) {
                            wifiData.password = SafeStringClean(passwordIt->second);
                            wifiData.has_saved_config = true;
                            std::cout << "Debug: Found saved config for: " << profileName << std::endl;
                        } else {
                            wifiData.password = u8"密码未保存";  // 使用UTF-8前缀
                            wifiData.has_saved_config = false;
                            std::cout << "Debug: No saved config for: " << profileName << std::endl;
                        }

                        combinedProfiles.push_back(wifiData);
                        processedProfiles.insert(profileName);
                    }
                }

                RegCloseKey(hSubKey);
            }

            index++;
        } else {
            std::cout << "Debug: Registry enumeration error: " << enumResult << std::endl;
            break;
        }
    }

    RegCloseKey(hKey);

    std::cout << "Debug: ========== Scan Summary ==========" << std::endl;
    std::cout << "Debug: Total registry profiles: " << totalRegistryProfiles << std::endl;
    std::cout << "Debug: WiFi profiles found: " << wifiProfiles << std::endl;
    std::cout << "Debug: WLAN API profiles with passwords: " << wlanPasswords.size() << std::endl;
    std::cout << "Debug: Combined unique profiles: " << combinedProfiles.size() << std::endl;

    return combinedProfiles;
}

// WiFi连接历史时间获取实现（基于你的SYSTEMTIME解析代码）
std::string WiFiManager::GetWiFiLastConnectedTime(const std::string& profileName) {
    try {
        std::cout << "Debug: Looking for WiFi profile: " << profileName << std::endl;

        const char* keyPath = "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\NetworkList\\Profiles";
        HKEY hKey;

        std::cout << "Debug: Registry Path: " << keyPath << std::endl;

        // 检查程序架构
        #ifdef _WIN64
            std::cout << "Debug: Program Architecture: 64-bit" << std::endl;
        #else
            std::cout << "Debug: Program Architecture: 32-bit" << std::endl;
        #endif

        // 详细的注册表访问调试信息
        std::cout << "Debug: ========== Registry Access Attempt ==========" << std::endl;

        LONG result = ERROR_FILE_NOT_FOUND;

        #ifdef _WIN64
            // 64位程序直接访问
            std::cout << "Debug: 64-bit program: Direct access..." << std::endl;
            result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, keyPath, 0, KEY_READ, &hKey);
        #else
            // 32位程序：在Windows 7上需要强制访问64位注册表视图
            std::cout << "Debug: 32-bit program: Trying 64-bit registry view..." << std::endl;
            result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, keyPath, 0, KEY_READ | KEY_WOW64_64KEY, &hKey);

            if (result != ERROR_SUCCESS) {
                std::cout << "Debug: 64-bit view failed, trying default access..." << std::endl;
                result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, keyPath, 0, KEY_READ, &hKey);
            }
        #endif

        std::cout << "Debug: Registry access result: " << result;

        if (result == ERROR_SUCCESS) {
            std::cout << " (SUCCESS - Registry opened successfully!)" << std::endl;
            std::cout << "Debug: ✓ Registry handle obtained: " << hKey << std::endl;
            std::cout << "Debug: ✓ Ready to enumerate network profiles" << std::endl;
        } else {
            std::cout << " (FAILED)" << std::endl;
            std::cout << "Debug: ✗ Registry access failed with error code: " << result << std::endl;

            // 显示具体错误信息
            switch (result) {
                case ERROR_ACCESS_DENIED:
                    std::cout << "Debug: Error - Access denied (need admin rights)" << std::endl;
                    break;
                case ERROR_FILE_NOT_FOUND:
                    std::cout << "Debug: Error - Registry key not found" << std::endl;
                    std::cout << "Debug: This may indicate NetworkList feature not available" << std::endl;
                    break;
                default:
                    std::cout << "Debug: Error - Unknown error code " << result << std::endl;
                    break;
            }

            std::cout << "Debug: Using current time as fallback" << std::endl;
            return GetCurrentTimestamp();
        }

        std::cout << "Debug: ========== Registry Successfully Opened ==========" << std::endl;

        // 枚举子键查找匹配的WiFi配置文件
        char subKeyName[256];
        DWORD nameSize;
        DWORD index = 0;
        int profileCount = 0;

        // 用于存储找到的匹配配置文件和最新时间
        std::string latestTime = "";
        std::string latestProfileName = "";

        std::cout << "Debug: Starting to enumerate registry profiles..." << std::endl;
        std::cout << "Debug: Looking for profile matching: '" << profileName << "'" << std::endl;

        while (true) {
            nameSize = sizeof(subKeyName);
            LONG enumResult = RegEnumKeyExA(hKey, index, subKeyName, &nameSize, nullptr, nullptr, nullptr, nullptr);

            if (enumResult == ERROR_NO_MORE_ITEMS) {
                std::cout << "Debug: Finished enumerating, found " << profileCount << " profiles" << std::endl;
                break;
            } else if (enumResult == ERROR_SUCCESS) {
                profileCount++;
                std::cout << "Debug: Found profile GUID: " << subKeyName << std::endl;

                // 检查这个配置文件是否匹配
                std::string fullPath = "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\NetworkList\\Profiles\\";
                fullPath += subKeyName;

                HKEY hSubKey;
                LONG subResult = ERROR_FILE_NOT_FOUND;

                // 使用与主键相同的访问策略
                std::cout << "Debug: Attempting to open subkey: " << fullPath << std::endl;
                #ifdef _WIN64
                    std::cout << "Debug: Using 64-bit access for subkey..." << std::endl;
                    subResult = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey);
                    std::cout << "Debug: 64-bit subkey access result: " << subResult << std::endl;
                #else
                    // 32位程序：优先尝试64位注册表视图
                    std::cout << "Debug: Using 32-bit WOW64 access for subkey..." << std::endl;
                    subResult = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ | KEY_WOW64_64KEY, &hSubKey);
                    std::cout << "Debug: 32-bit WOW64 subkey access result: " << subResult << std::endl;
                    if (subResult != ERROR_SUCCESS) {
                        std::cout << "Debug: WOW64 failed, trying standard 32-bit access..." << std::endl;
                        subResult = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey);
                        std::cout << "Debug: 32-bit standard subkey access result: " << subResult << std::endl;
                    }
                #endif

                if (subResult == ERROR_SUCCESS) {
                    // 读取配置文件名称
                    std::string profileNameInRegistry = GetRegistryStringValue(hSubKey, "ProfileName");
                    std::cout << "Debug: Registry ProfileName: '" << profileNameInRegistry << "'" << std::endl;
                    std::cout << "Debug: Comparing with target: '" << profileName << "'" << std::endl;

                    // 检查匹配条件
                    bool match1 = !profileNameInRegistry.empty() && profileNameInRegistry.find(profileName) != std::string::npos;
                    bool match2 = !profileNameInRegistry.empty() && profileName.find(profileNameInRegistry) != std::string::npos;
                    std::cout << "Debug: Match condition 1 (registry contains target): " << (match1 ? "YES" : "NO") << std::endl;
                    std::cout << "Debug: Match condition 2 (target contains registry): " << (match2 ? "YES" : "NO") << std::endl;

                    // 如果找到匹配的配置文件
                    if (match1 || match2) {

                        std::cout << "Debug: Found matching profile: '" << profileNameInRegistry << "'" << std::endl;

                        // 读取DateLastConnected的二进制数据
                        std::string dateLastConnected = GetRegistryBinaryValue(hSubKey, "DateLastConnected");
                        std::cout << "Debug: DateLastConnected raw data: '" << dateLastConnected << "'" << std::endl;

                        if (!dateLastConnected.empty()) {
                            std::string parsedTime = ParseSystemTimeFromBinary(dateLastConnected);
                            std::cout << "Debug: Parsed time: " << parsedTime << std::endl;

                            // 比较时间，保留最新的
                            if (latestTime.empty() || parsedTime > latestTime) {
                                latestTime = parsedTime;
                                latestProfileName = profileNameInRegistry;
                                std::cout << "Debug: This is the latest time so far: " << parsedTime << std::endl;
                            } else {
                                std::cout << "Debug: This time is older than current latest: " << latestTime << std::endl;
                            }
                        } else {
                            std::cout << "Debug: No DateLastConnected data found for this profile" << std::endl;
                        }
                    }

                    RegCloseKey(hSubKey);
                } else {
                    std::cout << "Debug: Failed to open profile subkey, error: " << subResult << std::endl;
                }

                std::cout << "Debug: Profile " << index << " processed, continuing..." << std::endl;

                index++;
            } else {
                std::cout << "Debug: Enum error: " << enumResult << std::endl;
                break;
            }
        }

        RegCloseKey(hKey);
        std::cout << "Debug: ========== Search Summary ==========" << std::endl;
        std::cout << "Debug: Total profiles found: " << profileCount << std::endl;
        std::cout << "Debug: Target profile: '" << profileName << "'" << std::endl;

        if (!latestTime.empty()) {
            std::cout << "Debug: Found matching profiles, latest connection:" << std::endl;
            std::cout << "Debug: Latest profile name: '" << latestProfileName << "'" << std::endl;
            std::cout << "Debug: Latest connection time: " << latestTime << std::endl;
            return latestTime;
        } else {
            std::cout << "Debug: No matching profile found in registry" << std::endl;
            std::cout << "Debug: Suggestion: Check if profile name exactly matches" << std::endl;
            return "Profile Not Found";
        }

    } catch (const std::exception& e) {
        std::cout << "Debug: Exception in GetWiFiLastConnectedTime: " << e.what() << std::endl;
        return "Error";
    }
}

// 解析SYSTEMTIME格式的二进制数据（基于你的代码）
std::string WiFiManager::ParseSystemTimeFromBinary(const std::string& hexString) {
    if (hexString.empty()) {
        return "Unknown";
    }

    try {
        // 移除空格并转换为字节数组
        std::string hexOnly;
        for (char c : hexString) {
            if (c != ' ') hexOnly += c;
        }

        // 分析数据格式：E9 07 03 00 01 00 18 00 00 00 2A 00 25 00 D3 01
        // 这是SYSTEMTIME结构而不是FILETIME
        if (hexOnly.length() >= 32) { // 16字节 = 32个十六进制字符
            // 解析为SYSTEMTIME结构（小端序）
            // SYSTEMTIME结构：年(2字节) 月(2字节) 星期(2字节) 日(2字节) 时(2字节) 分(2字节) 秒(2字节) 毫秒(2字节)

            WORD year = 0, month = 0, dayOfWeek = 0, day = 0;
            WORD hour = 0, minute = 0, second = 0, milliseconds = 0;

            // 解析各个字段（小端序）
            if (hexOnly.length() >= 4) {
                year = (WORD)strtoul(hexOnly.substr(2, 2).c_str(), nullptr, 16) << 8 |
                       (WORD)strtoul(hexOnly.substr(0, 2).c_str(), nullptr, 16);
            }
            if (hexOnly.length() >= 8) {
                month = (WORD)strtoul(hexOnly.substr(6, 2).c_str(), nullptr, 16) << 8 |
                        (WORD)strtoul(hexOnly.substr(4, 2).c_str(), nullptr, 16);
            }
            if (hexOnly.length() >= 12) {
                dayOfWeek = (WORD)strtoul(hexOnly.substr(10, 2).c_str(), nullptr, 16) << 8 |
                            (WORD)strtoul(hexOnly.substr(8, 2).c_str(), nullptr, 16);
            }
            if (hexOnly.length() >= 16) {
                day = (WORD)strtoul(hexOnly.substr(14, 2).c_str(), nullptr, 16) << 8 |
                      (WORD)strtoul(hexOnly.substr(12, 2).c_str(), nullptr, 16);
            }
            if (hexOnly.length() >= 20) {
                hour = (WORD)strtoul(hexOnly.substr(18, 2).c_str(), nullptr, 16) << 8 |
                       (WORD)strtoul(hexOnly.substr(16, 2).c_str(), nullptr, 16);
            }
            if (hexOnly.length() >= 24) {
                minute = (WORD)strtoul(hexOnly.substr(22, 2).c_str(), nullptr, 16) << 8 |
                         (WORD)strtoul(hexOnly.substr(20, 2).c_str(), nullptr, 16);
            }
            if (hexOnly.length() >= 28) {
                second = (WORD)strtoul(hexOnly.substr(26, 2).c_str(), nullptr, 16) << 8 |
                         (WORD)strtoul(hexOnly.substr(24, 2).c_str(), nullptr, 16);
            }
            if (hexOnly.length() >= 32) {
                milliseconds = (WORD)strtoul(hexOnly.substr(30, 2).c_str(), nullptr, 16) << 8 |
                               (WORD)strtoul(hexOnly.substr(28, 2).c_str(), nullptr, 16);
            }

            // 验证解析的值是否合理
            if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 &&
                day >= 1 && day <= 31 && hour <= 23 && minute <= 59 && second <= 59) {

                std::ostringstream oss;
                oss << std::setfill('0') << std::setw(4) << year << "-"
                    << std::setfill('0') << std::setw(2) << month << "-"
                    << std::setfill('0') << std::setw(2) << day << " "
                    << std::setfill('0') << std::setw(2) << hour << ":"
                    << std::setfill('0') << std::setw(2) << minute << ":"
                    << std::setfill('0') << std::setw(2) << second;
                return oss.str();
            }
        }

        return "Invalid Date";

    } catch (...) {
        return "Parse Error";
    }
}

// 打印配置文件详细信息（基于你的代码，用于调试）
void WiFiManager::PrintProfileDetails(const std::string& profileGuid) {
    std::string fullPath = "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\NetworkList\\Profiles\\";
    fullPath += profileGuid;

    HKEY hSubKey;
    LONG result = ERROR_FILE_NOT_FOUND;

    // 使用与主程序相同的访问策略
    #ifdef _WIN64
        result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey);
    #else
        // 32位程序：优先尝试64位注册表视图
        result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ | KEY_WOW64_64KEY, &hSubKey);
        if (result != ERROR_SUCCESS) {
            result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey);
        }
    #endif

    if (result != ERROR_SUCCESS) {
        std::cout << "    Cannot open profile subkey, error: " << result << std::endl;
        return;
    }

    // 读取基本信息
    std::string profileName = GetRegistryStringValue(hSubKey, "ProfileName");
    std::string description = GetRegistryStringValue(hSubKey, "Description");
    DWORD category = GetRegistryDwordValue(hSubKey, "Category");
    DWORD categoryType = GetRegistryDwordValue(hSubKey, "CategoryType");
    DWORD managed = GetRegistryDwordValue(hSubKey, "Managed");
    DWORD nameType = GetRegistryDwordValue(hSubKey, "NameType");

    // 重点：读取DateLastConnected
    std::string dateLastConnected = GetRegistryBinaryValue(hSubKey, "DateLastConnected");

    // 打印信息
    if (!profileName.empty()) {
        std::cout << "    Profile Name: " << profileName << std::endl;
    }
    if (!description.empty()) {
        std::cout << "    Description: " << description << std::endl;
    }

    // 网络类别
    std::cout << "    Network Category: ";
    switch (category) {
        case 0: std::cout << "Public"; break;
        case 1: std::cout << "Private"; break;
        case 2: std::cout << "Domain"; break;
        default: std::cout << "Unknown (" << category << ")"; break;
    }
    std::cout << std::endl;

    // 名称类型
    std::cout << "    Name Type: ";
    switch (nameType) {
        case 6: std::cout << "Wired"; break;
        case 23: std::cout << "Wireless"; break;
        case 71: std::cout << "Mobile Broadband"; break;
        default: std::cout << "Other (" << nameType << ")"; break;
    }
    std::cout << std::endl;

    // 最后连接日期（重点信息）
    if (!dateLastConnected.empty()) {
        std::cout << "    Date Last Connected (Raw): " << dateLastConnected << std::endl;
        std::string parsedTime = ParseSystemTimeFromBinary(dateLastConnected);
        std::cout << "    Date Last Connected: " << parsedTime << std::endl;
    } else {
        std::cout << "    Date Last Connected: Not available" << std::endl;
    }

    RegCloseKey(hSubKey);
}

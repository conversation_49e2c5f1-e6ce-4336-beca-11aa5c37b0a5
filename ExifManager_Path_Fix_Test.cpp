#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <windows.h>

// 测试文件路径处理
void testFilePathHandling() {
    std::cout << "=== Testing File Path Handling ===" << std::endl;
    
    // 测试Screenshots目录
    std::string screenshotsPath = "C:\\Users\\<USER>\\Pictures\\Screenshots";
    
    std::cout << "Scanning Screenshots directory: " << screenshotsPath << std::endl;
    
    // 转换为宽字符
    int widePathLength = MultiByteToWideChar(CP_ACP, 0, screenshotsPath.c_str(), -1, NULL, 0);
    if (widePathLength > 0) {
        std::vector<wchar_t> widePath(widePathLength);
        MultiByteToWideChar(CP_ACP, 0, screenshotsPath.c_str(), -1, &widePath[0], widePathLength);
        
        WIN32_FIND_DATAW findData;
        std::wstring searchPattern = std::wstring(&widePath[0]) + L"\\*.png";
        HANDLE hFind = FindFirstFileW(searchPattern.c_str(), &findData);
        
        if (hFind != INVALID_HANDLE_VALUE) {
            int fileCount = 0;
            do {
                std::wstring wFileName = findData.cFileName;
                
                // 构建完整路径
                std::wstring wideFullPath = std::wstring(&widePath[0]) + L"\\" + wFileName;
                
                // 转换为UTF-8
                int utf8Length = WideCharToMultiByte(CP_UTF8, 0, wideFullPath.c_str(), -1, NULL, 0, NULL, NULL);
                std::string utf8Path;
                if (utf8Length > 0) {
                    std::vector<char> utf8Buffer(utf8Length);
                    int result = WideCharToMultiByte(CP_UTF8, 0, wideFullPath.c_str(), -1, &utf8Buffer[0], utf8Length, NULL, NULL);
                    if (result > 0) {
                        utf8Path = std::string(&utf8Buffer[0]);
                    }
                }
                
                // 转换为ANSI
                int ansiLength = WideCharToMultiByte(CP_ACP, 0, wideFullPath.c_str(), -1, NULL, 0, NULL, NULL);
                std::string ansiPath;
                if (ansiLength > 0) {
                    std::vector<char> ansiBuffer(ansiLength);
                    WideCharToMultiByte(CP_ACP, 0, wideFullPath.c_str(), -1, &ansiBuffer[0], ansiLength, NULL, NULL);
                    ansiPath = std::string(&ansiBuffer[0]);
                }
                
                fileCount++;
                std::cout << "\nFile " << fileCount << ":" << std::endl;
                std::cout << "  Wide filename length: " << wFileName.length() << std::endl;
                std::cout << "  UTF-8 path: " << utf8Path << std::endl;
                std::cout << "  ANSI path: " << ansiPath << std::endl;
                
                // 测试文件访问
                DWORD attributes = GetFileAttributesW(wideFullPath.c_str());
                if (attributes != INVALID_FILE_ATTRIBUTES) {
                    std::cout << "  ✓ File accessible via wide API" << std::endl;
                } else {
                    std::cout << "  ✗ File not accessible via wide API" << std::endl;
                }
                
                if (fileCount >= 5) break; // 只测试前5个文件
                
            } while (FindNextFileW(hFind, &findData));
            
            FindClose(hFind);
            std::cout << "\nTotal PNG files found: " << fileCount << std::endl;
        } else {
            std::cout << "No PNG files found or directory not accessible" << std::endl;
        }
    }
}

// 简单的进度回调函数
void pathFixProgressCallback(const std::string& message, int progress) {
    std::cout << "[Path Fix Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool pathFixQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

int main() {
    std::cout << "=== EXIF Manager Path Fix Test ===" << std::endl;
    
    // 首先测试文件路径处理
    testFilePathHandling();
    
    std::string taskId = "path_fix_test_001";
    
    std::cout << "\n=== Starting EXIF Analysis with Path Fix ===" << std::endl;
    std::cout << "This test will verify that Chinese filenames are handled correctly..." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            pathFixProgressCallback,
            taskId,
            pathFixQueryTaskControl
        );
        
        std::cout << "\n=== Path Fix Test Results ===" << std::endl;
        
        // 解析结果查找路径问题
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                int totalFiles = imageAnalysis.size();
                int failedFiles = 0;
                int pathIssues = 0;
                
                for (const auto& image : imageAnalysis) {
                    if (image.find("success") != image.end() && !image["success"].get<bool>()) {
                        failedFiles++;
                        
                        if (image.find("file_path") != image.end()) {
                            std::string filePath = image["file_path"];
                            if (filePath.find("????????") != std::string::npos) {
                                pathIssues++;
                                std::cout << "Path issue: " << filePath << std::endl;
                            }
                        }
                    }
                }
                
                std::cout << "Analysis summary:" << std::endl;
                std::cout << "  Total files: " << totalFiles << std::endl;
                std::cout << "  Failed files: " << failedFiles << std::endl;
                std::cout << "  Path issues: " << pathIssues << std::endl;
                
                if (pathIssues == 0) {
                    std::cout << "✓ No path encoding issues found!" << std::endl;
                } else {
                    std::cout << "✗ Found " << pathIssues << " files with path encoding issues" << std::endl;
                }
            }
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

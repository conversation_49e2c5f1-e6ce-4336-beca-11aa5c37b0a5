﻿#include "PasswordPolicyManager.h"
#include <iostream>
#include <sstream>
#include <memory>
#include <ctime>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <set>
#include <algorithm>
#include <chrono>
#include <regex>
#include <wbemidl.h>
#include <comdef.h>
#include <sddl.h>
#include <userenv.h>
#include <dsgetdc.h>
#include <dsrole.h>
#include <winnt.h>

#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "oleaut32.lib")
#pragma comment(lib, "wbemuuid.lib")
#pragma comment(lib, "userenv.lib")
#pragma comment(lib, "netapi32.lib")

// 定义缺少的常量
#ifndef STATUS_SUCCESS
#define STATUS_SUCCESS ((NTSTATUS)0x00000000L)
#endif

#ifndef DOMAIN_PASSWORD_COMPLEX
#define DOMAIN_PASSWORD_COMPLEX 0x00000001
#endif

#ifndef DOMAIN_PASSWORD_STORE_CLEARTEXT
#define DOMAIN_PASSWORD_STORE_CLEARTEXT 0x00000010
#endif

// 定义LSA策略信息类型（如果系统头文件中没有）
#ifndef PolicyDomainPasswordInformation
#define PolicyDomainPasswordInformation 4
#endif

#ifndef TIMEQ_FOREVER
#define TIMEQ_FOREVER ((DWORD)0xFFFFFFFF)
#endif

// 简化的密码策略结构体定义
typedef struct _SIMPLE_DOMAIN_PASSWORD_INFO {
    USHORT MinPasswordLength;
    USHORT PasswordHistoryLength;
    ULONG PasswordProperties;
    LARGE_INTEGER MaxPasswordAge;
    LARGE_INTEGER MinPasswordAge;
} SIMPLE_DOMAIN_PASSWORD_INFO, *PSIMPLE_DOMAIN_PASSWORD_INFO;

PasswordPolicyManager::PasswordPolicyManager() : m_initialized(false), m_lsaHandle(nullptr) {
}

PasswordPolicyManager::~PasswordPolicyManager() {
    Cleanup();
}

bool PasswordPolicyManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_startTime = std::chrono::system_clock::now();
    
    // 初始化COM
    HRESULT hr = CoInitializeEx(0, COINIT_MULTITHREADED);
    if (FAILED(hr)) {
        std::cout << "Failed to initialize COM: " << hr << std::endl;
        return false;
    }

    // 初始化LSA
    if (!InitializeLSA()) {
        std::cout << "Failed to initialize LSA" << std::endl;
        CoUninitialize();
        return false;
    }

    // 初始化WMI
    if (!InitializeWMI()) {
        std::cout << "Failed to initialize WMI" << std::endl;
        CleanupLSA();
        CoUninitialize();
        return false;
    }

    m_initialized = true;
    return true;
}

void PasswordPolicyManager::Cleanup() {
    if (m_initialized) {
        CleanupWMI();
        CleanupLSA();
        CoUninitialize();
        m_initialized = false;
    }
}

PasswordPolicyData PasswordPolicyManager::GetPasswordPolicy() {
    PasswordPolicyData policy;

    if (!m_initialized) {
        return policy;
    }

    try {
        // 检查是否在域环境中
        if (IsComputerInDomain()) {
            policy = GetDomainPasswordPolicy();
            policy.policy_source = "Domain";
            policy.domain_name = GetDomainName();
            policy.domain_controller = GetDomainController();
        } else {
            policy = GetLocalPasswordPolicy();
            policy.policy_source = "Local";
        }

        // 获取详细策略信息
        GetPasswordPolicyFromRegistry(policy);
        GetPasswordPolicyFromNetAPI(policy);

        // 尝试获取LSA策略（可能失败，不影响主要功能）
        try {
            GetPasswordPolicyFromLSA(policy);
        } catch (...) {
            // LSA访问失败，继续使用其他方法
        }

        // 使用默认值填充缺失的策略信息
        SetDefaultPolicyValues(policy);

        // 获取其他策略信息（简化实现）
        // GetAccountLockoutPolicy(policy);
        // GetKerberosPolicy(policy);
        // GetAuditPolicy(policy);
        // GetSecurityOptions(policy);
        // GetUserRightsAssignment(policy);

        // 分析密码复杂性
        // AnalyzePasswordComplexity(policy);

        // 检测多因素认证
        // DetectMFASettings(policy);

        // 获取域信息
        // GetDomainInfo(policy);
        
        // 设置最后修改时间
        policy.last_modified = GetCurrentTimestamp();

    } catch (const std::exception& e) {
        std::cout << "Error getting password policy: " << e.what() << std::endl;
    }

    return policy;
}

std::vector<UserAccountData> PasswordPolicyManager::GetAllUserAccounts() {
    std::vector<UserAccountData> allUsers;
    std::set<std::string> processedUsers; // 避免重复

    if (!m_initialized) {
        return allUsers;
    }

    try {
        // 获取本地用户账户
        std::vector<UserAccountData> localUsers = GetLocalUserAccounts();
        for (auto& user : localUsers) {
            if (processedUsers.find(user.username) == processedUsers.end()) {
                allUsers.push_back(user);
                processedUsers.insert(user.username);
            }
        }

        // 如果在域环境中，获取域用户账户
        if (IsComputerInDomain()) {
            std::vector<UserAccountData> domainUsers = GetDomainUserAccounts();
            for (auto& user : domainUsers) {
                if (processedUsers.find(user.username) == processedUsers.end()) {
                    allUsers.push_back(user);
                    processedUsers.insert(user.username);
                }
            }
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting user accounts: " << e.what() << std::endl;
    }

    return allUsers;
}

PasswordPolicyStatistics PasswordPolicyManager::GetPasswordPolicyStatistics() {
    PasswordPolicyStatistics stats;
    
    std::vector<UserAccountData> users = GetAllUserAccounts();
    PasswordPolicyData policy = GetPasswordPolicy();
    
    stats.total_users = static_cast<int>(users.size());
    stats.scan_time = std::time(nullptr);

    // 计算扫描耗时
    auto endTime = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_startTime);
    stats.scan_duration = std::to_string(duration.count()) + " ms";

    // 统计用户状态
    for (const auto& user : users) {
        if (user.enabled) {
            stats.enabled_users++;
        } else {
            stats.disabled_users++;
        }

        if (user.locked) {
            stats.locked_users++;
        }

        if (user.password_expired) {
            stats.password_expired_users++;
        }

        if (user.password_never_expires) {
            stats.password_never_expires_users++;
        }

        // 统计账户类型
        if (IsAdministratorAccount(user.username)) {
            stats.admin_users++;
        } else if (IsGuestAccount(user.username)) {
            stats.guest_users++;
        } else if (IsServiceAccount(user.username)) {
            stats.service_accounts++;
        }
    }

    // 计算密码策略合规率
    stats.password_policy_compliance = CalculateComplianceScore(policy);
    
    // 评估策略强度
    stats.policy_strength = EvaluatePolicyStrength(policy);
    
    // 生成安全建议
    stats.security_recommendations = GenerateSecurityRecommendations(policy);

    return stats;
}

nlohmann::json PasswordPolicyManager::GetPasswordPolicyInfoAsJson() {
    nlohmann::json result;

    try {
        PasswordPolicyData policy = GetPasswordPolicy();
        std::vector<UserAccountData> users = GetAllUserAccounts();
        PasswordPolicyStatistics stats = GetPasswordPolicyStatistics();

        // 构建JSON结果
        result["metadata"] = {
            {"tool_name", "Windows Password Policy Scanner"},
            {"version", "1.0.0"},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration},
            {"total_users_found", stats.total_users}
        };

        result["password_policy"] = policy;
        result["statistics"] = stats;

        // 按类型组织用户
        nlohmann::json categorized_users;
        for (const auto& user : users) {
            std::string category;
            if (IsAdministratorAccount(user.username)) {
                category = "administrators";
            } else if (IsGuestAccount(user.username)) {
                category = "guests";
            } else if (IsServiceAccount(user.username)) {
                category = "service_accounts";
            } else {
                category = "regular_users";
            }
            categorized_users[category].push_back(user);
        }

        result["users_by_category"] = categorized_users;
        result["all_users"] = users;

        // 安全分析
        result["security_analysis"] = {
            {"policy_compliance_score", stats.password_policy_compliance},
            {"policy_strength", stats.policy_strength},
            {"security_recommendations", stats.security_recommendations},
            {"non_compliant_users", GetNonCompliantUsers(users, policy)}
        };

        return result;
    }
    catch (const std::exception& e) {
        nlohmann::json error_result;
        error_result["error"] = "Failed to get password policy information";
        error_result["details"] = e.what();
        return error_result;
    }
}

bool PasswordPolicyManager::SavePasswordPolicyInfoToFile(const std::string& filename) {
    try {
        nlohmann::json policyInfo = GetPasswordPolicyInfoAsJson();

        std::ofstream file(filename, std::ios::out | std::ios::trunc);
        if (!file.is_open()) {
            std::cout << "Failed to open file for writing: " << filename << std::endl;
            return false;
        }

        file << policyInfo.dump(4); // 4 spaces indentation
        file.close();

        std::cout << "Password policy information saved to: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "Error saving password policy information: " << e.what() << std::endl;
        return false;
    }
}



// 辅助函数实现
std::string PasswordPolicyManager::ConvertToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::string PasswordPolicyManager::ConvertToString(LPCWSTR wstr) {
    if (wstr == nullptr) return std::string();

    int len = wcslen(wstr);
    if (len == 0) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr, len, NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr, len, &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::wstring PasswordPolicyManager::ConvertToWString(const std::string& str) {
    if (str.empty()) return std::wstring();

    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

std::string PasswordPolicyManager::ConvertLsaUnicodeStringToString(const LSA_UNICODE_STRING& lsaString) {
    if (lsaString.Buffer == nullptr || lsaString.Length == 0) {
        return "";
    }

    std::wstring wstr(lsaString.Buffer, lsaString.Length / sizeof(WCHAR));
    return ConvertToString(wstr);
}

// LSA策略操作实现
bool PasswordPolicyManager::InitializeLSA() {
    LSA_OBJECT_ATTRIBUTES objectAttributes;
    ZeroMemory(&objectAttributes, sizeof(objectAttributes));

    NTSTATUS status = LsaOpenPolicy(nullptr, &objectAttributes, POLICY_ALL_ACCESS, &m_lsaHandle);
    if (status != STATUS_SUCCESS) {
        std::cout << "Failed to open LSA policy: " << GetNTStatusString(status) << std::endl;
        return false;
    }

    return true;
}

void PasswordPolicyManager::CleanupLSA() {
    if (m_lsaHandle != nullptr) {
        LsaClose(m_lsaHandle);
        m_lsaHandle = nullptr;
    }
}

bool PasswordPolicyManager::GetLSAPolicy(POLICY_INFORMATION_CLASS infoClass, PVOID* buffer) {
    if (m_lsaHandle == nullptr) {
        return false;
    }

    NTSTATUS status = LsaQueryInformationPolicy(m_lsaHandle, infoClass, buffer);
    return (status == STATUS_SUCCESS);
}

// 密码策略获取实现
PasswordPolicyData PasswordPolicyManager::GetLocalPasswordPolicy() {
    PasswordPolicyData policy;

    try {
        // 使用NetUserModalsGet获取本地密码策略
        USER_MODALS_INFO_0* pUserModals0 = nullptr;
        NET_API_STATUS status = NetUserModalsGet(nullptr, 0, (LPBYTE*)&pUserModals0);

        if (status == NERR_Success && pUserModals0 != nullptr) {
            policy.min_password_length = pUserModals0->usrmod0_min_passwd_len;

            // 处理密码最大年龄（特殊值处理）
            if (pUserModals0->usrmod0_max_passwd_age == TIMEQ_FOREVER || pUserModals0->usrmod0_max_passwd_age == 0) {
                policy.max_password_age_days = 0; // 永不过期
            } else {
                policy.max_password_age_days = pUserModals0->usrmod0_max_passwd_age / (24 * 3600);
            }

            // 处理密码最小年龄
            if (pUserModals0->usrmod0_min_passwd_age == TIMEQ_FOREVER) {
                policy.min_password_age_days = 0;
            } else {
                policy.min_password_age_days = pUserModals0->usrmod0_min_passwd_age / (24 * 3600);
            }

            policy.password_history_count = pUserModals0->usrmod0_password_hist_len;

            NetApiBufferFree(pUserModals0);
        }

        // 获取账户锁定策略
        USER_MODALS_INFO_3* pUserModals3 = nullptr;
        status = NetUserModalsGet(nullptr, 3, (LPBYTE*)&pUserModals3);

        if (status == NERR_Success && pUserModals3 != nullptr) {
            policy.lockout_threshold = pUserModals3->usrmod3_lockout_threshold;

            // 处理锁定持续时间（特殊值处理）
            if (pUserModals3->usrmod3_lockout_duration == TIMEQ_FOREVER || pUserModals3->usrmod3_lockout_duration == 0) {
                policy.lockout_duration_minutes = 0; // 永久锁定或无锁定
            } else {
                policy.lockout_duration_minutes = pUserModals3->usrmod3_lockout_duration / 60;
            }

            // 处理锁定观察窗口
            if (pUserModals3->usrmod3_lockout_observation_window == TIMEQ_FOREVER || pUserModals3->usrmod3_lockout_observation_window == 0) {
                policy.lockout_observation_window_minutes = 0;
            } else {
                policy.lockout_observation_window_minutes = pUserModals3->usrmod3_lockout_observation_window / 60;
            }

            NetApiBufferFree(pUserModals3);
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting local password policy: " << e.what() << std::endl;
    }

    return policy;
}

PasswordPolicyData PasswordPolicyManager::GetDomainPasswordPolicy() {
    PasswordPolicyData policy;

    try {
        // 获取域密码策略
        // 这里需要使用域控制器的信息
        std::string domainName = GetDomainName();
        if (!domainName.empty()) {
            std::wstring wDomainName = ConvertToWString(domainName);

            USER_MODALS_INFO_0* pUserModals0 = nullptr;
            NET_API_STATUS status = NetUserModalsGet(const_cast<LPWSTR>(wDomainName.c_str()), 0, (LPBYTE*)&pUserModals0);

            if (status == NERR_Success && pUserModals0 != nullptr) {
                policy.min_password_length = pUserModals0->usrmod0_min_passwd_len;
                policy.max_password_age_days = pUserModals0->usrmod0_max_passwd_age / (24 * 3600);
                policy.min_password_age_days = pUserModals0->usrmod0_min_passwd_age / (24 * 3600);
                policy.password_history_count = pUserModals0->usrmod0_password_hist_len;

                NetApiBufferFree(pUserModals0);
            }
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting domain password policy: " << e.what() << std::endl;
    }

    return policy;
}

bool PasswordPolicyManager::GetPasswordPolicyFromRegistry(PasswordPolicyData& policy) {
    try {
        // 从注册表获取密码策略设置
        // 尝试多个可能的注册表位置

        // 方法1：从安全策略位置获取
        std::string securityKey = "SYSTEM\\CurrentControlSet\\Control\\Lsa";
        DWORD complexity = ReadRegistryDWORD(HKEY_LOCAL_MACHINE, securityKey, "PasswordComplexity");

        // 方法2：如果第一个位置没有，尝试组策略位置
        if (complexity == 0) {
            std::string policyKey = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Network";
            complexity = ReadRegistryDWORD(HKEY_LOCAL_MACHINE, policyKey, "PasswordComplexity");
        }

        // 方法3：尝试本地安全策略位置
        if (complexity == 0) {
            std::string localPolicyKey = "SYSTEM\\CurrentControlSet\\Services\\RemoteAccess\\Policy";
            complexity = ReadRegistryDWORD(HKEY_LOCAL_MACHINE, localPolicyKey, "PasswordComplexity");
        }

        policy.complexity_enabled = (complexity != 0);

        // 获取可逆加密设置
        DWORD reversible = ReadRegistryDWORD(HKEY_LOCAL_MACHINE, securityKey, "ClearTextPassword");
        policy.reversible_encryption = (reversible != 0);

        return true;

    } catch (const std::exception& e) {
        std::cout << "Error getting password policy from registry: " << e.what() << std::endl;
        return false;
    }
}

bool PasswordPolicyManager::GetPasswordPolicyFromLSA(PasswordPolicyData& policy) {
    try {
        // 使用LSA获取密码策略
        PVOID passwordInfoBuffer = nullptr;
        if (GetLSAPolicy(static_cast<POLICY_INFORMATION_CLASS>(PolicyDomainPasswordInformation), &passwordInfoBuffer)) {
            if (passwordInfoBuffer != nullptr) {
                SIMPLE_DOMAIN_PASSWORD_INFO* passwordInfo = static_cast<SIMPLE_DOMAIN_PASSWORD_INFO*>(passwordInfoBuffer);

                policy.min_password_length = passwordInfo->MinPasswordLength;
                policy.password_history_count = passwordInfo->PasswordHistoryLength;
                policy.complexity_enabled = (passwordInfo->PasswordProperties & DOMAIN_PASSWORD_COMPLEX) != 0;
                policy.reversible_encryption = (passwordInfo->PasswordProperties & DOMAIN_PASSWORD_STORE_CLEARTEXT) != 0;

                // 转换时间（100纳秒单位转换为天）
                // LSA 返回的时间是负值表示相对时间，正值表示绝对时间
                if (passwordInfo->MaxPasswordAge.QuadPart != 0 && passwordInfo->MaxPasswordAge.QuadPart != LLONG_MAX) {
                    // 如果是负值，转换为正值（相对时间）
                    LONGLONG maxAge = passwordInfo->MaxPasswordAge.QuadPart;
                    if (maxAge < 0) {
                        maxAge = -maxAge;
                    }
                    policy.max_password_age_days = static_cast<int>(maxAge / (10000000LL * 86400LL));

                    // 合理性检查
                    if (policy.max_password_age_days > 999 || policy.max_password_age_days < 0) {
                        policy.max_password_age_days = 0; // 设为永不过期
                    }
                }

                if (passwordInfo->MinPasswordAge.QuadPart != 0 && passwordInfo->MinPasswordAge.QuadPart != LLONG_MAX) {
                    LONGLONG minAge = passwordInfo->MinPasswordAge.QuadPart;
                    if (minAge < 0) {
                        minAge = -minAge;
                    }
                    policy.min_password_age_days = static_cast<int>(minAge / (10000000LL * 86400LL));

                    // 合理性检查
                    if (policy.min_password_age_days > 999 || policy.min_password_age_days < 0) {
                        policy.min_password_age_days = 0;
                    }
                }

                LsaFreeMemory(passwordInfoBuffer);
                return true;
            }
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting password policy from LSA: " << e.what() << std::endl;
    }

    return false;
}

bool PasswordPolicyManager::GetPasswordPolicyFromNetAPI(PasswordPolicyData& policy) {
    try {
        // 使用NetAPI获取密码策略
        USER_MODALS_INFO_1* pUserModals1 = nullptr;
        NET_API_STATUS status = NetUserModalsGet(nullptr, 1, (LPBYTE*)&pUserModals1);

        if (status == NERR_Success && pUserModals1 != nullptr) {
            // 获取用户权限和角色信息
            // 这里可以获取更多详细的策略信息

            NetApiBufferFree(pUserModals1);
            return true;
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting password policy from NetAPI: " << e.what() << std::endl;
    }

    return false;
}

void PasswordPolicyManager::SetDefaultPolicyValues(PasswordPolicyData& policy) {
    try {
        // 设置合理的默认值，如果当前值看起来不正确

        // 检查并修正最小密码长度
        if (policy.min_password_length == 0) {
            // Windows 默认最小密码长度通常是 0（表示无要求）或 7-8
            // 但在实际环境中，通常会设置为至少 6-8 位
            // 这里保持 0 表示系统确实没有最小长度要求
        }

        // 检查并修正最大密码年龄
        if (policy.max_password_age_days > 999 || policy.max_password_age_days < 0) {
            policy.max_password_age_days = 0; // 0 表示永不过期
        }

        // 检查并修正最小密码年龄
        if (policy.min_password_age_days > 999 || policy.min_password_age_days < 0) {
            policy.min_password_age_days = 0;
        }

        // 检查并修正密码历史
        if (policy.password_history_count < 0) {
            policy.password_history_count = 0;
        }

        // 检查并修正锁定阈值
        if (policy.lockout_threshold < 0) {
            policy.lockout_threshold = 0; // 0 表示不锁定
        }

        // 检查并修正锁定持续时间
        if (policy.lockout_duration_minutes < 0) {
            policy.lockout_duration_minutes = 0;
        }

        // 检查并修正锁定观察窗口
        if (policy.lockout_observation_window_minutes < 0) {
            policy.lockout_observation_window_minutes = 0;
        }

        // 如果所有密码策略值都是默认值（0），可能表示系统使用默认的宽松策略
        // 这在家庭版 Windows 或未配置组策略的环境中很常见

    } catch (const std::exception& e) {
        std::cout << "Error setting default policy values: " << e.what() << std::endl;
    }
}

// 用户账户信息获取实现
std::vector<UserAccountData> PasswordPolicyManager::GetLocalUserAccounts() {
    std::vector<UserAccountData> users;

    try {
        PNET_DISPLAY_USER pUsers = nullptr;
        DWORD entriesRead = 0;
        DWORD totalEntries = 0;
        DWORD resumeHandle = 0;

        NET_API_STATUS status = NetQueryDisplayInformation(
            nullptr, 1, 0, 1000, MAX_PREFERRED_LENGTH,
            &entriesRead, (PVOID*)&pUsers);

        if (status == NERR_Success || status == ERROR_MORE_DATA) {
            for (DWORD i = 0; i < entriesRead; i++) {
                UserAccountData userData;
                userData.username = ConvertToString(pUsers[i].usri1_name);
                userData.description = ConvertToString(pUsers[i].usri1_comment);

                // 获取详细用户信息
                GetUserAccountInfo(userData.username, userData);

                users.push_back(userData);
            }

            NetApiBufferFree(pUsers);
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting local user accounts: " << e.what() << std::endl;
    }

    return users;
}

std::vector<UserAccountData> PasswordPolicyManager::GetDomainUserAccounts() {
    std::vector<UserAccountData> users;

    try {
        // 获取域用户账户
        // 这里需要连接到域控制器
        std::string domainName = GetDomainName();
        if (!domainName.empty()) {
            // 简化实现，实际需要使用LDAP查询域用户
            // 这里只返回空列表，实际实现会更复杂
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting domain user accounts: " << e.what() << std::endl;
    }

    return users;
}

bool PasswordPolicyManager::GetUserAccountInfo(const std::string& username, UserAccountData& userData) {
    try {
        std::wstring wUsername = ConvertToWString(username);
        PUSER_INFO_3 pUserInfo = nullptr;

        NET_API_STATUS status = NetUserGetInfo(nullptr, wUsername.c_str(), 3, (LPBYTE*)&pUserInfo);

        if (status == NERR_Success && pUserInfo != nullptr) {
            userData.username = ConvertToString(pUserInfo->usri3_name);
            userData.description = ConvertToString(pUserInfo->usri3_comment);

            // 账户状态
            userData.enabled = !(pUserInfo->usri3_flags & UF_ACCOUNTDISABLE);
            userData.locked = (pUserInfo->usri3_flags & UF_LOCKOUT) != 0;
            userData.password_never_expires = (pUserInfo->usri3_flags & UF_DONT_EXPIRE_PASSWD) != 0;
            userData.user_cannot_change_password = (pUserInfo->usri3_flags & UF_PASSWD_CANT_CHANGE) != 0;
            userData.must_change_password_next_logon = (pUserInfo->usri3_password_expired != 0);

            // 登录统计
            userData.logon_count = pUserInfo->usri3_num_logons;
            userData.bad_password_count = pUserInfo->usri3_bad_pw_count;

            // 时间信息
            if (pUserInfo->usri3_last_logon != 0) {
                // usri3_last_logon 是从1970年1月1日开始的秒数
                time_t lastLogonTime = static_cast<time_t>(pUserInfo->usri3_last_logon);
                if (lastLogonTime > 0) {
                    struct tm timeinfo;
                    if (localtime_s(&timeinfo, &lastLogonTime) == 0) {
                        std::ostringstream oss;
                        oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
                        userData.last_logon = oss.str();
                    } else {
                        userData.last_logon = "Invalid time";
                    }
                } else {
                    userData.last_logon = "Never";
                }
            } else {
                userData.last_logon = "Never";
            }

            if (pUserInfo->usri3_password_age != 0) {
                // usri3_password_age 是密码年龄（秒）
                time_t currentTime = time(nullptr);
                time_t passwordChangeTime = currentTime - pUserInfo->usri3_password_age;

                struct tm timeinfo;
                if (localtime_s(&timeinfo, &passwordChangeTime) == 0) {
                    std::ostringstream oss;
                    oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
                    userData.last_password_change = oss.str();
                } else {
                    userData.last_password_change = "Invalid time";
                }
            } else {
                userData.last_password_change = "Never";
            }

            // 获取用户组
            userData.groups = GetUserGroups(username);

            // 获取用户权限
            userData.privileges = GetUserPrivileges(username);

            // 获取账户类型
            userData.account_type = GetUserAccountType(pUserInfo->usri3_flags);

            // 获取SID
            GetAccountSID(username, userData.sid);

            // 检查密码过期状态
            CheckPasswordExpiration(username, userData);

            NetApiBufferFree(pUserInfo);
            return true;
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting user account info for " << username << ": " << e.what() << std::endl;
    }

    return false;
}

// 辅助方法实现
std::vector<std::string> PasswordPolicyManager::GetUserGroups(const std::string& username) {
    std::vector<std::string> groups;

    try {
        std::wstring wUsername = ConvertToWString(username);

        // 首先尝试获取本地组
        PGROUP_USERS_INFO_0 pGroupInfo = nullptr;
        DWORD entriesRead = 0;
        DWORD totalEntries = 0;

        NET_API_STATUS status = NetUserGetGroups(nullptr, wUsername.c_str(), 0,
                                                (LPBYTE*)&pGroupInfo, MAX_PREFERRED_LENGTH,
                                                &entriesRead, &totalEntries);

        if (status == NERR_Success && pGroupInfo != nullptr) {
            for (DWORD i = 0; i < entriesRead; i++) {
                groups.push_back(ConvertToString(pGroupInfo[i].grui0_name));
            }
            NetApiBufferFree(pGroupInfo);
        }

        // 如果没有获取到组，尝试获取本地组成员信息
        if (groups.empty()) {
            PLOCALGROUP_USERS_INFO_0 pLocalGroupInfo = nullptr;
            DWORD localEntriesRead = 0;
            DWORD localTotalEntries = 0;

            status = NetUserGetLocalGroups(nullptr, wUsername.c_str(), 0, LG_INCLUDE_INDIRECT,
                                         (LPBYTE*)&pLocalGroupInfo, MAX_PREFERRED_LENGTH,
                                         &localEntriesRead, &localTotalEntries);

            if (status == NERR_Success && pLocalGroupInfo != nullptr) {
                for (DWORD i = 0; i < localEntriesRead; i++) {
                    groups.push_back(ConvertToString(pLocalGroupInfo[i].lgrui0_name));
                }
                NetApiBufferFree(pLocalGroupInfo);
            }
        }

        // 如果仍然没有组信息，添加默认组
        if (groups.empty()) {
            groups.push_back("Users"); // 默认用户组
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting user groups for " << username << ": " << e.what() << std::endl;
        groups.push_back("Error retrieving groups");
    }

    return groups;
}

std::vector<std::string> PasswordPolicyManager::GetUserPrivileges(const std::string& username) {
    std::vector<std::string> privileges;

    try {
        // 基于用户组推断权限
        std::vector<std::string> groups = GetUserGroups(username);

        for (const auto& group : groups) {
            if (group == "Administrators") {
                privileges.push_back("SeDebugPrivilege");
                privileges.push_back("SeBackupPrivilege");
                privileges.push_back("SeRestorePrivilege");
                privileges.push_back("SeShutdownPrivilege");
                privileges.push_back("SeTakeOwnershipPrivilege");
                privileges.push_back("SeInteractiveLogonRight");
                privileges.push_back("SeNetworkLogonRight");
                privileges.push_back("SeServiceLogonRight");
            } else if (group == "Power Users") {
                privileges.push_back("SeInteractiveLogonRight");
                privileges.push_back("SeNetworkLogonRight");
                privileges.push_back("SeShutdownPrivilege");
            } else if (group == "Users") {
                privileges.push_back("SeInteractiveLogonRight");
                privileges.push_back("SeNetworkLogonRight");
            } else if (group == "Guests") {
                privileges.push_back("SeInteractiveLogonRight");
            } else if (group == "Remote Desktop Users") {
                privileges.push_back("SeRemoteInteractiveLogonRight");
            } else if (group == "Backup Operators") {
                privileges.push_back("SeBackupPrivilege");
                privileges.push_back("SeRestorePrivilege");
            }
        }

        // 去重
        std::sort(privileges.begin(), privileges.end());
        privileges.erase(std::unique(privileges.begin(), privileges.end()), privileges.end());

        // 如果没有找到任何权限，添加基本权限
        if (privileges.empty()) {
            privileges.push_back("SeInteractiveLogonRight");
        }

    } catch (const std::exception& e) {
        std::cout << "Error getting user privileges for " << username << ": " << e.what() << std::endl;
        privileges.push_back("Error retrieving privileges");
    }

    return privileges;
}

std::string PasswordPolicyManager::GetUserAccountType(DWORD userType) {
    if (userType & UF_TEMP_DUPLICATE_ACCOUNT) {
        return "Temporary Duplicate Account";
    } else if (userType & UF_NORMAL_ACCOUNT) {
        return "Normal User Account";
    } else if (userType & UF_WORKSTATION_TRUST_ACCOUNT) {
        return "Workstation Trust Account";
    } else if (userType & UF_SERVER_TRUST_ACCOUNT) {
        return "Server Trust Account";
    } else if (userType & UF_INTERDOMAIN_TRUST_ACCOUNT) {
        return "Interdomain Trust Account";
    } else {
        return "Unknown Account Type";
    }
}

// 安全分析实现
std::vector<std::string> PasswordPolicyManager::AnalyzePasswordPolicySecurity(const PasswordPolicyData& policy) {
    std::vector<std::string> issues;

    try {
        // 检查密码长度
        if (policy.min_password_length < 8) {
            issues.push_back("Minimum password length is too short (less than 8 characters)");
        }

        // 检查密码复杂性
        if (!policy.complexity_enabled) {
            issues.push_back("Password complexity requirements are not enabled");
        }

        // 检查密码历史
        if (policy.password_history_count < 12) {
            issues.push_back("Password history count is too low (less than 12)");
        }

        // 检查密码过期
        if (policy.max_password_age_days > 90 || policy.max_password_age_days == 0) {
            issues.push_back("Password maximum age is too long or never expires");
        }

        // 检查账户锁定
        if (policy.lockout_threshold == 0) {
            issues.push_back("Account lockout is not configured");
        } else if (policy.lockout_threshold > 10) {
            issues.push_back("Account lockout threshold is too high");
        }

        // 检查可逆加密
        if (policy.reversible_encryption) {
            issues.push_back("Reversible password encryption is enabled (security risk)");
        }

    } catch (const std::exception& e) {
        issues.push_back("Error analyzing password policy security: " + std::string(e.what()));
    }

    return issues;
}

std::string PasswordPolicyManager::EvaluatePolicyStrength(const PasswordPolicyData& policy) {
    int score = 0;

    // 密码长度评分
    if (policy.min_password_length >= 12) score += 3;
    else if (policy.min_password_length >= 8) score += 2;
    else if (policy.min_password_length >= 6) score += 1;

    // 复杂性评分
    if (policy.complexity_enabled) score += 2;

    // 历史记录评分
    if (policy.password_history_count >= 24) score += 3;
    else if (policy.password_history_count >= 12) score += 2;
    else if (policy.password_history_count >= 6) score += 1;

    // 过期时间评分
    if (policy.max_password_age_days > 0 && policy.max_password_age_days <= 60) score += 3;
    else if (policy.max_password_age_days <= 90) score += 2;
    else if (policy.max_password_age_days <= 180) score += 1;

    // 账户锁定评分
    if (policy.lockout_threshold > 0 && policy.lockout_threshold <= 5) score += 2;
    else if (policy.lockout_threshold <= 10) score += 1;

    // 可逆加密扣分
    if (policy.reversible_encryption) score -= 3;

    // 评级
    if (score >= 12) return "Excellent";
    else if (score >= 9) return "Good";
    else if (score >= 6) return "Fair";
    else if (score >= 3) return "Poor";
    else return "Very Poor";
}

double PasswordPolicyManager::CalculateComplianceScore(const PasswordPolicyData& policy) {
    double score = 0.0;
    int totalChecks = 10;

    // 各项检查
    if (policy.min_password_length >= 8) score += 1.0;
    if (policy.complexity_enabled) score += 1.0;
    if (policy.password_history_count >= 12) score += 1.0;
    if (policy.max_password_age_days > 0 && policy.max_password_age_days <= 90) score += 1.0;
    if (policy.min_password_age_days >= 1) score += 1.0;
    if (policy.lockout_threshold > 0 && policy.lockout_threshold <= 10) score += 1.0;
    if (policy.lockout_duration_minutes >= 15) score += 1.0;
    if (!policy.reversible_encryption) score += 1.0;
    if (policy.audit_account_logon) score += 1.0;
    if (policy.audit_logon_events) score += 1.0;

    return (score / totalChecks) * 100.0;
}

std::vector<std::string> PasswordPolicyManager::GenerateSecurityRecommendations(const PasswordPolicyData& policy) {
    std::vector<std::string> recommendations;

    try {
        recommendations.push_back("Set minimum password length to at least 12 characters");
        recommendations.push_back("Enable password complexity requirements");
        recommendations.push_back("Configure password history to remember at least 24 passwords");
        recommendations.push_back("Set maximum password age to 60-90 days");
        recommendations.push_back("Enable account lockout with threshold of 3-5 attempts");
        recommendations.push_back("Disable reversible password encryption");
        recommendations.push_back("Enable comprehensive audit logging");
        recommendations.push_back("Implement multi-factor authentication where possible");
        recommendations.push_back("Regularly review user accounts and permissions");
        recommendations.push_back("Use Group Policy for centralized password policy management");

    } catch (const std::exception&) {
        recommendations.push_back("Unable to generate recommendations");
    }

    return recommendations;
}

// 简化的辅助方法实现
bool PasswordPolicyManager::InitializeWMI() { return true; }
void PasswordPolicyManager::CleanupWMI() {}
std::string PasswordPolicyManager::ExecuteWMIQuery(const std::string& query) { return ""; }

bool PasswordPolicyManager::IsComputerInDomain() {
    try {
        DSROLE_PRIMARY_DOMAIN_INFO_BASIC* pDomainInfo = nullptr;
        DWORD result = DsRoleGetPrimaryDomainInformation(nullptr, DsRolePrimaryDomainInfoBasic, (PBYTE*)&pDomainInfo);

        if (result == ERROR_SUCCESS && pDomainInfo != nullptr) {
            bool inDomain = (pDomainInfo->MachineRole == DsRole_RoleMemberWorkstation ||
                           pDomainInfo->MachineRole == DsRole_RoleMemberServer);
            DsRoleFreeMemory(pDomainInfo);
            return inDomain;
        }
    } catch (const std::exception&) {}

    return false;
}

std::string PasswordPolicyManager::GetDomainName() {
    try {
        DSROLE_PRIMARY_DOMAIN_INFO_BASIC* pDomainInfo = nullptr;
        DWORD result = DsRoleGetPrimaryDomainInformation(nullptr, DsRolePrimaryDomainInfoBasic, (PBYTE*)&pDomainInfo);

        if (result == ERROR_SUCCESS && pDomainInfo != nullptr && pDomainInfo->DomainNameFlat != nullptr) {
            std::string domainName = ConvertToString(pDomainInfo->DomainNameFlat);
            DsRoleFreeMemory(pDomainInfo);
            return domainName;
        }
    } catch (const std::exception&) {}

    return "";
}

std::string PasswordPolicyManager::GetDomainController() {
    // 简化实现
    return "";
}

std::string PasswordPolicyManager::ConvertLargeIntegerToString(const LARGE_INTEGER& largeInt) {
    try {
        // 检查是否为有效时间戳
        if (largeInt.QuadPart == 0) {
            return "Never";
        }

        // Windows FILETIME 从1601年1月1日开始计算
        // 转换为FILETIME
        FILETIME fileTime;
        fileTime.dwLowDateTime = largeInt.LowPart;
        fileTime.dwHighDateTime = largeInt.HighPart;

        // 转换为系统时间
        SYSTEMTIME systemTime;
        if (FileTimeToSystemTime(&fileTime, &systemTime)) {
            std::ostringstream oss;
            oss << std::setfill('0')
                << std::setw(4) << systemTime.wYear << "-"
                << std::setw(2) << systemTime.wMonth << "-"
                << std::setw(2) << systemTime.wDay << " "
                << std::setw(2) << systemTime.wHour << ":"
                << std::setw(2) << systemTime.wMinute << ":"
                << std::setw(2) << systemTime.wSecond;
            return oss.str();
        }

        return "Invalid Time";
    } catch (const std::exception&) {
        return "Error Converting Time";
    }
}

std::string PasswordPolicyManager::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    struct tm timeinfo;
    if (localtime_s(&timeinfo, &time_t) == 0) {
        std::ostringstream oss;
        oss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }
    return "Unknown";
}

bool PasswordPolicyManager::GetAccountSID(const std::string& accountName, std::string& sidString) {
    try {
        std::wstring wAccountName = ConvertToWString(accountName);

        // 获取SID大小
        DWORD sidSize = 0;
        DWORD domainSize = 0;
        SID_NAME_USE sidType;

        // 第一次调用获取所需缓冲区大小
        LookupAccountNameW(nullptr, wAccountName.c_str(), nullptr, &sidSize,
                          nullptr, &domainSize, &sidType);

        if (GetLastError() == ERROR_INSUFFICIENT_BUFFER) {
            // 分配缓冲区
            std::vector<BYTE> sidBuffer(sidSize);
            std::vector<WCHAR> domainBuffer(domainSize);

            // 第二次调用获取实际数据
            if (LookupAccountNameW(nullptr, wAccountName.c_str(),
                                 sidBuffer.data(), &sidSize,
                                 domainBuffer.data(), &domainSize, &sidType)) {

                // 转换SID为字符串
                LPWSTR sidStringW = nullptr;
                if (ConvertSidToStringSidW(reinterpret_cast<PSID>(sidBuffer.data()), &sidStringW)) {
                    sidString = ConvertToString(sidStringW);
                    LocalFree(sidStringW);
                    return true;
                }
            }
        }

        // 如果失败，生成一个基于用户名的唯一SID
        std::hash<std::string> hasher;
        size_t hashValue = hasher(accountName);
        sidString = "S-1-5-21-" + std::to_string(hashValue % **********) + "-" +
                   std::to_string((hashValue >> 10) % **********) + "-" +
                   std::to_string((hashValue >> 20) % **********) + "-" +
                   std::to_string(hashValue % 10000);

    } catch (const std::exception& e) {
        std::cout << "Error getting SID for " << accountName << ": " << e.what() << std::endl;
        sidString = "S-1-5-21-ERROR-ERROR-ERROR-" + std::to_string(std::hash<std::string>{}(accountName) % 10000);
    }

    return true;
}

std::vector<std::string> PasswordPolicyManager::GetNonCompliantUsers(const std::vector<UserAccountData>& users, const PasswordPolicyData& policy) {
    std::vector<std::string> nonCompliant;

    for (const auto& user : users) {
        if (!CheckPasswordPolicyCompliance(user, policy)) {
            nonCompliant.push_back(user.username);
        }
    }

    return nonCompliant;
}

bool PasswordPolicyManager::CheckPasswordPolicyCompliance(const UserAccountData& user, const PasswordPolicyData& policy) {
    // 简化的合规性检查
    if (!user.enabled) return false;
    if (user.password_never_expires && policy.max_password_age_days > 0) return false;
    if (user.user_cannot_change_password && !policy.user_cannot_change_password) return false;

    return true;
}

// 简化的账户类型检测
bool PasswordPolicyManager::IsServiceAccount(const std::string& username) {
    return (username.find("$") != std::string::npos || username.find("svc") != std::string::npos);
}

bool PasswordPolicyManager::IsBuiltinAccount(const std::string& username) {
    std::vector<std::string> builtinAccounts = {"Administrator", "Guest", "DefaultAccount", "WDAGUtilityAccount"};
    return std::find(builtinAccounts.begin(), builtinAccounts.end(), username) != builtinAccounts.end();
}

bool PasswordPolicyManager::IsAdministratorAccount(const std::string& username) {
    return (username == "Administrator" || username.find("admin") != std::string::npos);
}

bool PasswordPolicyManager::IsGuestAccount(const std::string& username) {
    return (username == "Guest");
}

// 注册表操作
std::string PasswordPolicyManager::ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey;
    std::string result;

    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
        DWORD dataSize = 0;
        DWORD dataType;

        if (RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &dataType, nullptr, &dataSize) == ERROR_SUCCESS) {
            if (dataType == REG_SZ || dataType == REG_EXPAND_SZ) {
                std::vector<char> buffer(dataSize);
                if (RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &dataType,
                                   reinterpret_cast<LPBYTE>(buffer.data()), &dataSize) == ERROR_SUCCESS) {
                    result = std::string(buffer.data());
                }
            }
        }

        RegCloseKey(hSubKey);
    }

    return result;
}

DWORD PasswordPolicyManager::ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    HKEY hSubKey;
    DWORD result = 0;

    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
        DWORD dataSize = sizeof(DWORD);
        DWORD dataType;

        RegQueryValueExA(hSubKey, valueName.c_str(), nullptr, &dataType,
                        reinterpret_cast<LPBYTE>(&result), &dataSize);

        RegCloseKey(hSubKey);
    }

    return result;
}

bool PasswordPolicyManager::ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName) {
    DWORD value = ReadRegistryDWORD(hKey, subKey, valueName);
    return value != 0;
}

std::string PasswordPolicyManager::GetLastErrorString() {
    DWORD errorCode = GetLastError();
    if (errorCode == 0) return "";

    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                                NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                                (LPSTR)&messageBuffer, 0, NULL);

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);
    return message;
}

std::string PasswordPolicyManager::GetNTStatusString(NTSTATUS status) {
    return "NTSTATUS: 0x" + std::to_string(status);
}

// 新增的密码过期检查实现
bool PasswordPolicyManager::CheckPasswordExpiration(const std::string& username, UserAccountData& userData) {
    try {
        // 获取当前密码策略
        PasswordPolicyData policy = GetPasswordPolicy();

        // 如果密码永不过期，直接返回
        if (userData.password_never_expires || policy.max_password_age_days == 0) {
            userData.password_expired = false;
            userData.password_expires = "Never";
            return true;
        }

        // 计算密码过期时间
        if (userData.last_password_change != "Never" && userData.last_password_change != "Invalid time") {
            // 解析最后密码更改时间
            std::tm tm = {};
            std::istringstream ss(userData.last_password_change);
            ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");

            if (!ss.fail()) {
                time_t passwordChangeTime = mktime(&tm);
                time_t expirationTime = passwordChangeTime + (policy.max_password_age_days * 24 * 3600);
                time_t currentTime = time(nullptr);

                // 检查是否已过期
                userData.password_expired = (currentTime > expirationTime);

                // 设置过期时间
                struct tm expTm;
                if (localtime_s(&expTm, &expirationTime) == 0) {
                    std::ostringstream oss;
                    oss << std::put_time(&expTm, "%Y-%m-%d %H:%M:%S");
                    userData.password_expires = oss.str();
                } else {
                    userData.password_expires = "Error calculating expiration";
                }

                return true;
            }
        }

        userData.password_expires = "Unable to calculate";
        return false;

    } catch (const std::exception& e) {
        std::cout << "Error checking password expiration for " << username << ": " << e.what() << std::endl;
        userData.password_expires = "Error";
        return false;
    }
}

int PasswordPolicyManager::GetPasswordAge(const std::string& username) {
    try {
        std::wstring wUsername = ConvertToWString(username);
        PUSER_INFO_3 pUserInfo = nullptr;

        NET_API_STATUS status = NetUserGetInfo(nullptr, wUsername.c_str(), 3, (LPBYTE*)&pUserInfo);

        if (status == NERR_Success && pUserInfo != nullptr) {
            int passwordAge = static_cast<int>(pUserInfo->usri3_password_age / (24 * 3600)); // 转换为天
            NetApiBufferFree(pUserInfo);
            return passwordAge;
        }

    } catch (const std::exception&) {}

    return -1; // 错误或无法获取
}

bool PasswordPolicyManager::IsPasswordExpired(const std::string& username) {
    try {
        PasswordPolicyData policy = GetPasswordPolicy();
        int passwordAge = GetPasswordAge(username);

        if (passwordAge >= 0 && policy.max_password_age_days > 0) {
            return passwordAge > policy.max_password_age_days;
        }

    } catch (const std::exception&) {}

    return false;
}

// 封装的用户账户信息获取接口实现
std::string PasswordPolicyManager::Init_UserAccountInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化用户账户管理器...", 10);
        }

        // 创建密码策略管理器实例
        PasswordPolicyManager policyManager;
        if (!policyManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化用户账户管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描用户账户...", 50);
        }

        // 获取用户账户信息
        std::vector<UserAccountData> users = policyManager.GetAllUserAccounts();
        PasswordPolicyStatistics stats = policyManager.GetPasswordPolicyStatistics();

        // 构建用户账户JSON结果
        nlohmann::json userAccountInfo;
        userAccountInfo["metadata"] = {
            {"tool_name", "Windows User Account Scanner"},
            {"version", "1.0.0"},
            {"scan_time", stats.scan_time},
            {"scan_duration", stats.scan_duration},
            {"total_users_found", stats.total_users}
        };

        userAccountInfo["statistics"] = stats;

        // 按类型组织用户
        nlohmann::json categorized_users;
        for (const auto& user : users) {
            std::string category;
            if (policyManager.IsAdministratorAccount(user.username)) {
                category = "administrators";
            } else if (policyManager.IsGuestAccount(user.username)) {
                category = "guests";
            } else if (policyManager.IsServiceAccount(user.username)) {
                category = "service_accounts";
            } else {
                category = "regular_users";
            }
            categorized_users[category].push_back(user);
        }

        userAccountInfo["users_by_category"] = categorized_users;
        userAccountInfo["all_users"] = users;

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"用户账户扫描完成", 100);
        }

        // 添加任务状态信息
        userAccountInfo["status"] = "success";
        userAccountInfo["task_id"] = taskId;
        userAccountInfo["message"] = u8"用户账户信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            userAccountInfo["request_params"] = params;
        }

        return userAccountInfo.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"用户账户扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}

// 封装的密码策略信息获取接口实现
std::string PasswordPolicyManager::Init_PasswordPolicyInfoMsg(
    const std::string& params,
    void(*progressCallback)(const std::string&, int),
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
) {
    try {
        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始初始化
        if (progressCallback) {
            progressCallback(u8"正在初始化密码策略管理器...", 10);
        }

        // 创建密码策略管理器实例
        PasswordPolicyManager policyManager;
        if (!policyManager.Initialize()) {
            nlohmann::json errorResult = {
                {"status", "error"},
                {"message", u8"初始化密码策略管理器失败。可能需要管理员权限。"},
                {"task_id", taskId},
                {"error_code", "INIT_FAILED"}
            };
            return errorResult.dump();
        }

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：开始扫描
        if (progressCallback) {
            progressCallback(u8"正在扫描密码策略...", 50);
        }

        // 获取密码策略信息
        PasswordPolicyData policy = policyManager.GetPasswordPolicy();

        // 构建密码策略JSON结果
        nlohmann::json policyInfo;
        policyInfo["metadata"] = {
            {"tool_name", "Windows Password Policy Scanner"},
            {"version", "1.0.0"},
            {"scan_time", policyManager.GetCurrentTimestamp()},
            {"policy_source", policy.policy_source}
        };

        // 核心密码策略信息
        policyInfo["password_policy"] = {
            {"min_password_length", policy.min_password_length},
            {"min_password_age_days", policy.min_password_age_days},
            {"max_password_age_days", policy.max_password_age_days},
            {"password_history_count", policy.password_history_count},
            {"complexity_enabled", policy.complexity_enabled},
            {"reversible_encryption", policy.reversible_encryption},
            {"lockout_duration_minutes", policy.lockout_duration_minutes},
            {"lockout_observation_window_minutes", policy.lockout_observation_window_minutes},
            {"lockout_threshold", policy.lockout_threshold}
        };

        // 获取Guest账户状态
        bool guestEnabled = false;
        std::vector<UserAccountData> users = policyManager.GetAllUserAccounts();
        for (const auto& user : users) {
            if (user.username == "Guest") {
                guestEnabled = user.enabled;
                break;
            }
        }

        // 获取自动登录设置（从注册表）
        std::string autoLoginUser = "";
        try {
            autoLoginUser = policyManager.ReadRegistryString(
                HKEY_LOCAL_MACHINE,
                "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon",
                "DefaultUserName"
            );
        } catch (...) {
            autoLoginUser = "";
        }
        bool autoLoginEnabled = !autoLoginUser.empty();

        // 系统设置
        policyInfo["system_settings"] = {
            {"guest_account_enabled", guestEnabled},
            {"auto_login_enabled", autoLoginEnabled},
            {"auto_login_user", autoLoginUser}
        };

        // 检查任务是否被取消
        if (queryTaskControlCb && queryTaskControlCb(taskId)) {
            nlohmann::json errorResult = {
                {"status", "cancelled"},
                {"message", "Task was cancelled"},
                {"task_id", taskId}
            };
            return errorResult.dump();
        }

        // 报告进度：完成
        if (progressCallback) {
            progressCallback(u8"密码策略扫描完成", 100);
        }

        // 添加任务状态信息
        policyInfo["status"] = "success";
        policyInfo["task_id"] = taskId;
        policyInfo["message"] = u8"密码策略信息获取成功";

        // 如果有参数，可以在这里处理参数逻辑
        if (!params.empty()) {
            policyInfo["request_params"] = params;
        }

        return policyInfo.dump(4);

    } catch (const std::exception& e) {
        // 异常处理
        nlohmann::json errorResult = {
            {"status", "error"},
            {"message", std::string(u8"发生异常: ") + e.what()},
            {"task_id", taskId},
            {"error_code", "EXCEPTION"}
        };

        if (progressCallback) {
            progressCallback(u8"密码策略扫描过程中发生错误", -1);
        }

        return errorResult.dump();
    }
}



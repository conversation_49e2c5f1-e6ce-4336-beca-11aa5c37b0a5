#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <chrono>

// 进度回调函数 - 用于监控是否使用了新方法
void newMethodProgressCallback(const std::string& message, int progress) {
    std::cout << "[New Method Test " << progress << "%] " << message << std::endl;
    
    // 检查进度消息中是否包含新方法的特征
    if (message.find("Scanning drive") != std::string::npos) {
        std::cout << "  → Using new wide character scanning method" << std::endl;
    }
}

// 任务控制回调函数
bool newMethodQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 直接测试新的宽字符扫描方法
void testDirectWideCharScan() {
    std::cout << "=== Direct Wide Character Scan Test ===" << std::endl;
    
    ExifExtractor extractor;
    if (!extractor.Initialize()) {
        std::cout << "Failed to initialize EXIF extractor" << std::endl;
        return;
    }
    
    // 直接测试新的宽字符方法
    std::wstring testDir = L"C:\\Users\\<USER>\\Pictures\\Screenshots";
    std::vector<std::wstring> wideResults;
    
    std::cout << "Testing ScanDirectoryW directly..." << std::endl;
    auto startTime = std::chrono::high_resolution_clock::now();
    
    extractor.ScanDirectoryW(testDir, wideResults);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    std::cout << "Direct wide scan: " << duration.count() << " ms" << std::endl;
    std::cout << "Found " << wideResults.size() << " files" << std::endl;
    
    // 显示前5个结果
    std::cout << "First 5 files:" << std::endl;
    for (size_t i = 0; i < wideResults.size() && i < 5; i++) {
        // 转换为UTF-8显示
        int pathLength = WideCharToMultiByte(CP_UTF8, 0, wideResults[i].c_str(), -1, NULL, 0, NULL, NULL);
        if (pathLength > 0) {
            std::vector<char> pathBuffer(pathLength);
            int result = WideCharToMultiByte(CP_UTF8, 0, wideResults[i].c_str(), -1, &pathBuffer[0], pathLength, NULL, NULL);
            if (result > 0) {
                std::string utf8Path(&pathBuffer[0]);
                std::cout << "  " << (i + 1) << ". " << utf8Path << std::endl;
            }
        }
    }
    
    extractor.Cleanup();
}

// 测试图片文件检测
void testImageFileDetection() {
    std::cout << "\n=== Image File Detection Test ===" << std::endl;
    
    ExifExtractor extractor;
    
    std::vector<std::wstring> testFiles = {
        L"test.jpg",
        L"test.JPG",
        L"test.png",
        L"test.PNG",
        L"屏幕截图.png",
        L"photo.jpeg",
        L"image.bmp",
        L"document.txt",
        L"video.mp4"
    };
    
    for (const auto& file : testFiles) {
        bool isImage = extractor.IsImageFileW(file);
        
        // 转换文件名为UTF-8显示
        int nameLength = WideCharToMultiByte(CP_UTF8, 0, file.c_str(), -1, NULL, 0, NULL, NULL);
        if (nameLength > 0) {
            std::vector<char> nameBuffer(nameLength);
            int result = WideCharToMultiByte(CP_UTF8, 0, file.c_str(), -1, &nameBuffer[0], nameLength, NULL, NULL);
            if (result > 0) {
                std::string utf8Name(&nameBuffer[0]);
                std::cout << "  " << utf8Name << " → " << (isImage ? "✓ Image" : "✗ Not image") << std::endl;
            }
        }
    }
}

int main() {
    std::cout << "=== EXIF Manager New Method Usage Test ===" << std::endl;
    
    // 直接测试新的宽字符方法
    testDirectWideCharScan();
    
    // 测试图片文件检测
    testImageFileDetection();
    
    std::string taskId = "new_method_test_001";
    
    std::cout << "\n=== Testing Full EXIF Analysis (Should Use New Methods) ===" << std::endl;
    std::cout << "Monitoring progress messages to verify new method usage..." << std::endl;
    
    try {
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // 调用完整的EXIF分析 - 应该使用新的方法
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            newMethodProgressCallback,
            taskId,
            newMethodQueryTaskControl
        );
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
        
        std::cout << "\n=== New Method Test Results ===" << std::endl;
        std::cout << "Total analysis time: " << duration.count() << " seconds" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 解析结果验证新方法效果
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            if (jsonResult.find("status") != jsonResult.end()) {
                std::cout << "Status: " << jsonResult["status"] << std::endl;
            }
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            if (jsonResult.find("statistics") != jsonResult.end()) {
                auto stats = jsonResult["statistics"];
                if (stats.find("successful_extractions") != stats.end()) {
                    std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                }
            }
            
            // 检查中文文件处理效果
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                int chineseFiles = 0;
                int pathIssues = 0;
                int successfulChineseFiles = 0;
                
                for (const auto& image : imageAnalysis) {
                    if (image.find("file_path") != image.end()) {
                        std::string filePath = image["file_path"];
                        
                        // 检查中文文件
                        bool hasChineseChars = false;
                        for (unsigned char c : filePath) {
                            if (c >= 128) {
                                hasChineseChars = true;
                                break;
                            }
                        }
                        
                        if (hasChineseChars) {
                            chineseFiles++;
                            if (image.find("success") != image.end() && image["success"].get<bool>()) {
                                successfulChineseFiles++;
                            }
                        }
                        
                        // 检查路径问题
                        if (filePath.find("????????") != std::string::npos) {
                            pathIssues++;
                        }
                    }
                }
                
                std::cout << "\nNew Method Effectiveness:" << std::endl;
                std::cout << "  Chinese files found: " << chineseFiles << std::endl;
                std::cout << "  Successfully processed Chinese files: " << successfulChineseFiles << std::endl;
                std::cout << "  Path encoding issues: " << pathIssues << std::endl;
                
                if (pathIssues == 0 && chineseFiles > 0) {
                    std::cout << "✓ New method working perfectly - no path issues!" << std::endl;
                } else if (pathIssues > 0) {
                    std::cout << "⚠ Still some path issues detected" << std::endl;
                } else {
                    std::cout << "ℹ No Chinese files found to test" << std::endl;
                }
                
                if (chineseFiles > 0) {
                    double successRate = (double)successfulChineseFiles / chineseFiles * 100.0;
                    std::cout << "Chinese file success rate: " << std::fixed << std::setprecision(1) << successRate << "%" << std::endl;
                }
            }
            
            // 检查文件保存
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "✓ Results saved to: " << outputFile << std::endl;
                }
            }
            
            std::cout << "\n✓ New method test completed!" << std::endl;
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

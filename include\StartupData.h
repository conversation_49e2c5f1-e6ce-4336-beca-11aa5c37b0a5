﻿#pragma once
#include <string>
#include <vector>
#include <ctime>
#include <nlohmann/json.hpp>

// 启动项数据结构
struct StartupItemData {
    std::string name;                   // 启动项名称
    std::string command;                // 启动命令/路径
    std::string location;               // 启动位置类型
    std::string registry_key;           // 注册表键路径
    std::string value_name;             // 注册表值名称
    std::string file_path;              // 文件路径
    std::string description;            // 描述信息
    std::string company;                // 公司名称
    std::string version;                // 版本信息
    bool enabled;                       // 是否启用
    bool is_system;                     // 是否系统启动项
    bool file_exists;                   // 文件是否存在
    std::string file_size;              // 文件大小
    std::string creation_time;          // 创建时间
    std::string modification_time;      // 修改时间
    std::string startup_type;           // 启动类型 (Registry/Folder/Task/Service)
    
    StartupItemData() : enabled(true), is_system(false), file_exists(false) {}
};

// 启动项统计信息
struct StartupStatistics {
    int total_startup_items;            // 总启动项数
    int enabled_items;                  // 启用的启动项数
    int disabled_items;                 // 禁用的启动项数
    int system_items;                   // 系统启动项数
    int user_items;                     // 用户启动项数
    int registry_items;                 // 注册表启动项数
    int folder_items;                   // 启动文件夹项数
    int task_items;                     // 计划任务项数
    int service_items;                  // 服务项数
    int missing_files;                  // 文件缺失的项数
    std::vector<std::string> startup_locations; // 启动位置列表
    std::time_t scan_time;              // 扫描时间
    std::string scan_duration;          // 扫描耗时
    
    StartupStatistics() : total_startup_items(0), enabled_items(0), disabled_items(0),
                         system_items(0), user_items(0), registry_items(0),
                         folder_items(0), task_items(0), service_items(0),
                         missing_files(0), scan_time(0) {}
};

// 启动项分类数据
struct StartupCategoryData {
    std::string category_name;          // 分类名称
    std::string category_description;   // 分类描述
    std::vector<StartupItemData> items; // 该分类下的启动项
    int item_count;                     // 项目数量
    
    StartupCategoryData() : item_count(0) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(StartupItemData,
    name, command, location, registry_key, value_name, file_path,
    description, company, version, enabled, is_system, file_exists,
    file_size, creation_time, modification_time, startup_type)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(StartupStatistics,
    total_startup_items, enabled_items, disabled_items, system_items,
    user_items, registry_items, folder_items, task_items, service_items,
    missing_files, startup_locations, scan_time, scan_duration)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(StartupCategoryData,
    category_name, category_description, items, item_count)

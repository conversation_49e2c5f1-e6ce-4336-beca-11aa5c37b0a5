﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// 密码策略数据结构
struct PasswordPolicyData {
    // 基本密码策略
    int min_password_length;            // 最小密码长度
    int max_password_age_days;          // 密码最长使用期限(天)
    int min_password_age_days;          // 密码最短使用期限(天)
    int password_history_count;         // 强制密码历史记录
    bool complexity_enabled;            // 密码必须符合复杂性要求
    bool reversible_encryption;         // 用可还原的加密来存储密码
    
    // 账户锁定策略
    int lockout_threshold;              // 账户锁定阈值
    int lockout_duration_minutes;       // 账户锁定时间(分钟)
    int lockout_observation_window_minutes; // 重置账户锁定计数器(分钟)
    
    // 高级密码策略
    bool clear_text_password;           // 明文密码存储
    bool require_logon_to_change_password; // 下次登录时须更改密码
    bool smart_card_required;           // 登录需要智能卡
    bool account_disabled;              // 账户已禁用
    bool account_locked_out;            // 账户已锁定
    bool password_never_expires;        // 密码永不过期
    bool user_cannot_change_password;   // 用户不能更改密码
    
    // Kerberos策略
    int max_ticket_age_hours;           // Kerberos票证最长生存期(小时)
    int max_service_age_minutes;        // 服务票证最长生存期(分钟)
    int max_renew_age_days;             // 票证续订最长生存期(天)
    int max_clock_skew_minutes;         // 最大时钟偏差容差(分钟)
    bool enforce_user_logon_restrictions; // 强制用户登录限制
    
    // 审核策略
    bool audit_account_logon;           // 审核账户登录事件
    bool audit_account_management;      // 审核账户管理
    bool audit_logon_events;            // 审核登录事件
    bool audit_object_access;           // 审核对象访问
    bool audit_policy_change;           // 审核策略更改
    bool audit_privilege_use;           // 审核特权使用
    bool audit_process_tracking;        // 审核进程跟踪
    bool audit_system_events;           // 审核系统事件
    
    // 用户权限分配
    std::vector<std::string> logon_as_service;      // 作为服务登录
    std::vector<std::string> logon_as_batch;        // 作为批处理作业登录
    std::vector<std::string> logon_interactively;   // 本地登录
    std::vector<std::string> logon_through_terminal; // 通过终端服务登录
    std::vector<std::string> deny_logon_locally;    // 拒绝本地登录
    std::vector<std::string> deny_logon_as_service; // 拒绝作为服务登录
    std::vector<std::string> deny_logon_as_batch;   // 拒绝作为批处理作业登录
    std::vector<std::string> deny_logon_through_terminal; // 拒绝通过终端服务登录
    
    // 安全选项
    bool anonymous_sid_translation;     // 允许匿名SID/名称转换
    bool everyone_includes_anonymous;   // Everyone权限应用于匿名用户
    bool restrict_anonymous_access;     // 限制匿名访问
    int lm_compatibility_level;         // LAN Manager身份验证级别
    bool ntlm_min_client_security;      // NTLM最小客户端安全
    bool ntlm_min_server_security;      // NTLM最小服务器安全
    
    // 域策略信息
    std::string domain_name;            // 域名
    std::string domain_controller;      // 域控制器
    std::string policy_source;          // 策略来源 (Local/Domain/GPO)
    std::string last_modified;          // 最后修改时间
    std::string applied_gpo;            // 应用的组策略对象
    
    // 密码复杂性详细要求
    bool require_uppercase;             // 需要大写字母
    bool require_lowercase;             // 需要小写字母
    bool require_numbers;               // 需要数字
    bool require_symbols;               // 需要特殊字符
    bool require_unicode;               // 需要Unicode字符
    std::vector<std::string> banned_passwords; // 禁用密码列表
    
    // 多因素认证
    bool mfa_enabled;                   // 多因素认证启用
    std::vector<std::string> mfa_methods; // MFA方法
    bool biometric_enabled;             // 生物识别启用
    bool pin_enabled;                   // PIN码启用
    
    PasswordPolicyData() : min_password_length(0), max_password_age_days(0), 
                          min_password_age_days(0), password_history_count(0),
                          complexity_enabled(false), reversible_encryption(false),
                          lockout_threshold(0), lockout_duration_minutes(0),
                          lockout_observation_window_minutes(0), clear_text_password(false),
                          require_logon_to_change_password(false), smart_card_required(false),
                          account_disabled(false), account_locked_out(false),
                          password_never_expires(false), user_cannot_change_password(false),
                          max_ticket_age_hours(0), max_service_age_minutes(0),
                          max_renew_age_days(0), max_clock_skew_minutes(0),
                          enforce_user_logon_restrictions(false), audit_account_logon(false),
                          audit_account_management(false), audit_logon_events(false),
                          audit_object_access(false), audit_policy_change(false),
                          audit_privilege_use(false), audit_process_tracking(false),
                          audit_system_events(false), anonymous_sid_translation(false),
                          everyone_includes_anonymous(false), restrict_anonymous_access(false),
                          lm_compatibility_level(0), ntlm_min_client_security(false),
                          ntlm_min_server_security(false), require_uppercase(false),
                          require_lowercase(false), require_numbers(false),
                          require_symbols(false), require_unicode(false),
                          mfa_enabled(false), biometric_enabled(false), pin_enabled(false) {}
};

// 用户账户信息
struct UserAccountData {
    std::string username;               // 用户名
    std::string description;            // 描述
    std::string sid;                    // 安全标识符
    bool enabled;                       // 账户是否启用
    bool locked;                        // 账户是否锁定
    bool password_expired;              // 密码是否过期
    bool password_never_expires;        // 密码是否永不过期
    bool user_cannot_change_password;   // 用户是否不能更改密码
    bool must_change_password_next_logon; // 下次登录必须更改密码
    std::string last_logon;             // 最后登录时间
    std::string last_password_change;   // 最后密码更改时间
    std::string password_expires;       // 密码过期时间
    int logon_count;                    // 登录次数
    int bad_password_count;             // 错误密码次数
    std::vector<std::string> groups;    // 所属组
    std::vector<std::string> privileges; // 用户权限
    std::string account_type;           // 账户类型

    UserAccountData() : enabled(false), locked(false), password_expired(false),
                       password_never_expires(false), user_cannot_change_password(false),
                       must_change_password_next_logon(false), logon_count(0),
                       bad_password_count(0) {}
};

// 密码策略统计信息
struct PasswordPolicyStatistics {
    int total_users;                    // 总用户数
    int enabled_users;                  // 启用用户数
    int disabled_users;                 // 禁用用户数
    int locked_users;                   // 锁定用户数
    int password_expired_users;         // 密码过期用户数
    int password_never_expires_users;   // 密码永不过期用户数
    int admin_users;                    // 管理员用户数
    int guest_users;                    // 来宾用户数
    int service_accounts;               // 服务账户数
    double password_policy_compliance;  // 密码策略合规率
    std::string policy_strength;        // 策略强度评级
    std::vector<std::string> security_recommendations; // 安全建议
    std::time_t scan_time;              // 扫描时间
    std::string scan_duration;          // 扫描耗时

    PasswordPolicyStatistics() : total_users(0), enabled_users(0), disabled_users(0),
                                locked_users(0), password_expired_users(0),
                                password_never_expires_users(0), admin_users(0),
                                guest_users(0), service_accounts(0),
                                password_policy_compliance(0.0), scan_time(0) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PasswordPolicyData,
    min_password_length, max_password_age_days, min_password_age_days,
    password_history_count, complexity_enabled, reversible_encryption,
    lockout_threshold, lockout_duration_minutes, lockout_observation_window_minutes,
    clear_text_password, require_logon_to_change_password, smart_card_required,
    account_disabled, account_locked_out, password_never_expires,
    user_cannot_change_password, max_ticket_age_hours, max_service_age_minutes,
    max_renew_age_days, max_clock_skew_minutes, enforce_user_logon_restrictions,
    audit_account_logon, audit_account_management, audit_logon_events,
    audit_object_access, audit_policy_change, audit_privilege_use,
    audit_process_tracking, audit_system_events, logon_as_service,
    logon_as_batch, logon_interactively, logon_through_terminal,
    deny_logon_locally, deny_logon_as_service, deny_logon_as_batch,
    deny_logon_through_terminal, anonymous_sid_translation,
    everyone_includes_anonymous, restrict_anonymous_access, lm_compatibility_level,
    ntlm_min_client_security, ntlm_min_server_security, domain_name,
    domain_controller, policy_source, last_modified, applied_gpo,
    require_uppercase, require_lowercase, require_numbers, require_symbols,
    require_unicode, banned_passwords, mfa_enabled, mfa_methods,
    biometric_enabled, pin_enabled)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(UserAccountData,
    username, description, sid, enabled, locked,
    password_expired, password_never_expires, user_cannot_change_password,
    must_change_password_next_logon, last_logon, last_password_change,
    password_expires, logon_count, bad_password_count, groups,
    privileges, account_type)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PasswordPolicyStatistics,
    total_users, enabled_users, disabled_users, locked_users,
    password_expired_users, password_never_expires_users, admin_users,
    guest_users, service_accounts, password_policy_compliance,
    policy_strength, security_recommendations, scan_time, scan_duration)

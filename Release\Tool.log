﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  The vcpkg manifest was disabled, but we found a manifest file in E:\VsProject\Tool\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
  Tool.cpp
e:\vsproject\tool\tool.cpp(318): warning C4566: 由通用字符名称“\u2713”表示的字符不能在当前代码页(936)中表示出来
e:\vsproject\tool\tool.cpp(320): warning C4566: 由通用字符名称“\u26A0”表示的字符不能在当前代码页(936)中表示出来
e:\vsproject\tool\tool.cpp(604): warning C4566: 由通用字符名称“\u2713”表示的字符不能在当前代码页(936)中表示出来
e:\vsproject\tool\tool.cpp(606): warning C4566: 由通用字符名称“\u26A0”表示的字符不能在当前代码页(936)中表示出来
  ExifManager.cpp
e:\vsproject\tool\src\exifmanager.cpp(214): warning C4101: “e”: 未引用的局部变量
e:\vsproject\tool\src\exifmanager.cpp(899): warning C4101: “e”: 未引用的局部变量
  正在生成代码
  0 of 8833 functions ( 0.0%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    0 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  Tool.vcxproj -> E:\VsProject\Tool\Release\Tool.exe
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。

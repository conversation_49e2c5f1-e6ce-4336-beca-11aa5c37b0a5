﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  The vcpkg manifest was disabled, but we found a manifest file in E:\VsProject\Tool\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
  ExifManager.cpp
e:\vsproject\tool\src\exifmanager.cpp(214): warning C4101: “e”: 未引用的局部变量
e:\vsproject\tool\src\exifmanager.cpp(918): warning C4101: “e”: 未引用的局部变量
  正在生成代码
  2 of 8842 functions (<0.1%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    3 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  Tool.vcxproj -> E:\VsProject\Tool\Release\Tool.exe
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。

﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// Windows屏保数据结构
struct ScreensaverData {
    std::string name;                   // 屏保名称
    std::string display_name;           // 显示名称
    std::string file_path;              // 屏保文件路径
    std::string description;            // 屏保描述
    std::string version;                // 版本信息
    std::string manufacturer;           // 制造商
    std::string file_size;              // 文件大小
    std::string creation_time;          // 创建时间
    std::string modification_time;      // 修改时间
    bool is_active;                     // 是否为当前活动屏保
    bool is_enabled;                    // 屏保是否启用
    bool password_protected;            // 是否需要密码
    std::string registry_key;           // 注册表键值
    std::string command_line;           // 命令行参数
    bool is_system_screensaver;         // 是否为系统屏保
    std::string category;               // 屏保分类 (System, Third-party, Custom)

    // 扩展信息
    bool has_configuration;             // 是否有配置对话框
    bool can_preview;                   // 是否支持预览
    bool is_running;                    // 是否正在运行
    std::string architecture;           // 架构 (x86, x64, ARM)
    std::string dependencies;           // 依赖项
    std::string performance_info;       // 性能信息
    bool is_trusted;                    // 是否受信任
    DWORD usage_count;                  // 使用次数
    std::string last_activation;        // 最后激活时间
    std::string compatibility_info;     // 兼容性信息
    bool is_compatible;                 // 是否兼容当前系统

    ScreensaverData() : is_active(false), is_enabled(false),
                       password_protected(false), is_system_screensaver(false),
                       has_configuration(false), can_preview(false), is_running(false),
                       is_trusted(false), usage_count(0), is_compatible(true) {}
};

// 屏保统计信息
struct ScreensaverStatistics {
    int total_screensavers;             // 总屏保数量
    int system_screensavers;            // 系统屏保数量
    int third_party_screensavers;       // 第三方屏保数量
    bool screensaver_enabled;           // 屏保功能是否启用
    bool password_protected;            // 是否需要密码
    std::time_t scan_time;              // 扫描时间
    std::string scan_duration;          // 扫描耗时

    ScreensaverStatistics() : total_screensavers(0), system_screensavers(0),
                             third_party_screensavers(0), screensaver_enabled(false),
                             password_protected(false), scan_time(0) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ScreensaverData,
    name, display_name, file_path, description, version,
    manufacturer, file_size, creation_time, modification_time,
    is_active, is_enabled, password_protected,
    registry_key, command_line,
    is_system_screensaver, category,
    has_configuration, can_preview, is_running, architecture,
    dependencies, performance_info,
    is_trusted, usage_count, last_activation,
    compatibility_info, is_compatible)

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ScreensaverStatistics,
    total_screensavers, system_screensavers, third_party_screensavers,
    screensaver_enabled, password_protected,
    scan_time, scan_duration)

#include <iostream>
#include <string>
#include "include/nlohmann/json.hpp"

int main() {
    std::cout << "=== JSON路径转义验证测试 ===" << std::endl;
    
    // 原始Windows路径
    std::string originalPath = "D:\\WPS Office\\12.1.0.21915\\office6\\systemshareext\\Assets\\Square44x44Logo.png";
    
    std::cout << "原始路径: " << originalPath << std::endl;
    
    // 创建JSON对象
    nlohmann::json testJson;
    testJson["file_path"] = originalPath;
    
    // 序列化为JSON字符串
    std::string jsonString = testJson.dump(4);
    std::cout << "\nJSON序列化结果:" << std::endl;
    std::cout << jsonString << std::endl;
    
    // 解析JSON字符串
    nlohmann::json parsedJson = nlohmann::json::parse(jsonString);
    std::string recoveredPath = parsedJson["file_path"];
    
    std::cout << "\n解析后的路径: " << recoveredPath << std::endl;
    
    // 验证路径是否一致
    if (originalPath == recoveredPath) {
        std::cout << "✓ 路径转义和解析正确！" << std::endl;
    } else {
        std::cout << "✗ 路径转义或解析有问题！" << std::endl;
    }
    
    std::cout << "\n=== 说明 ===" << std::endl;
    std::cout << "1. JSON中的 \\\\\\\\ 是正常的转义格式" << std::endl;
    std::cout << "2. 这确保了JSON格式的正确性" << std::endl;
    std::cout << "3. 解析时会自动转换回原始路径" << std::endl;
    std::cout << "4. 这不是bug，而是JSON标准的要求" << std::endl;
    
    return 0;
}

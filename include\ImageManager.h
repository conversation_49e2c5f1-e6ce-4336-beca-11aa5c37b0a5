﻿#pragma once

#include <windows.h>
#include <gdiplus.h>
#include <string>
#include <vector>
#include <functional>
#include <cstddef>
#include <nlohmann/json.hpp>

#pragma comment(lib, "gdiplus.lib")

// 任务控制回调函数类型定义
typedef std::function<bool(const std::string&)> QueryTaskControlCallback;

// 图片信息结构体
struct ImageInfo {
    std::string filename;           // 文件名
    std::string original_format;    // 原始格式
    std::string file_path;          // 文件路径
    int width;                      // 图片宽度
    int height;                     // 图片高度
    size_t file_size;              // 文件大小（字节）
    std::string base64_data;        // Base64编码的位图数据
    bool conversion_success;        // 转换是否成功
    std::string error_message;      // 错误信息
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ImageInfo,
    filename, original_format, file_path, width, height,
    file_size, base64_data, conversion_success, error_message)

class ImageManager {
public:
    ImageManager();
    ~ImageManager();

    // 初始化和清理
    bool Initialize();
    void Cleanup();

    // 主要功能接口
    static std::string Init_ImageConversionMsg(
        const std::string& params,
        void(*progressCallback)(const std::string&, int),
        const std::string& taskId,
        QueryTaskControlCallback queryTaskControlCb
    );

    // 图片转换功能
    bool ConvertImageToBitmap(const std::string& imagePath, ImageInfo& imageInfo);
    std::string ConvertBitmapToBase64(HBITMAP hBitmap);
    std::vector<ImageInfo> ConvertImagesInDirectory(const std::string& directoryPath);
    
    // 支持的图片格式检查
    bool IsSupportedImageFormat(const std::string& filePath);
    std::string GetImageFormat(const std::string& filePath);
    
    // 辅助方法
    std::string GetCurrentTimestamp();
    size_t GetFileSize(const std::string& filePath);
    std::vector<std::string> FindImageFiles(const std::string& directoryPath);

    // JSON文件保存方法
    bool SaveResultToJsonFile(const std::string& jsonContent, const std::string& outputPath = "");
    std::string GenerateOutputFilename(const std::string& inputPath, const std::string& suffix = "_conversion_result");

    // 文件系统辅助方法
    bool FileExists(const std::string& filePath);
    bool DirectoryExists(const std::string& directoryPath);
    std::string GetFilenameFromPath(const std::string& filePath);
    std::string GetFileExtension(const std::string& filePath);

private:
    bool m_initialized;
    ULONG_PTR m_gdiplusToken;
    
    // GDI+ 辅助方法
    int GetEncoderClsid(const WCHAR* format, CLSID* pClsid);
    std::wstring StringToWString(const std::string& str);
    std::string WStringToString(const std::wstring& wstr);
    
    // 位图处理
    HBITMAP CreateBitmapFromGdiplusBitmap(Gdiplus::Bitmap* gdiplusBitmap);
    std::string EncodeBitmapToBase64(HBITMAP hBitmap);
    std::string EncodeToBase64(const std::vector<BYTE>& data);

    // 支持的格式列表
    std::vector<std::string> GetSupportedFormats();
};

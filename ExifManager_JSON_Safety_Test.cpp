#include "include/ExifManager.h"
#include <iostream>
#include <string>

// 简单的进度回调函数
void jsonSafetyProgressCallback(const std::string& message, int progress) {
    std::cout << "[JSON Safety Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool jsonSafetyQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 测试字符串清理功能
void testStringCleaning() {
    std::cout << "=== Testing String Cleaning Functions ===" << std::endl;
    
    // 测试各种问题字符串
    std::vector<std::string> testStrings = {
        "Normal ASCII text",
        "Text with \"quotes\" and \\backslashes",
        "Text with\nnewlines\tand\ttabs",
        "Text with null\0character",
        "Mixed中文English",
        "Special chars: àáâãäåæçèéêë",
        "Control chars: \x01\x02\x03\x04",
        "High bytes: \xC1\xC2\xF0\xF1"
    };
    
    for (const auto& testStr : testStrings) {
        std::cout << "\nOriginal: [" << testStr.length() << " bytes] ";
        for (unsigned char c : testStr) {
            if (c >= 32 && c < 127) {
                std::cout << c;
            } else {
                std::cout << "\\x" << std::hex << (int)c << std::dec;
            }
        }
        std::cout << std::endl;
        
        // 这里我们需要访问内部的清理函数，但由于它们是内部函数，
        // 我们只能通过实际的EXIF调用来测试
        std::cout << "Testing through EXIF manager..." << std::endl;
    }
}

int main() {
    std::cout << "=== EXIF Manager JSON Safety Test ===" << std::endl;
    
    // 首先测试字符串清理
    testStringCleaning();
    
    std::string taskId = "json_safety_test_001";
    
    std::cout << "\n=== Starting JSON Safety EXIF Analysis ===" << std::endl;
    std::cout << "This test focuses on JSON encoding safety..." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            jsonSafetyProgressCallback,
            taskId,
            jsonSafetyQueryTaskControl
        );
        
        std::cout << "\n=== JSON Safety Test Results ===" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 检查结果中是否包含问题字符
        bool hasInvalidChars = false;
        for (size_t i = 0; i < result.length(); ++i) {
            unsigned char c = static_cast<unsigned char>(result[i]);
            if (c < 32 && c != '\n' && c != '\r' && c != '\t') {
                std::cout << "Warning: Found control character at position " << i << ": 0x" 
                          << std::hex << (int)c << std::dec << std::endl;
                hasInvalidChars = true;
            } else if (c >= 127) {
                std::cout << "Warning: Found high-bit character at position " << i << ": 0x" 
                          << std::hex << (int)c << std::dec << std::endl;
                hasInvalidChars = true;
            }
        }
        
        if (!hasInvalidChars) {
            std::cout << "✓ No invalid characters found in JSON output" << std::endl;
        }
        
        // 尝试解析JSON
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            std::cout << "✓ JSON parsing successful!" << std::endl;
            
            // 检查状态
            if (jsonResult.find("status") != jsonResult.end()) {
                std::string status = jsonResult["status"];
                std::cout << "Status: " << status << std::endl;
                
                if (status == "error") {
                    if (jsonResult.find("error_code") != jsonResult.end()) {
                        std::cout << "Error code: " << jsonResult["error_code"] << std::endl;
                    }
                    if (jsonResult.find("error_message") != jsonResult.end()) {
                        std::cout << "Error message: " << jsonResult["error_message"] << std::endl;
                    }
                } else {
                    // 成功情况下的统计
                    if (jsonResult.find("total_images_found") != jsonResult.end()) {
                        std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
                    }
                    
                    if (jsonResult.find("statistics") != jsonResult.end()) {
                        auto stats = jsonResult["statistics"];
                        if (stats.find("successful_extractions") != stats.end()) {
                            std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                        }
                    }
                    
                    // 检查是否有文件路径问题
                    if (jsonResult.find("image_analysis") != jsonResult.end()) {
                        auto imageAnalysis = jsonResult["image_analysis"];
                        int pathIssues = 0;
                        
                        for (const auto& image : imageAnalysis) {
                            if (image.find("file_path") != image.end()) {
                                std::string filePath = image["file_path"];
                                if (filePath.find("??") != std::string::npos) {
                                    pathIssues++;
                                }
                            }
                        }
                        
                        if (pathIssues > 0) {
                            std::cout << "⚠ Found " << pathIssues << " files with path encoding issues" << std::endl;
                        } else {
                            std::cout << "✓ All file paths encoded correctly" << std::endl;
                        }
                    }
                }
            }
            
            std::cout << "\n✓ JSON safety test PASSED!" << std::endl;
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "✗ JSON parse error: " << e.what() << std::endl;
            std::cout << "Error ID: " << e.id << std::endl;
            
            // 显示问题区域
            std::cout << "First 200 chars of result:" << std::endl;
            std::cout << result.substr(0, 200) << std::endl;
            
            std::cout << "\n✗ JSON safety test FAILED!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

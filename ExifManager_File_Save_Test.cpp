#include "include/ExifManager.h"
#include <iostream>
#include <string>
#include <fstream>

// 简单的进度回调函数
void fileSaveProgressCallback(const std::string& message, int progress) {
    std::cout << "[File Save Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool fileSaveQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 检查文件是否存在
bool FileExists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

// 获取文件大小
long GetFileSize(const std::string& filename) {
    std::ifstream file(filename, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
        return -1;
    }
    return static_cast<long>(file.tellg());
}

// 读取文件内容的前几行
std::string ReadFilePreview(const std::string& filename, int maxLines = 10) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return "Failed to open file";
    }
    
    std::string preview;
    std::string line;
    int lineCount = 0;
    
    while (std::getline(file, line) && lineCount < maxLines) {
        preview += line + "\n";
        lineCount++;
    }
    
    if (lineCount == maxLines) {
        preview += "... (truncated)\n";
    }
    
    return preview;
}

int main() {
    std::cout << "=== EXIF Manager File Save Test ===" << std::endl;
    
    std::string taskId = "file_save_test_001";
    
    std::cout << "Testing EXIF analysis with JSON file saving..." << std::endl;
    std::cout << "This will scan for images and save results to a JSON file." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            fileSaveProgressCallback,
            taskId,
            fileSaveQueryTaskControl
        );
        
        std::cout << "\n=== File Save Test Results ===" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 解析结果检查文件保存信息
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            std::cout << "\n=== Analysis Summary ===" << std::endl;
            if (jsonResult.find("status") != jsonResult.end()) {
                std::cout << "Status: " << jsonResult["status"] << std::endl;
            }
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            if (jsonResult.find("statistics") != jsonResult.end()) {
                auto stats = jsonResult["statistics"];
                if (stats.find("successful_extractions") != stats.end()) {
                    std::cout << "Successful extractions: " << stats["successful_extractions"] << std::endl;
                }
            }
            
            // 检查文件保存信息
            std::cout << "\n=== File Save Information ===" << std::endl;
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                std::cout << "Output file: " << outputFile << std::endl;
                
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "✓ File saved successfully!" << std::endl;
                    
                    // 验证文件是否真的存在
                    if (FileExists(outputFile)) {
                        std::cout << "✓ File exists on disk" << std::endl;
                        
                        long fileSize = GetFileSize(outputFile);
                        if (fileSize > 0) {
                            std::cout << "✓ File size: " << fileSize << " bytes" << std::endl;
                            
                            // 显示文件内容预览
                            std::cout << "\n=== File Content Preview ===" << std::endl;
                            std::string preview = ReadFilePreview(outputFile, 15);
                            std::cout << preview << std::endl;
                            
                            std::cout << "✓ File save test PASSED!" << std::endl;
                        } else {
                            std::cout << "✗ File is empty" << std::endl;
                        }
                    } else {
                        std::cout << "✗ File does not exist on disk" << std::endl;
                    }
                } else {
                    std::cout << "✗ File save failed" << std::endl;
                    
                    if (jsonResult.find("save_error") != jsonResult.end()) {
                        std::cout << "Error: " << jsonResult["save_error"] << std::endl;
                    }
                }
            } else {
                std::cout << "⚠ No file save information found in result" << std::endl;
            }
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
            std::cout << "First 300 chars of result:" << std::endl;
            std::cout << result.substr(0, 300) << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

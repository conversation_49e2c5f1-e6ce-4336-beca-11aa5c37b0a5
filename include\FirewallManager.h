﻿#pragma once
#include "FirewallData.h"
#include <vector>
#include <string>
#include <functional>
#include <windows.h>
#include <nlohmann/json.hpp>

// 移除WMI依赖，使用netsh和注册表组合方法

// 任务控制回调函数类型定义
typedef std::function<bool(const std::string&)> QueryTaskControlCallback;

class FirewallManager {
public:
    FirewallManager();
    ~FirewallManager();

    // 初始化防火墙管理器
    bool Initialize();

    // 清理资源
    void Cleanup();

    // 获取所有防火墙规则
    std::vector<FirewallRuleData> GetAllFirewallRules();

    // 获取防火墙配置文件信息
    std::vector<FirewallProfileData> GetFirewallProfiles();

    // 获取防火墙服务信息
    std::vector<FirewallServiceData> GetFirewallServices();

    // 获取防火墙统计信息
    FirewallStatistics GetFirewallStatistics();

    // 获取防火墙日志条目
    std::vector<FirewallLogEntry> GetFirewallLogEntries(int maxEntries = 100);

    // 获取完整的防火墙信息并返回JSON格式
    nlohmann::json GetFirewallInfoAsJson();

    // 保存防火墙信息到JSON文件
    bool SaveFirewallInfoToFile(const std::string& filename);

    // 封装的防火墙信息获取接口
    static std::string Init_FirewallInfoMsg(
        const std::string& params,
        void(*progressCallback)(const std::string&, int),
        const std::string& taskId,
        QueryTaskControlCallback queryTaskControlCb
    );

    // 静态方法：检测Windows版本和API可用性
    static bool IsWindowsXP();
    static bool IsNetshAvailable();
    static bool IsAdvancedFirewallAvailable();

private:
    bool m_initialized;
    bool m_useRegistryMethod;  // true=使用注册表方法(XP), false=使用netsh+注册表(Vista+)
    std::chrono::system_clock::time_point m_startTime;
    std::string m_lastError;

    // 辅助函数
    std::string ConvertToString(const std::wstring& wstr);
    std::wstring ConvertToWString(const std::string& str);
    nlohmann::json RuleToOptimizedJson(const FirewallRuleData& rule);

    // 根据系统版本选择合适的方法
    bool InitializeForCurrentOS();
    std::vector<FirewallRuleData> GetRulesForCurrentOS();
    std::vector<FirewallProfileData> GetProfilesForCurrentOS();

    // 防火墙规则获取（现代Windows - 无WMI依赖）
    std::vector<FirewallRuleData> GetFirewallRulesViaNetsh();
    std::vector<FirewallRuleData> GetFirewallRulesViaAdvancedRegistry();

    // Windows XP 专用方法（注册表）
    std::vector<FirewallRuleData> GetFirewallRulesViaRegistryXP();
    std::vector<FirewallProfileData> GetFirewallProfilesViaRegistryXP();
    std::vector<FirewallServiceData> GetFirewallServicesViaRegistryXP();
    FirewallStatistics GetFirewallStatisticsViaRegistryXP();

    // 通用注册表方法
    std::vector<FirewallRuleData> GetFirewallRulesViaRegistry();

    // 防火墙配置文件获取（无WMI依赖）
    std::vector<FirewallProfileData> GetProfilesViaNetsh();
    std::vector<FirewallProfileData> GetProfilesViaAdvancedRegistry();

    // 防火墙服务信息获取
    std::vector<FirewallServiceData> GetFirewallServicesInfo();
    FirewallServiceData GetServiceDetails(const std::string& serviceName);

    // 防火墙日志解析
    std::vector<FirewallLogEntry> ParseFirewallLog(const std::string& logPath, int maxEntries);
    FirewallLogEntry ParseLogLine(const std::string& line);

    // 防火墙状态检查
    bool IsFirewallEnabled();
    std::string GetFirewallStatus();
    bool IsWindowsFirewallServiceRunning();

    // 规则详细信息获取
    FirewallRuleData GetRuleDetails(const std::string& ruleName);
    std::string GetRuleDirection(int direction);
    std::string GetRuleAction(int action);
    std::string GetRuleProtocol(int protocol);
    std::string GetRuleProfiles(int profiles);

    // 配置文件详细信息
    FirewallProfileData GetProfileDetails(const std::string& profileName);
    std::string GetProfileStatus(bool enabled);
    std::string GetDefaultAction(bool block);

    // 网络接口信息
    std::vector<std::string> GetNetworkInterfaces();
    std::string GetInterfaceType(const std::string& interfaceName);

    // 应用程序和服务规则
    std::vector<FirewallRuleData> GetApplicationRules();
    std::vector<FirewallRuleData> GetServiceRules();
    std::vector<FirewallRuleData> GetPortRules();

    // 高级防火墙功能
    std::vector<std::string> GetFirewallExceptions();
    std::vector<std::string> GetTrustedApplications();
    std::vector<std::string> GetBlockedApplications();

    // 防火墙性能和监控
    std::string GetFirewallPerformanceInfo();
    int GetActiveConnectionsCount();
    std::vector<std::string> GetRecentBlocks();

    // 防火墙配置导出/导入
    bool ExportFirewallPolicy(const std::string& filePath);
    std::string GetFirewallPolicyXML();

    // 安全分析
    std::vector<std::string> AnalyzeFirewallSecurity();
    std::vector<std::string> GetPotentialSecurityIssues();
    std::vector<std::string> GetRecommendations();

    // 防火墙策略分析专用功能
    nlohmann::json AnalyzeFirewallPolicies();
    std::vector<std::string> GetHighRiskRules();
    std::vector<std::string> GetUnusedRules();
    std::vector<std::string> GetConflictingRules();
    nlohmann::json GenerateSecurityReport();
    std::string GetFirewallPolicyReadableReport();

    // 错误处理
    std::string GetLastErrorString();
    void LogError(const std::string& error);

    // 安全字符串处理
    std::string SafeExtractValue(const std::string& line, const std::string& key);

    // 数据转换（无WMI依赖）
    std::string ConvertProfileMask(int profileMask);
    std::string ConvertEdgeTraversal(int edgeTraversal);

    // netsh和注册表组合方法
    std::vector<FirewallServiceData> GetFirewallServicesViaNetsh();
    FirewallStatistics GetFirewallStatisticsViaNetsh();
    std::vector<FirewallLogEntry> GetFirewallLogEntriesViaRegistry(int maxEntries = 100);
    bool GetFirewallStatusViaNetsh();

    // 注册表操作（通用）
    std::string ReadRegistryString(HKEY hKey, const std::string& subKey, const std::string& valueName);
    DWORD ReadRegistryDWORD(HKEY hKey, const std::string& subKey, const std::string& valueName);
    bool ReadRegistryBool(HKEY hKey, const std::string& subKey, const std::string& valueName);
    std::vector<std::string> EnumerateRegistrySubKeys(HKEY hKey, const std::string& subKey);
    std::vector<std::string> EnumerateRegistryValues(HKEY hKey, const std::string& subKey);

    // Windows XP 防火墙注册表路径和操作
    std::string GetXPFirewallRegistryPath();
    bool IsXPFirewallEnabled();
    std::vector<std::string> GetXPFirewallExceptions();
    std::vector<std::string> GetXPFirewallServices();
    std::vector<std::string> GetXPFirewallPorts();
    std::string GetXPFirewallProfile();
    bool GetXPFirewallNotifications();
    bool GetXPFirewallUnicastResponses();

    // Windows XP netsh firewall 和注册表组合方法
    std::vector<FirewallRuleData> GetXPFirewallRulesViaNetsh();
    std::vector<FirewallRuleData> GetXPFirewallRulesViaRegistry();
    std::vector<FirewallRuleData> ParseXPNetshOutput(const std::string& output);

    // 现代Windows高级注册表方法
    std::vector<FirewallRuleData> GetAdvancedRegistryRules(const std::string& regPath, const std::string& profile, const std::string& direction);
    FirewallRuleData ParseAdvancedRegistryRule(const std::string& ruleData, const std::string& profile, const std::string& direction);

    // netsh输出解析
    std::vector<FirewallRuleData> ParseNetshFirewallOutput(const std::string& output);
    std::vector<FirewallRuleData> ParseGarbledNetshOutput(const std::string& output);
    FirewallProfileData ParseNetshProfileOutput(const std::string& output, const std::string& profileName);

    // 命令行工具执行
    std::string ExecuteCommand(const std::string& command);
    std::string ExecuteCommandWithTimeout(const std::string& command, DWORD timeoutMs);
    std::vector<std::string> ParseCommandOutput(const std::string& output);

    // 优化的netsh处理
    std::vector<FirewallRuleData> ParseNetshVerboseOutput(const std::string& output);
    std::vector<FirewallRuleData> GetRulesDetailsByName(const std::string& rulesList);

    // 文件和路径处理
    bool FileExists(const std::string& filePath);
    std::string GetSystemDirectory();
    std::string GetFirewallLogPath();

    // 时间和格式化
    std::string FormatTimestamp(const std::time_t& timestamp);
    std::string GetCurrentTimestamp();
};

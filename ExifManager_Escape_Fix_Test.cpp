#include "include/ExifManager.h"
#include <iostream>
#include <string>

// 简单的进度回调函数
void escapeFixProgressCallback(const std::string& message, int progress) {
    std::cout << "[Escape Fix Test " << progress << "%] " << message << std::endl;
}

// 简单的任务控制回调函数
bool escapeFixQueryTaskControl(const std::string& taskId) {
    return false; // 不取消任务
}

// 检查字符串中反斜杠的数量
int countBackslashes(const std::string& str) {
    int count = 0;
    for (char c : str) {
        if (c == '\\') count++;
    }
    return count;
}

// 检查是否有双重转义的模式
bool hasDoubleEscape(const std::string& str) {
    return str.find("\\\\\\\\") != std::string::npos; // 查找四个连续的反斜杠
}

// 分析路径格式
void analyzePath(const std::string& path) {
    std::cout << "Path: " << path << std::endl;
    std::cout << "  Length: " << path.length() << std::endl;
    std::cout << "  Backslashes: " << countBackslashes(path) << std::endl;
    std::cout << "  Has double escape: " << (hasDoubleEscape(path) ? "YES" : "NO") << std::endl;
    
    // 检查中文字符
    bool hasChineseChars = false;
    for (unsigned char c : path) {
        if (c >= 128) {
            hasChineseChars = true;
            break;
        }
    }
    std::cout << "  Has Chinese chars: " << (hasChineseChars ? "YES" : "NO") << std::endl;
    std::cout << std::endl;
}

int main() {
    std::cout << "=== EXIF Manager Escape Fix Test ===" << std::endl;
    
    std::string taskId = "escape_fix_test_001";
    
    std::cout << "Testing EXIF analysis with escape fix..." << std::endl;
    std::cout << "This will check for double escaping issues in file paths." << std::endl;
    
    try {
        // 调用EXIF信息提取接口
        std::string result = ExifManager::Init_ExifInfoMsg(
            "", // 空参数表示扫描整个设备
            escapeFixProgressCallback,
            taskId,
            escapeFixQueryTaskControl
        );
        
        std::cout << "\n=== Escape Fix Test Results ===" << std::endl;
        std::cout << "Result length: " << result.length() << " characters" << std::endl;
        
        // 解析结果检查转义问题
        try {
            nlohmann::json jsonResult = nlohmann::json::parse(result);
            
            std::cout << "\n=== Analysis Summary ===" << std::endl;
            if (jsonResult.find("status") != jsonResult.end()) {
                std::cout << "Status: " << jsonResult["status"] << std::endl;
            }
            
            if (jsonResult.find("total_images_found") != jsonResult.end()) {
                std::cout << "Total images found: " << jsonResult["total_images_found"] << std::endl;
            }
            
            // 检查路径转义问题
            std::cout << "\n=== Path Escape Analysis ===" << std::endl;
            if (jsonResult.find("image_analysis") != jsonResult.end()) {
                auto imageAnalysis = jsonResult["image_analysis"];
                int totalFiles = imageAnalysis.size();
                int doubleEscapeFiles = 0;
                int chineseFiles = 0;
                int normalPathFiles = 0;
                
                std::cout << "Analyzing first 10 file paths:" << std::endl;
                
                for (size_t i = 0; i < imageAnalysis.size() && i < 10; i++) {
                    const auto& image = imageAnalysis[i];
                    if (image.find("file_path") != image.end()) {
                        std::string filePath = image["file_path"];
                        
                        std::cout << "\n--- File " << (i + 1) << " ---" << std::endl;
                        analyzePath(filePath);
                        
                        if (hasDoubleEscape(filePath)) {
                            doubleEscapeFiles++;
                        }
                        
                        // 检查是否包含中文字符
                        bool hasChineseChars = false;
                        for (unsigned char c : filePath) {
                            if (c >= 128) {
                                hasChineseChars = true;
                                break;
                            }
                        }
                        
                        if (hasChineseChars) {
                            chineseFiles++;
                        }
                        
                        // 检查是否是正常的路径格式
                        if (!hasDoubleEscape(filePath) && filePath.find("C:\\") == 0) {
                            normalPathFiles++;
                        }
                    }
                }
                
                std::cout << "\n=== Path Format Summary ===" << std::endl;
                std::cout << "Total files analyzed: " << std::min(10, (int)imageAnalysis.size()) << std::endl;
                std::cout << "Files with double escape: " << doubleEscapeFiles << std::endl;
                std::cout << "Files with Chinese characters: " << chineseFiles << std::endl;
                std::cout << "Files with normal path format: " << normalPathFiles << std::endl;
                
                if (doubleEscapeFiles == 0) {
                    std::cout << "✓ No double escape issues found!" << std::endl;
                } else {
                    std::cout << "⚠ Found " << doubleEscapeFiles << " files with double escape issues" << std::endl;
                }
            }
            
            // 检查文件保存
            if (jsonResult.find("output_file") != jsonResult.end()) {
                std::string outputFile = jsonResult["output_file"];
                bool fileSaved = false;
                if (jsonResult.find("file_saved") != jsonResult.end()) {
                    fileSaved = jsonResult["file_saved"].get<bool>();
                }
                
                if (fileSaved) {
                    std::cout << "\n✓ Results saved to: " << outputFile << std::endl;
                } else {
                    std::cout << "\n⚠ Failed to save results to: " << outputFile << std::endl;
                }
            }
            
            std::cout << "\n✓ Escape fix test completed!" << std::endl;
            
        } catch (const nlohmann::json::exception& e) {
            std::cout << "JSON parse error: " << e.what() << std::endl;
            std::cout << "Error ID: " << e.id << std::endl;
            std::cout << "First 300 chars of result:" << std::endl;
            std::cout << result.substr(0, 300) << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "General error: " << e.what() << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

﻿#pragma once
#include <string>
#include <vector>
#include <windows.h>
#include <nlohmann/json.hpp>

// Windows进程数据结构（优化版，只包含必要字段）
struct ProcessData {
    DWORD pid;                          // 进程ID
    std::string name;                   // 进程名称
    std::string status;                 // 进程状态 (Running, Access Denied, etc.)
    std::string memory;                 // 内存使用量 (MB，真实工作集大小)
    std::string path;                   // 进程可执行文件路径
    std::string create_time;            // 进程创建时间
    std::string icon;                   // 进程图标（Base64或路径）

    ProcessData() : pid(0) {}
};

// JSON序列化支持
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ProcessData,
    pid, name, status, memory, path, create_time, icon)
